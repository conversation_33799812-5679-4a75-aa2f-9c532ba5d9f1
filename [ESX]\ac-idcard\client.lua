ESX = nil

RegisterFontFile('A9eelsh')
fontId = RegisterFontId('A9eelsh')

Citizen.CreateThread(function()
	while ESX == nil do
		ESX = exports["es_extended"]:getSharedObject()
		Citizen.Wait(500)
	end

	Citizen.Wait(5000)
	PlayerData = ESX.GetPlayerData()
end)

local open = false
local jobHaveCards = {
	'admin',
	'agent',
	'beekeeper',
	'milker',
	'police',
	'ambulance',
	'mechanic',
	'taxi',
	'miner',
	'fisherman',
	'slaughterer',
	'lumberjack',
	'fueler',
	'tailor',
	'cardealer',
	'unemployed',
}


RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job
end)

-- Open ID card
RegisterNetEvent('jsfour-idcard:open')
AddEventHandler('jsfour-idcard:open', function(data, type, ID)
	local playerIdx = GetPlayerFromServerId(ID)
	local playerPed = GetPlayerPed(playerIdx)
	ESX.UI.HUD.SetDisplay(0.0)
	local result = exports["loaf_headshot_base64"]:getBase64(playerPed)
    -- if result.success then
    --     print("^2Base64:^0",result.base64)
    -- else
    --     print("^1Error:^0",result.error)
    -- end
	if not isJobHaveCards(data.jobname) then
		data.jobname = 'idcard'
	end
	open = true
	SendNUIMessage({
		action = "open",
		array  = data,
		type   = type,
		mugshot = result.base64
	})
	Wait(6000)
	ESX.UI.HUD.SetDisplay(1.0)
end)

-- Key events
Citizen.CreateThread(function()
	while true do
		local sleep = 1500
		if open then
			sleep = 5
		if IsControlJustReleased(0, 322) and open or IsControlJustReleased(0, 177) and open then
			SendNUIMessage({
				action = "close"
			})
			open = false
			--ESX.UI.HUD.SetDisplay(1.0)
		end
	end
	Wait(sleep)
	end
end)

function isJobHaveCards(job)
	for k,v in pairs(jobHaveCards) do
		if v == job then
			return true
		end	
	end
end
