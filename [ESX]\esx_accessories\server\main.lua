
RegisterServerEvent('esx_accessories:pay')
AddEventHandler('esx_accessories:pay', function()
	local xPlayer = ESX.GetPlayerFromId(source)

	xPlayer.removeMoney(Config.Price)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid', ESX.Math.GroupDigits(Config.Price)))
end)

RegisterServerEvent('esx_accessories:save')
AddEventHandler('esx_accessories:save', function(skin, accessory)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)

	TriggerEvent('esx_datastore:getDataStore', 'user_' .. string.lower(accessory), xPlayer.identifier, function(store)
		store.set('has' .. accessory, true)

		local itemSkin = {}
		local item1 = string.lower(accessory) .. '_1'
		local item2 = string.lower(accessory) .. '_2'
		itemSkin[item1] = skin[item1]
		itemSkin[item2] = skin[item2]

		store.set('skin', itemSkin)
	end)
end)

ESX.RegisterServerCallback('esx_accessories:get', function(source, cb, accessory)
	local xPlayer = ESX.GetPlayerFromId(source)

	TriggerEvent('esx_datastore:getDataStore', 'user_' .. string.lower(accessory), xPlayer.identifier, function(store)
		local hasAccessory = (store.get('has' .. accessory) and store.get('has' .. accessory) or false)
		local skin = (store.get('skin') and store.get('skin') or {})

		cb(hasAccessory, skin)
	end)

end)

ESX.RegisterServerCallback('esx_accessories:checkMoney', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)

	cb(xPlayer.getMoney() >= Config.Price)
end)

ESX.RegisterServerCallback('wsh:esx_accessories:checkprize', function(source, cb)
	local callback = {}
	local xPlayer = ESX.GetPlayerFromId(source)
	local result = MySQL.Sync.fetchAll('SELECT type FROM tebex WHERE id = @identifier', {
		['@identifier'] = xPlayer.identifier
	})
	if result ~= nil then
		for k, v in pairs(result) do
			table.inesrt(callback, v)
		end
	end
	cb(callback)
end)

Citizen.CreateThread(function()
	MySQL.ready(function()
		MySQL.Async.fetchAll('SELECT * FROM user_licenses', {},
		function (licenses)
			local nowdate = os.time()
			for i=1, #licenses, 1 do
				if licenses[i].time ~= 0 and licenses[i].time <= os.time() then
					MySQL.Async.execute('DELETE FROM user_licenses WHERE type = @type AND owner = @owner', {
						['@type'] = licenses[i].type,
						['@owner'] = licenses[i].owner
					}, function(rowsChanged)
							print('removed license : '..licenses[i].type..' '..licenses[i].owner)
					end)
				end
			end
		end)
	end)
end)
