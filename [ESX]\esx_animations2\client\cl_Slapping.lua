local Langue = "fr"
local VolumeDeLaMusique = 0.2
local CoolDownTime = 30 -- مدة الأنتظار مابين كل كف
local TargetCoolDownTime = 20 -- كم ثانية إنتظار بعد أخذ كف من شخص
local TakeXP = 150 -- كم خبرة يخصم على الكلف الواحد

local notifIn = false
local notifOut = false

local isDead = false

if Langue == "fr" then
    Notif1 = "~r~Aucun citoyen face a vous~s~"
    Notif2 = "๐‘๐พ ~g~Vous venez de vous faire gifler~s~"
elseif Langue == "en" then
    Notif1 = "~r~No citizen in front of you~s~"
    Notif2 = "๐‘๐พ ~g~You have just been slapped~s~"
elseif Langue == "es" then
    Notif1 = "~r~Ningรบn ciudadano frente a ti~s~"
    Notif2 = "๐‘๐พ ~g~Te acaban de abofetear~s~"
end

function getPlayers()
    local playerList = {}
    for i = 0, 256 do
        local player = GetPlayerFromServerId(i)
        if NetworkIsPlayerActive(player) then
            table.insert(playerList, player)
        end
    end
    return playerList
end

function getNearPlayer()
    local players = getPlayers()
    local closestDistance = -1
    local closestPlayer = -1
    local ply = GetPlayerPed(-1)
    local plyCoords = GetEntityCoords(ply, 0)
    
    for index,value in ipairs(players) do
        local target = GetPlayerPed(value)
        if(target ~= ply) then
            local targetCoords = GetEntityCoords(GetPlayerPed(value), 0)
            local distance = Vdist(targetCoords["x"], targetCoords["y"], targetCoords["z"], plyCoords["x"], plyCoords["y"], plyCoords["z"])
            if(closestDistance == -1 or closestDistance > distance) then
                closestPlayer = value
                closestDistance = distance
            end
        end
    end
    return closestPlayer, closestDistance
end

local count = 0
	
	local function slapCoolDown(sec)
		CreateThread(function()
			count = sec
			while count ~= 0 do
				count = count - 1
				Wait(1000)
			end	
			count = 0
		end)	
	end

RegisterNetEvent('RebornProject:SyncAnimation')
AddEventHandler('RebornProject:SyncAnimation', function(playerNetId)
    local closestPlayer, distance = ESX.Game.GetClosestPlayer()
    --Wait(250)
	slapCoolDown(TargetCoolDownTime)
    Wait(50)
	if IsPedInAnyVehicle(PlayerPedId(), false) then
    elseif IsPedInAnyVehicle(PlayerPedId()) then
        ESX.ShowNotification("<font color=red>لايمكنك صفع شخص وانت داخل المركبة")
    elseif IsPedInAnyVehicle(GetPlayerPed(closestPlayer)) then
        ESX.ShowNotification("<font color=red>لايمكن صفع شخص داخل مركبه")
    elseif IsPedDeadOrDying(GetPlayerPed(closestPlayer)) then
        ESX.ShowNotification("<font color=red>لايمكن صفع لاعب مغمى عليه او ميت")
	else
		--SetPedToRagdoll(GetPlayerPed(-1), 2000, 2000, 0, 0, 0, 0) -- old
	    ChargementAnimation("melee@unarmed@streamed_variations")
        TaskPlayAnim(GetPlayerPed(-1), "melee@unarmed@streamed_variations", "victim_takedown_front_slap", 8.0, 1.0, 1500, 1, 0, 0, 0, 0)
	    ESX.ShowNotification('لـقد تـم صـفعـك')
        Wait(1200)
        SetPedToRagdoll(GetPlayerPed(-1), 1000, 1000, 0, 0, 0, 0)
	end			
end)

RegisterNetEvent("RebornProject:Notification")
AddEventHandler('RebornProject:Notification', function(text)
    SetNotificationTextEntry('STRING')
    AddTextComponentString(text)
    DrawNotification(true, false)
end)

function ChargementAnimation(donnees)
    while (not HasAnimDictLoaded(donnees)) do 
        RequestAnimDict(donnees)
        Wait(5)
    end
end

--[[ Citizen.CreateThread(function()
	while not NetworkIsPlayerActive(PlayerId()) do
		Citizen.Wait(1)
	end
	
	while true do
		local sleep = 1500
		local player = PlayerPedId()
        local closestPlayer, distance = ESX.Game.GetClosestPlayer()
	
        if closestPlayer ~= -1 and distance <= 3.0 then

		if IsPedDeadOrDying(closestPlayer, 1) then  ------------------------------------------------------------------------------ Here you can change the RADIUS of the Safe Zone. Remember, whatever you put here will DOUBLE because 
			sleep = 0
			if not notifIn then		
				sleep = 0																	  -- it is a sphere. So 50 will actually result in a diameter of 100. I assume it is meters. No clue to be honest.
				NetworkSetFriendlyFireOption(false)
				ClearPlayerWantedLevel(PlayerId())
				SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true)
				notifIn = true
				notifOut = false
			end
		else
			if not notifOut then
				sleep = 0
				NetworkSetFriendlyFireOption(true)
				notifOut = true
				notifIn = false
			end
		end
		if notifIn then
		sleep = 500
			
		else
		DisableControlAction(2, 37, true) -- disable weapon wheel (Tab)
		DisablePlayerFiring(player,true) -- Disables firing all together if they somehow bypass inzone Mouse Disable
		DisableControlAction(0, 106, true) -- Disable in-game mouse controls
		DisableControlAction(0, 45, true) -- Disable R Button
		DisableControlAction(0, 24, true) -- Disable R Button
		DisableControlAction(0, 69, true)
		DisableControlAction(0, 92, true)
		DisableControlAction(0, 106, true)
		DisableControlAction(0, 140, true)
		
			if IsDisabledControlJustPressed(2, 37) then --if Tab is pressed, send error message
				sleep = 0
				SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true) -- if tab is pressed it will set them to unarmed (this is to cover the vehicle glitch until I sort that all out)
				ESX.ShowNotification('لايمكن إستعمال الأسلحة في <font color=##2ECC71>منطقة اَمنة</font>')
			end
			if IsDisabledControlJustPressed(0, 106) then --if LeftClick is pressed, send error message
				sleep = 0
				SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true) -- If they click it will set them to unarmed
				ESX.ShowNotification('لايمكن إستعمال القوة في <font color=##2ECC71>منطقة اَمنة</font>')
			end
			if IsDisabledControlJustPressed(0, 140) then --if R is pressed, send error message
				sleep = 0
				SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true) -- If they click it will set them to unarmed
				ESX.ShowNotification('لايمكن إستعمال القوة في <font color=##2ECC71>منطقة اَمنة</font>')
			end
		end
    end
		-- Comment out lines 142 - 145 if you dont want a marker.
        --if DoesEntityExist(player) then	      --The -1.0001 will place it on the ground flush		-- SIZING CIRCLE |  x    y    z | R   G    B   alpha| *more alpha more transparent*
	 	--	DrawMarker(1, zones[closestZone].x, zones[closestZone].y, zones[closestZone].z-1.0001, 0, 0, 0, 0, 0, 0, 100.0, 100.0, 2.0, 13, 232, 255, 155, 0, 0, 2, 0, 0, 0, 0) -- heres what all these numbers are. Honestly you dont really need to mess with any other than what isnt 0.
	 		--DrawMarker(type, float posX, float posY, float posZ, float dirX, float dirY, float dirZ, float rotX, float rotY, float rotZ, float scaleX, float scaleY, float scaleZ, int red, int green, int blue, int alpha, BOOL bobUpAndDown, BOOL faceCamera, int p19(LEAVE AS 2), BOOL rotate, char* textureDict, char* textureName, BOOL drawOnEnts)
	 	--end
		 Citizen.Wait(sleep)
	end
end) ]]

RegisterNetEvent("hamada:playerResurrected")
AddEventHandler("hamada:playerResurrected", function()
    local ped = PlayerPedId()
	local pos = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    NetworkResurrectLocalPlayer(pos.x, pos.y, pos.z, heading, false, false)
end)

CreateThread(function()
    while true do
        Wait(0)

        if IsControlPressed(1, 19) and IsControlJustPressed(1, 46) then  -- alt + E
            if count == 0 then 
                slapCoolDown(6)
            if IsPedArmed(GetPlayerPed(-1), 7) then
                SetCurrentPedWeapon(GetPlayerPed(-1), GetHashKey('WEAPON_UNARMED'), true)
            end
            if (DoesEntityExist(GetPlayerPed(-1)) and not IsEntityDead(GetPlayerPed(-1))) then
                ChargementAnimation("rcmnigel1c")
                TaskPlayAnim(GetPlayerPed(-1), "rcmnigel1c", "hailing_whistle_waive_a", 2.0, 2.0, 2000, 51, 0, false, false, false)
            elseif IsPedInAnyVehicle(PlayerPedId()) then
                ESX.ShowNotification("<font color=red>لايمكنك صفع شخص وانت داخل المركبة")
            elseif IsPedInAnyVehicle(GetPlayerPed(closestPlayer)) then
                ESX.ShowNotification("<font color=red>لايمكن صفع شخص داخل مركبه")
            elseif IsPedDeadOrDying(GetPlayerPed(closestPlayer)) then
                ESX.ShowNotification("<font color=red>لايمكن صفع لاعب مغمى عليه او ميت")
            end
        else
            ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..count..' ثانية</font>')
        end
       end
    end
end)

CreateThread(function()
    local coordss = 'h8&3f%@se'
    while true do
        local closestPlayer, distance = ESX.Game.GetClosestPlayer()
        Wait(0)

        local sleep = true

        if closestPlayer ~= -1 and distance <= 3.0 then
        sleep = false
        if IsControlPressed(1, 19) and IsControlJustPressed(1, 47) then  -- alt + G
            local CitoyenCible, distance = ESX.Game.GetClosestPlayer()
            if (distance ~= -1 and distance < 2.0001) then
                if count == 0 then 
                    if IsPedArmed(GetPlayerPed(-1), 7) then
                        SetCurrentPedWeapon(GetPlayerPed(-1), GetHashKey('WEAPON_UNARMED'), true)
                    end

                    if (DoesEntityExist(GetPlayerPed(-1)) and not IsEntityDead(GetPlayerPed(-1))) then
					if IsPedInAnyVehicle(PlayerPedId(), false) then
                    elseif IsPedInAnyVehicle(PlayerPedId()) then
                        ESX.ShowNotification("<font color=red>لايمكنك صفع شخص وانت داخل المركبة")
                    elseif IsPedInAnyVehicle(GetPlayerPed(closestPlayer)) then
                        ESX.ShowNotification("<font color=red>لايمكن صفع شخص داخل مركبه")
                    elseif IsPedDeadOrDying(GetPlayerPed(closestPlayer)) then
                        ESX.ShowNotification("<font color=red>لايمكن صفع لاعب مغمى عليه او ميت")
	                else
                        slapCoolDown(CoolDownTime)
                        TriggerServerEvent('napoly_xplevel:updateCurrentPlayerXP_clientSide', 'remove', 100, 'ضرب كف للاعب')
                        --exports.napoly_xplevel:ESXP_Remove(TakeXP)
                        ChargementAnimation("melee@unarmed@streamed_variations")
                        --TaskPlayAnim(GetPlayerPed(-1), "melee@unarmed@streamed_variations", "plyr_takedown_front_slap", 8.0, 1.0, 1500, 1, 0, 0, 0, 0) -- old
                        TaskPlayAnim(GetPlayerPed(-1), "melee@unarmed@streamed_variations", "plyr_takedown_front_slap", 8.0, 1.0, 1500, 1, 0, 0, 0, 0)
                        TriggerServerEvent("RebornProject:SyncK2K5KGiffle", GetPlayerServerId(CitoyenCible), coordss)
						Wait(400)
						TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 3.0, "slap", 0.3)
                    end
                    end
                else
					ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..count..' ثانية</font>')
                end
            end
        end
    end
        if sleep then
            Wait(500)
        end
    end
end)

CreateThread(function()
    local doneK = false
    while true do
        Wait(0)
        if IsControlPressed(1, 19) and IsControlJustPressed(1, 74) and not doneK then  -- alt + H
           ExecuteCommand("k")
		   doneK = true
		   Wait(1500)
		   doneK = false
      end
    end
end)