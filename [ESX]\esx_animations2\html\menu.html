﻿<!DOCTYPE html>
<html lang="en">
<head>
    
    <script type="text/javascript" src="raphael.min.js"></script>
    <script type="text/javascript" src="wheelnav.min.js"></script>
    <script type="text/javascript" src="nui://game/ui/jquery.js"></script>

    <!-- Default CSS styles -->
    <style>
		#font-face {
			font-family: RB-Bold;
			src: url('../RB-Bold.ttf');
		}
		#wheelDiv > svg {
			font-family: RB-Bold;
			font-weight: bold;
			text-shadow:
						3px 3px 0 #000,
						-1px -1px 0 #000,  
						1px -1px 0 #000,
						-1px 1px 0 #000,
						1px 1px 0 #000;
            width: 100%;
            height: 100%;
        }
        #wheelDiv {
			font-family: RB-Bold;
			font-weight: bold;
			text-shadow:
						3px 3px 0 #000,
						-1px -1px 0 #000,  
						1px -1px 0 #000,
						-1px 1px 0 #000,
						1px 1px 0 #000;
            width: 400px;
            height: 400px;
            margin: auto;
        }
        #container {
			font-family: RB-Bold;
			font-weight: bold;
            margin-top: 18%;
            flex-direction: column;
			
        }
    </style>

    <!-- Javascript code -->
    <script type="text/javascript">
        $(function() {
            window.addEventListener('message', function(event) {
                // Initialize menu
                if (event.data.type == 'init') {
                    // Set global keybind and resource name
                    menuKeybind = event.data.data.keybind;
                    resourceName = event.data.resourceName

                    // Get number of wheels and create array to hold them
                    numWheels = event.data.data.wheels.length;
                    wheels = new Array(numWheels);

                    // Get wheel style settings
                    wheelStyle = event.data.data.style;

                    // Set wheel size
                    var wheelSize = wheelStyle.sizePx;
                    document.getElementById("wheelDiv").style.width = wheelSize+"px";
                    document.getElementById("wheelDiv").style.height = wheelSize+"px";

                    // Set top margin to center wheel
                    var marginTopSize = ($(window).height() - wheelSize) / 2;
                    document.getElementById("container").style.marginTop = marginTopSize+"px";

                    // Create and initialize all wheels
                    for (var i = 0; i < numWheels; i++) {
                        // Create new wheelnav and add to array of wheels
                        if (i == 0) {
                            wheels[i] = new wheelnav('wheelDiv', null);
                        }
                        else {
                            wheels[i] = new wheelnav('wheelDiv', wheels[0].raphael);
                        }

                        // Get wheel data from JSON and customize wheel
                        wheelData = event.data.data.wheels[i];
                        wheels[i].navAngle = wheelData.navAngle;
                        wheels[i].clickModeRotate = false;
                        wheels[i].slicePathFunction = slicePath().DonutSlice;
                        wheels[i].slicePathCustom = slicePath().DonutSliceCustomization();
                        wheels[i].slicePathCustom.minRadiusPercent = wheelData.minRadiusPercent;
                        wheels[i].slicePathCustom.maxRadiusPercent = wheelData.maxRadiusPercent;
                        wheels[i].sliceInitPathCustom = wheels[i].slicePathCustom;
                        wheels[i].sliceHoverPathCustom = wheels[i].slicePathCustom;
                        wheels[i].sliceSelectedPathCustom = wheels[i].slicePathCustom;
                        wheels[i].slicePathAttr = wheelStyle.slices.default;
                        wheels[i].sliceHoverAttr = wheelStyle.slices.hover;
                        wheels[i].sliceSelectedAttr = wheelStyle.slices.selected;
                        wheels[i].titleAttr = wheelStyle.titles.default;
                        wheels[i].titleHoverAttr = wheelStyle.titles.hover;
                        wheels[i].titleSelectedAttr = wheelStyle.titles.selected;
                        wheels[i].createWheel(wheelData.labels);
                        
                        // Clear selected items and add mouse functions
                        for (var j = 0; j < wheels[i].navItems.length; j++) {
                            // Clear selected
                            wheels[i].navItems[j].selected = false;

                            // Add events for both navSlice and navTitle
                            const execCmd = wheelData.commands[j]
                            wheels[i].navItems[j].navSlice.mousedown(function () {
                                $.post('http://'+resourceName+'/sliceclicked', JSON.stringify({command: execCmd}));
                            });
                            wheels[i].navItems[j].navTitle.mousedown(function () {
                                $.post('http://'+resourceName+'/sliceclicked', JSON.stringify({command: execCmd}));
                            });
                        }

                        // Refresh menu
                        wheels[i].refreshWheel();
                    }
                }
                // Destroy menu
                else if (event.data.type == 'destroy') {
                    // Remove all wheels when they exist
                    if ((typeof wheels !== 'undefined') && (typeof numWheels !== 'undefined')) {
                        for (var i = 0; i < numWheels; i++) {
                            wheels[i].removeWheel();
                        }
                        delete wheels;
                        delete numWheels;
                    }
                }
            });
            window.addEventListener("keyup", function onEvent(event) {
                // Close menu when key is released
                if (event.key === menuKeybind) {
                    $.post('http://'+resourceName+'/closemenu', JSON.stringify({}));
                }
            });
        });
    </script>

</head>
<body>
    <!-- HTML code -->
    <div id="container">
        <div id='wheelDiv' data-wheelnav>
        </div>
    </div>
</body>
</html>
