function IsPlayerGrabbable(target, source)
	local coords = {
		GetEntityCoords(GetPlayerPed(target)),
		GetEntityCoords(GetPlayerPed(source)),
	} 
	return #(coords[1] - coords[2]) < 5.0
end

RegisterServerEvent('cmg3_animations:sync')
AddEventHandler('cmg3_animations:sync', function(target, animationLib,animationLib2, animation, animation2, distans, distans2, height,targetSrc,length,spin,controlFlagSrc,controlFlagTarget,animFlagTarget,attachFlag)
	if (IsPlayerGrabbable(targetSrc, source)) then
		print("got to srv cmg3_animations:sync")
		print("got that fucking attach flag as: " .. tostring(attachFlag))
		TriggerClientEvent('cmg3_animations:syncTarget', targetSrc, source, animationLib2, animation2, distans, distans2, height, length,spin,controlFlagTarget,animFlagTarget,attachFlag)
		print("triggering to me: " .. source)
		print("triggering to target: " .. tostring(targetSrc))
		TriggerClientEvent('cmg3_animations:syncMe', source, animationLib, animation,length,controlFlagSrc,animFlagTarget)
	end
end)

RegisterServerEvent('cmg3_animations:stop')
AddEventHandler('cmg3_animations:stop', function(targetSrc)
	TriggerClientEvent('cmg3_animations:cl_stop', targetSrc)
end)