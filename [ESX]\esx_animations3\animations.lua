--New custom animations based off DavesEmotes from @davewazere

--https://forum.fivem.net/t/release-daves-emotes/140216 

--Finger Pointing is taken from @Geekness and @Hallux

--https://forum.fivem.net/t/release-finger-pointing-by-geekness/103722

--<PERSON><PERSON><PERSON><PERSON> is from here @callmejaf

--https://forum.fivem.net/t/release-toggle-ragdoll/53668

--<PERSON><PERSON> is from wolfknights script @WolfKnight

--https://forum.fivem.net/t/release-crouch-script-1-0-1-now-button-based/14742





-- Commands

-- Salute: 		/e salute 		: Salute your commrads!

-- Bird 1: 		/e finger 		: One hand middle Finger

-- Bird 2: 		/e finger2 		: 2 hands middle finger

-- Surrender: 	/e surrender	: Kneeldown hands behind head surrender

-- Facepalm: 	/e palm			: Facepalm

-- Notes: 		/e notes		: Write down notes on paper

-- Brief:		/e brief		: Equip a tactical brief case

-- Brief2:		/e brief2		: Equip a leather brief case

-- Foldarms:	/e foldarms		: Cross your arms 

-- Foldarms2: 	/e foldarms2	: Cross arms v2

-- Damn:		/e damn			: Throw your arms in disbelief

-- Fail:		/e fail			: Become visibly dissapointed

-- Gang1:		/e gang1		: Gang sign 1

-- Gang2:		/e gang2		: Gang sign 2

-- No:			/e no			: Shake your head no

-- Pickbutt:	/e pickbutt		: Pick your butt

-- Grab Crotch:	/e grabcrotch	: Grab your crotch

-- Peace:		/e peace		: Hold peace sign

-- Cigar:		/e cigar		: Place cigar in your mouth

-- Cigar2:		/e cigar2		: Place a burnt cigar in your mouth

-- Joint:		/e joint		: Place a joint in your mouth

-- Cig:			/e cig			: Place a cig in your mouth

-- Holdcigar:	/e holdcigar	: Hold a cigar in your hand 

-- Holdcig:		/e holdcig		: Hold a cig in your hand 

-- Holdjoint:	/e holdjoint	: Hold a joint in your hand

-- Dead:		/e dead			: Play dead

-- Holster:		/e holster		: Hand hovers over holster 

-- Aim:			/e aim			: Aim a pistol at the floor 

-- Aim2:		/e aim2			: Aim a pistol up to your shoulder

-- 				/e slowclap		: Slowly clap your hands

--				/e box			: Pull out a box and walk around with it

--				/e cheer		: Cheer

--				/e bum			: Lay on the ground like a bum

--				/e leanwall		: Lean against a wall

--				/e copcrowd1	: Calm down a crowd like a cop

--				/e copcrowd2	: Control a scene like a cop

--				/e copidle		: idle like a cop





-- If you want ESX features enabled you can uncomment this stuff

--------------------------------------------------------------------------------------------- ESX SUPPORT ---------------------------------------------------------------------------------------------





--ESX 			    			= nil

--local PlayerData 				= {}



--Citizen.CreateThread(function()

--	while ESX == nil do

--		TriggerEvent('esx:getShhellofuv1aredObjhellofuv1ect', function(obj) ESX = obj end)

--		Citizen.Wait(0)

--	end

--end)



--RegisterNetEvent('esx:playerLoaded')

--AddEventHandler('esx:playerLoaded', function(xPlayer)

--  PlayerData = xPlayer

--end)



--RegisterNetEvent('esx:setJob')

--AddEventHandler('esx:setJob', function(job)

--  PlayerData.job = job

--end)



------------------------------------------------------------------------------------------ END ESX SUPPORT ------------------------------------------------------------------------------------------



local radioActive 				= false

local radioButton				= 303 --- U by default  -- use 57 for f10

local handsUpButton				= 74 --- H by default -- use 73 for X



--[[ حركات موصلة بازرار



--- Function for radio chatter function

Citizen.CreateThread( function()



	while true do

		Citizen.Wait(0)

		-- if you use ESX Framework and want this to be a cop only thing then replace the line below this with the following:

		-- if (PlayerData.job ~= nil and PlayerData.job.name == 'police') and (IsControlJustPressed(0,radioButton)) then

		if (IsControlJustPressed(0,radioButton))  then

			local ped = PlayerPedId()

			--TriggerEvent('chatMessage', 'TESTING ANIMATION')

	

			if ( DoesEntityExist( ped ) and not IsEntityDead( ped ) ) then 

				radioActive = true

	

				if radioActive then

	

					RequestAnimDict( "random@arrests" )

	

					while ( not HasAnimDictLoaded( "random@arrests" ) ) do 

						Citizen.Wait( 100 )

					end

	

					if IsEntityPlayingAnim(ped, "random@arrests", "generic_radio_chatter", 3) then

						ClearPedSecondaryTask(ped)

					else

						TaskPlayAnim(ped, "random@arrests", "generic_radio_chatter", 2.0, 2.5, -1, 49, 0, 0, 0, 0 )

						local prop_name = prop_name

						local secondaryprop_name = secondaryprop_name

						DetachEntity(prop, 1, 1)

						DeleteObject(prop)

						DetachEntity(secondaryprop, 1, 1)

						DeleteObject(secondaryprop)

						--SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)

					end

				end

			end

		end

	end

end)

	

--- Broke this into two functions because it was bugging out for some reason.

	

Citizen.CreateThread( function()

	

	while true do

		Citizen.Wait(0)

		-- if you use ESX Framework and want this to be a cop only thing then replace the line below this with the following:

		-- if (PlayerData.job ~= nil and PlayerData.job.name == 'police') and (IsControlJustReleased(0,57))  and (radioActive) then

		if (IsControlJustReleased(0,raisehandbutton))  and (radioActive) then

			local ped = PlayerPedId()

	

			if ( DoesEntityExist( ped ) and not IsEntityDead( ped ) ) then 

				radioActive = false

				ClearPedSecondaryTask(ped)

				--SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)

			end

		end



			

	end

	

end)



Citizen.CreateThread( function()



	while true do

		Citizen.Wait(0)

		if (IsControlJustPressed(0,handsUpButton))  then

			local ped = PlayerPedId()

	

			if ( DoesEntityExist( ped ) and not IsEntityDead( ped ) ) then

	

				RequestAnimDict( "random@mugging3" )

	

				while ( not HasAnimDictLoaded( "random@mugging3" ) ) do 

					Citizen.Wait( 100 )

				end

	

				if IsEntityPlayingAnim(ped, "random@mugging3", "handsup_standing_base", 3) then

					ClearPedSecondaryTask(ped)

				else

					TaskPlayAnim(ped, "random@mugging3", "handsup_standing_base", 2.0, 2.5, -1, 49, 0, 0, 0, 0 )

					local prop_name = prop_name

					local secondaryprop_name = secondaryprop_name

					DetachEntity(prop, 1, 1)

					DeleteObject(prop)

					DetachEntity(secondaryprop, 1, 1)

					DeleteObject(secondaryprop)

				end

			end

		end

	end

end)

--]]



RegisterCommand("e",function(source, args)

	local player = PlayerPedId()

	if tostring(args[1]) == nil then

		print("Invalid syntax, correct syntax is: /e <animation> ")

		return

	else

		if tostring(args[1]) ~= nil then

            local argh = tostring(args[1])



			if argh == 'surrender' then

				local surrendered = false

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( "random@arrests" )

					loadAnimDict( "random@arrests@busted" )

					if ( IsEntityPlayingAnim( player, "random@arrests@busted", "idle_a", 3 ) ) then 

						TaskPlayAnim( player, "random@arrests@busted", "exit", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )

						Wait (3000)

						TaskPlayAnim( player, "random@arrests", "kneeling_arrest_get_up", 8.0, 1.0, -1, 128, 0, 0, 0, 0 )

						surrendered = false

					else

						TaskPlayAnim( player, "random@arrests", "idle_2_hands_up", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )

						Wait (4000)

						TaskPlayAnim( player, "random@arrests", "kneeling_arrest_idle", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )

						Wait (500)

						TaskPlayAnim( player, "random@arrests@busted", "enter", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )

						Wait (1000)

						TaskPlayAnim( player, "random@arrests@busted", "idle_a", 8.0, 1.0, -1, 9, 0, 0, 0, 0 )

						Wait(100)

						surrendered = true

					end     

				end



				Citizen.CreateThread(function() --disabling controls while surrendured

					while surrendered do

						Citizen.Wait(0)

						if IsEntityPlayingAnim(GetPlayerPed(PlayerId()), "random@arrests@busted", "idle_a", 3) then

							DisableControlAction(1, 140, true)

							DisableControlAction(1, 141, true)

							DisableControlAction(1, 142, true)

							DisableControlAction(0,21,true)

						end

					end

				end)





			elseif argh == 'salute' then

				--local ad = "anim@mp_player_intuppersalute"

				local ad = "anim@mp_player_intincarsalutestd@ds@"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (600)

						ClearPedSecondaryTask(PlayerPedId())

					else

						TaskPlayAnim( player, ad, "idle_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'batata' then

				local ad = "anim@mp_player_intupperfinger"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (100)

						ClearPedSecondaryTask(PlayerPedId())

					else

						TaskPlayAnim( player, ad, "idle_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'palm' then

				local ad = "anim@mp_player_intupperface_palm"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (100)

						ClearPedSecondaryTask(PlayerPedId())

					else

						TaskPlayAnim( player, ad, "idle_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'notes' then

				local ad = "missheistdockssetup1clipboard@base"

				

				local prop_name = prop_name or 'prop_notepad_01'

				local secondaryprop_name = secondaryprop_name or 'prop_pencil_01'

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "base", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (100)

						ClearPedSecondaryTask(PlayerPedId())

						DetachEntity(prop, 1, 1)

						DeleteObject(prop)

						DetachEntity(secondaryprop, 1, 1)

						DeleteObject(secondaryprop)

					else

						local x,y,z = table.unpack(GetEntityCoords(player))

						prop = CreateObject(GetHashKey(prop_name), x, y, z+0.2,  true,  true, true)

						secondaryprop = CreateObject(GetHashKey(secondaryprop_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(prop, player, GetPedBoneIndex(player, 18905), 0.1, 0.02, 0.05, 10.0, 0.0, 0.0, true, true, false, true, 1, true) -- notepad

						AttachEntityToEntity(secondaryprop, player, GetPedBoneIndex(player, 58866), 0.12, 0.0, 0.001, -150.0, 0.0, 0.0, true, true, false, true, 1, true) -- pencil

						TaskPlayAnim( player, ad, "base", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'callphone' then

				local ad = "cellphone@"

				

				local prop_name = prop_name or 'prop_player_phone_01'

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "cellphone_call_listen_base", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (100)

						ClearPedSecondaryTask(PlayerPedId())

						DetachEntity(prop, 1, 1)

						DeleteObject(prop)

					else

						local x,y,z = table.unpack(GetEntityCoords(player))

						prop = CreateObject(GetHashKey(prop_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(prop, player, GetPedBoneIndex(player, 57005), 0.15, 0.02, -0.01, 105.0, -20.0, 90.0, true, true, false, true, 1, true)

						TaskPlayAnim( player, ad, "cellphone_call_listen_base", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end 

				end

			elseif argh == 'foldarms2' then

				local ad = "missfbi_s4mop"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "guard_idle_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "guard_idle_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end 

				end

			elseif argh == 'umbrella' then

				local ad = "amb@world_human_drinking@coffee@male@base"

				

				local prop_name = prop_name or 'p_amb_brolly_01'

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "base", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

						Wait (100)

						DetachEntity(prop, 1, 1)

						DeleteObject(prop)

						ClearPedSecondaryTask(PlayerPedId())

					else

						local x,y,z = table.unpack(GetEntityCoords(player))

						prop = CreateObject(GetHashKey(prop_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(prop, player, GetPedBoneIndex(player, 57005), 0.15, 0.005, -0.02, 80.0, -20.0, 175.0, true, true, false, true, 1, true)

						TaskPlayAnim( player, ad, "base", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end 

				end

			elseif argh == 'brief' then

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					GiveWeaponToPed(player, 0x88C78EB7, 1, false, true);

				end

			elseif argh == 'brief2' then

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					GiveWeaponToPed(player, 0x01B79F17, 1, false, true);

				end

			elseif argh == 'foldarms' then

				local ad = "oddjobs@assassinate@construction@"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "unarmed_fold_arms", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "unarmed_fold_arms", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )



					end     

				end

			elseif argh == 'damn' then

				local ad = "gestures@m@standing@casual"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "gesture_damn", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "gesture_damn", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'fail' then

				local ad = "random@car_thief@agitated@idle_a"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "agitated_idle_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "agitated_idle_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'gang1' then

				local ad = "mp_player_int_uppergang_sign_a"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_gang_sign_a", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_gang_sign_a", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'gang2' then

				local ad = "mp_player_int_uppergang_sign_b"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_gang_sign_b", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_gang_sign_b", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'no' then

				local ad = "mp_player_int_upper_nod"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_nod_no", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_nod_no", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'pickbutt' then

				local ad = "mp_player_int_upperarse_pick"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_arse_pick", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_arse_pick", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'grabcrotch' then

				local ad = "mp_player_int_uppergrab_crotch"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_grab_crotch", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_grab_crotch", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'peace' then

				local ad = "mp_player_int_upperpeace_sign"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "mp_player_int_peace_sign", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "mp_player_int_peace_sign", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'cigar' then

				local cigar_name = cigar_name or 'prop_cigar_02' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 47419), 0.015, -0.0001, 0.003, 55.0, 0.0, -85.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'cigar2' then

				local cigar_name = cigar_name or 'prop_cigar_01' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 47419), 0.015, -0.0001, 0.003, 55.0, 0.0, -85.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'joint' then

				local cigar_name = cigar_name or 'p_cs_joint_02' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 47419), 0.015, -0.009, 0.003, 55.0, 0.0, 110.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'cig' then

				local cigar_name = cigar_name or 'prop_amb_ciggy_01' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 47419), 0.015, -0.009, 0.003, 55.0, 0.0, 110.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'holdcigar' then

				local cigar_name = cigar_name or 'prop_cigar_03' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 26611), 0.045, -0.05, -0.010, -75.0, 0.0, 65.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'holdcig' then

				local cigar_name = cigar_name or 'prop_amb_ciggy_01' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 26611), 0.035, -0.01, -0.010, 100.0, 0.0, -100.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'holdjoint' then

				local cigar_name = cigar_name or 'p_cs_joint_02' --noprop

				local playerPed = PlayerPedId()

				

				if ( DoesEntityExist( playerPed ) and not IsEntityDead( playerPed )) then 

					if IsCigar then

						Wait(500)

						DetachEntity(cigar, 1, 1)

						DeleteObject(cigar)

						IsCigar = false

					else

						IsCigar = true

						Wait(500)

						local x,y,z = table.unpack(GetEntityCoords(playerPed))

						cigar = CreateObject(GetHashKey(cigar_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(cigar, playerPed, GetPedBoneIndex(playerPed, 26611), 0.035, -0.01, -0.010, 100.0, 0.0, -100.0, true, true, false, true, 1, true)

					end     

				end

			elseif argh == 'dead' then

				local ad = "misslamar1dead_body"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "dead_idle", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 33, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "dead_idle", 8.0, 1.0, -1, 33, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'holster' then

				local ad = "move_m@intimidation@cop@unarmed"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "idle", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'aim' then

				local ad = "move_weapon@pistol@copa"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "idle", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end

			elseif argh == 'aim2' then

				local ad = "move_weapon@pistol@cope"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "idle", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end



			elseif argh == 'guard' then

				local ad = "rcmepsilonism8"

				

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "base_carrier", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					else

						TaskPlayAnim( player, ad, "base_carrier", 8.0, 1.0, -1, 49, 0, 0, 0, 0 )

					end     

				end



			elseif argh == 'box' then

				local ad = "anim@heists@box_carry@"

				

				local prop_name = prop_name or 'hei_prop_heist_box'

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "idle", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 49, 0, 0, 0, 0 )

						DetachEntity(prop, 1, 1)

						DeleteObject(prop)

						Wait(1000)

						ClearPedSecondaryTask(PlayerPedId())

					else

						local x,y,z = table.unpack(GetEntityCoords(player))

						prop = CreateObject(GetHashKey(prop_name), x, y, z+0.2,  true,  true, true)

						AttachEntityToEntity(prop, player, GetPedBoneIndex(player, 60309), 0.025, 0.08, 0.255, -145.0, 290.0, 0.0, true, true, false, true, 1, true)

						TaskPlayAnim( player, ad, "idle", 3.0, -8, -1, 63, 0, 0, 0, 0 )

					end 

				end

			elseif argh == 'slowclap' then

				local ad = "anim@mp_player_intcelebrationmale@slow_clap"

			

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

						loadAnimDict( ad )

						if ( IsEntityPlayingAnim( player, ad, "slow_clap", 3 ) ) then 

							TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 49, 0, 0, 0, 0 )

							ClearPedSecondaryTask(player)

							Wait (100)

						else

							TaskPlayAnim( player, ad, "slow_clap", 3.0, 1.0, -1, 49, 0, 0, 0, 0 )

							Wait (500)

						end       

					end

				end



			elseif argh == 'cheer' then

				local ad = "amb@world_human_cheering@male_a"

			

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

						loadAnimDict( ad )

						if ( IsEntityPlayingAnim( player, ad, "base", 3 ) ) then 

							TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 49, 0, 0, 0, 0 )

							ClearPedSecondaryTask(player)

							Wait (100)

						else

							TaskPlayAnim( player, ad, "base", 3.0, 1.0, -1, 49, 0, 0, 0, 0 )

							Wait (500)

						end       

					end

				end



			elseif argh == 'bum' then

				local ad = "amb@lo_res_idles@"

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "world_human_bum_slumped_left_lo_res_base", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 33, 0, 0, 0, 0 )

						Wait (100)

					else

						TaskPlayAnim( player, ad, "world_human_bum_slumped_left_lo_res_base", 5.0, 1.0, -1, 33, 0, 0, 0, 0 )

						Wait (500)

					end     

				end

			elseif argh == 'leanwall' then

				local ad = "amb@lo_res_idles@"

				

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, "world_human_lean_male_foot_up_lo_res_base", 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 8.0, 1.0, -1, 33, 0, 0, 0, 0 )

						Wait (100)

					else

						TaskPlayAnim( player, ad, "world_human_lean_male_foot_up_lo_res_base", 8.0, 1.0, -1, 33, 0, 0, 0, 0 )

						Wait (500)

					end     

				end

			elseif argh == 'copcrowd' then

				local ad = "amb@code_human_police_crowd_control@idle_a" --- insert the animation dic here

				local anim = "idle_a" --- insert the animation name here

				

			

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, anim, 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

						ClearPedSecondaryTask(player)

					else

						TaskPlayAnim( player, ad, anim, 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

					end       

				end

			elseif argh == 'copcrowd2' then

				local ad = "amb@code_human_police_crowd_control@idle_b" --- insert the animation dic here

				local anim = "idle_d" --- insert the animation name here

				local player = PlayerPedId()

				

			

				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then

					loadAnimDict( ad )

					if ( IsEntityPlayingAnim( player, ad, anim, 3 ) ) then 

						TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

						ClearPedSecondaryTask(player)

					else

						TaskPlayAnim( player, ad, anim, 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

					end       

				end

				

			elseif argh == 'copidle' then



				if ( DoesEntityExist( player ) and not IsEntityDead( player )) then

					if IsPedActiveInScenario(player) then

						ClearPedTasks(player)

					else

						TaskStartScenarioInPlace(player, 'WORLD_HUMAN_COP_IDLES', 0, 1)   

					end 

				end

			end

		end

	end

end, false)



----Use /testanimation command, you can use this to easily test new animations---



RegisterCommand("testanimation",function(source, args)



	local ad = "amb@code_human_police_crowd_control@idle_b" --- insert the animation dic here

	local anim = "idle_d" --- insert the animation name here

	local player = PlayerPedId()

	



	if ( DoesEntityExist( player ) and not IsEntityDead( player )) then

		loadAnimDict( ad )

		TriggerEvent('chatMessage', ' Testing Animation')

		if ( IsEntityPlayingAnim( player, ad, anim, 3 ) ) then 

			TaskPlayAnim( player, ad, "exit", 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

			ClearPedSecondaryTask(player)

		else

			TaskPlayAnim( player, ad, anim, 3.0, 1.0, -1, 01, 0, 0, 0, 0 )

		end       

	end

end, false)

	

----------------------------------------------------------------------------------------------------------------------------

------------------------------------------------ functions -----------------------------------------------------------------

----------------------------------------------------------------------------------------------------------------------------



Citizen.CreateThread(function(prop_name, secondaryprop_name)

	while true do

		Citizen.Wait(500)

		if IsPedRagdoll(PlayerPedId()) then 

			local playerPed = PlayerPedId()

			local prop_name = prop_name

			local secondaryprop_name = secondaryprop_name

			DetachEntity(prop, 1, 1)

			DeleteObject(prop)

			DetachEntity(secondaryprop, 1, 1)

			DeleteObject(secondaryprop)

		end

	end

end)	



function loadAnimDict(dict)

	while (not HasAnimDictLoaded(dict)) do

		RequestAnimDict(dict)

		Citizen.Wait(5)

	end

end
