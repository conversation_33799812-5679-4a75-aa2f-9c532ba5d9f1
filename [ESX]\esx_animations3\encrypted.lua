
local Window1 = 1
local Window2 = 1
local Window3 = 1
local Window4 = 1

RegisterCommand('carglass1', function()
    local playerPed = PlayerPedId()
	local veh = GetVehiclePedIsIn(playerPed, false)
	if Window1 == 0 then
		RollUpWindow(veh, 0)
		Window1 = 1
	else
		RollDownWindow(veh, 0)
		Window1 = 0
	end
end, false)

RegisterCommand('carglass2', function()
    local playerPed = PlayerPedId()
	local veh = GetVehiclePedIsIn(playerPed, false)
	if Window2 == 0 then
		RollUpWindow(veh, 1)
		Window2 = 1
	else
		RollDownWindow(veh, 1)
		Window2 = 0
	end
end, false)

RegisterCommand('carglass3', function()
    local playerPed = PlayerPedId()
	local veh = GetVehiclePedIsIn(playerPed, false)
	if Window3 == 0 then
		RollUpWindow(veh, 2)
		Window3 = 1
	else
		RollDownWindow(veh, 2)
		Window3 = 0
	end
end, false)


RegisterCommand('carglass4', function()
    local playerPed = PlayerPedId()
	local veh = GetVehiclePedIsIn(playerPed, false)
	if Window4 == 0 then
		RollUpWindow(veh, 3)
		Window4 = 1
	else
		RollDownWindow(veh, 3)
		Window4 = 0
	end
end, false)
