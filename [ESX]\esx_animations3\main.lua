PlayerData = {}

MycurrentJobLeo = false



---holstered

holstered  = false

blocked	 = false



--other

inTrunk = false

isAirshot = false

handsup = false



--two players

holdingHostageInProgress = false

beingHeldHostage = false

piggyBackInProgress = false

carryingBackInProgress = false



RegisterFontFile('A9eelsh')

fontId = RegisterFontId('A9eelsh')



-- CODE HERE --

Citizen.CreateThread(function()	

	while ESX.GetPlayerData().job == nil do

		Citizen.Wait(500)

	end

	

	PlayerData = ESX.GetPlayerData()

	

	isLoeJob()

end)



RegisterNetEvent('esx:setJob')

AddEventHandler('esx:setJob', function(job)

	PlayerData.job = job

	

	isLoeJob()

end)



AddEventHandler('esx:onPlayerDeath', function(reason)

	OnPlayerDeath()

end)



AddEventHandler('onResourceStop', function(resource)

	OnPlayerDeath()

end)



function isPlayerDoingAnimation2()

	if holdingHostageInProgress or piggyBackInProgress or carryingBackInProgress or beingHeldHostage or inTrunk then

		return true

	end	

end



Citizen.CreateThread(function()

	while true do

		local parachuteState = GetPedParachuteState(PlayerPedId())

			--[[ parachuteState:

				-1: Normal  

				0: Wearing parachute on back  

				1: Parachute opening  

				2: Parachute open  

				3: Falling to doom (e.g. after exiting parachute)  

				Normal means no parachute?  

			]]

		

		if holdingHostageInProgress or piggyBackInProgress or carryingBackInProgress or beingHeldHostage or inTrunk then

			DisableControlAction(0,24,true) -- disable attack

			DisableControlAction(0,25,true) -- disable aim

			DisableControlAction(0,58,true) -- disable weapon

			DisableControlAction(0,288,true) -- F1

			DisableControlAction(0,289,true) -- F2

			DisableControlAction(0,170,true) -- F3

			DisableControlAction(0,166,true) -- F5

			DisableControlAction(0,168,true) -- F7

			DisableControlAction(0,37,true) -- TAB

			DisableControlAction(0,38,true) -- E

			

			if not inTrunk then

				DisableControlAction(0,47,true) -- G

			end	

			

			DisableControlAction(0,23,true) -- F

			DisablePlayerFiring(PlayerPedId(),true)

			

			if holdingHostageInProgress or inTrunk then

				DisableControlAction(0,243,true) -- ~

			end

		else	

			if IsControlJustReleased(0, 73) and GetLastInputMethod(2) and not isDead and parachuteState <= 0 and not inTrunk then
                local player = PlayerPedId()
                if not IsEntityPlayingAnim( player, "random@arrests@busted", "idle_a", 3 ) then
				    ClearPedTasks(PlayerPedId())
                end

			end

		end


        Citizen.Wait(1)
	end

end)



function OnPlayerDeath()

	ClearPedSecondaryTask(PlayerPedId())

	DetachEntity(PlayerPedId(), true, false)

	local closestPlayer = GetClosestPlayer(3)

	target = GetPlayerServerId(closestPlayer)

	

	--set all var to false when dead

	holstered  = true

	blocked	 = false

	inTrunk = false

	isAirshot = false

	handsup = false

	

	if carryingBackInProgress then

		carryingBackInProgress = false

		TriggerServerEvent("cmg2_animations_carry:stop",target)

	end



	if piggyBackInProgress then

		piggyBackInProgress = false

		TriggerServerEvent("cmg2_animations_piggyback:stop",target)

	end

	

	if holdingHostage then

		print("release this mofo")			

		holdingHostage = false

		holdingHostageInProgress = false 

		--ClearPedSecondaryTask(PlayerPedId())

		--DetachEntity(PlayerPedId(), true, false)

		TriggerServerEvent("cmg3_animations_takehostage:stop",target)

		Wait(100)

		releaseHostage()

	end	

end



function GetPlayers()

    local players = {}



    for i = 0, 255 do

        if NetworkIsPlayerActive(i) then

            table.insert(players, i)

        end

    end



    return players

end



function GetClosestPlayer(radius)

    local players = GetPlayers()

    local closestDistance = -1

    local closestPlayer = -1

    local ply = PlayerPedId()

    local plyCoords = GetEntityCoords(ply, 0)



    for index,value in ipairs(players) do

        local target = GetPlayerPed(value)

        if(target ~= ply) then

            local targetCoords = GetEntityCoords(GetPlayerPed(value), 0)

            local distance = GetDistanceBetweenCoords(targetCoords['x'], targetCoords['y'], targetCoords['z'], plyCoords['x'], plyCoords['y'], plyCoords['z'], true)

            if(closestDistance == -1 or closestDistance > distance) then

                closestPlayer = value

                closestDistance = distance

            end

        end

    end

	if closestDistance <= radius then

		return closestPlayer

	else

		return nil

	end

end



function isLoeJob()

	while PlayerData.job == nil do

		Citizen.Wait(500)

	end

	

	local check = false

	

	for i=1, #LeoJobs, 1 do

		if PlayerData.job.name == LeoJobs[i] then

			check = true

			break

		end

	end



	if check then

		MycurrentJobLeo = true

		Config.cooldownCurrent = Config.cooldownPolice

	else

		MycurrentJobLeo = false

		Config.cooldownCurrent = Config.cooldownCitizen

	end

end



local cooldown = 0

function CoolDown(itime)

	cooldown = itime

	CreateThread(function()

		while cooldown ~= 0 do

			cooldown = cooldown - 1

			Citizen.Wait(1000)

		end	

	end)	

end



RegisterCommand('fx', function()

    if cooldown == 0 then

		CoolDown(120)

		OnPlayerDeath()

		PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)

        TriggerEvent('chatMessage', 'إدارة المشاكل التقنية', {255, 0, 0}, 'تم تنفيذ الأمر. إذا مازلت لا تتمكن من استعمال السلاح او ركوب المركبة افصل من السيرفر وحاول الاتصال مرة اخرى')

		ESX.ShowNotification('<font color=red>حل تعليق السلاح والمركبة</font><br>تم تنفيذ الأمر')

	else

		ESX.ShowNotification('<font color=red>عليك الانتظار</font><font color=orange> '..cooldown..' </font>ثانية.')

		PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)

	end

end, false)


--	  AresstAnims - Aresst animations for FiveM
--    Copyright (C) 2017  Cosharek
--
--    This program is free software: you can redistribute it and/or modify
--    it under the terms of the GNU General Public License as published by
--    the Free Software Foundation, either version 3 of the License, or
--   (at your option) any later version.
--
--    This program is distributed in the hope that it will be useful,
--    but WITHOUT ANY WARRANTY; without even the implied warranty of
--    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
--    GNU General Public License for more details.
--
--    You should have received a copy of the GNU General Public License
--    along with this program.  If not, see <http://www.gnu.org/licenses/>.

function loadAnimDict( dict )
    while ( not HasAnimDictLoaded( dict ) ) do
        RequestAnimDict( dict )
        Citizen.Wait( 5 )
    end
end 

-- RegisterNetEvent( 'KneelHU' )
-- AddEventHandler( 'KneelHU', function()
--     local player = GetPlayerPed( -1 )
-- 	if ( DoesEntityExist( player ) and not IsEntityDead( player )) then 
--         loadAnimDict( "random@arrests" )
-- 		loadAnimDict( "random@arrests@busted" )
-- 		if ( IsEntityPlayingAnim( player, "random@arrests@busted", "idle_a", 3 ) ) then 
-- 			TaskPlayAnim( player, "random@arrests@busted", "exit", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )
-- 			Wait (3000)
--             TaskPlayAnim( player, "random@arrests", "kneeling_arrest_get_up", 8.0, 1.0, -1, 128, 0, 0, 0, 0 )
--         else
--             TaskPlayAnim( player, "random@arrests", "idle_2_hands_up", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )
-- 			Wait (4000)
--             TaskPlayAnim( player, "random@arrests", "kneeling_arrest_idle", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )
-- 			Wait (500)
-- 			TaskPlayAnim( player, "random@arrests@busted", "enter", 8.0, 1.0, -1, 2, 0, 0, 0, 0 )
-- 			Wait (1000)
-- 			TaskPlayAnim( player, "random@arrests@busted", "idle_a", 8.0, 1.0, -1, 9, 0, 0, 0, 0 )
--         end     
--     end
-- end )

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if IsEntityPlayingAnim(GetPlayerPed(PlayerId()), "random@arrests@busted", "idle_a", 3) then
			DisableControlAction(1, 140, true)
			DisableControlAction(1, 141, true)
			DisableControlAction(1, 142, true)
			DisableControlAction(0,21,true)
		end
	end
end)

-- RegisterCommand("k", function(source, args, raw) 
--     TriggerEvent("KneelHU", {})
-- end, false) 


----------------------------------------------
-- External Vehicle Commands, Made by FAXES --
----------------------------------------------

--- Code ---

function ShowInfo(text)
	BeginTextCommandThefeedPost("STRING")
	AddTextComponentSubstringPlayerName(text)
	EndTextCommandThefeedPostTicker(false, false)
end

RegisterCommand("trunk", function(source, args, raw)
    local ped = PlayerPedId()
    local veh = GetVehiclePedIsUsing(ped)
    local vehLast = GetPlayersLastVehicle()
    local distanceToVeh = GetDistanceBetweenCoords(GetEntityCoords(ped), GetEntityCoords(vehLast), 1)
    local door = 5

    if IsPedInAnyVehicle(ped, false) then
        if GetVehicleDoorAngleRatio(veh, door) > 0 then
            SetVehicleDoorShut(veh, door, false)
            ESX.ShowNotification('<font color=#3498DB>اغلاق شنطة المركبة')
        else	
            SetVehicleDoorOpen(veh, door, false, false)
            ESX.ShowNotification('<font color=#3498DB>فتح شنطة المركبة')
        end
    else
        if distanceToVeh < 6 then
            if GetVehicleDoorAngleRatio(vehLast, door) > 0 then
                SetVehicleDoorShut(vehLast, door, false)
                ESX.ShowNotification('<font color=#3498DB>اغلاق شنطة المركبة')
            else
                SetVehicleDoorOpen(vehLast, door, false, false)
                ESX.ShowNotification('<font color=#3498DB>فتح شنطة المركبة')
            end
        else
            ESX.ShowNotification('<font color=#3498DB>انت بعيد جدا عن المركبة') -- بعيد جدا عن السيارة

        end
    end
end)

RegisterCommand("hood", function(source, args, raw)
    local ped = PlayerPedId()
    local veh = GetVehiclePedIsUsing(ped)
    local vehLast = GetPlayersLastVehicle()
    local distanceToVeh = GetDistanceBetweenCoords(GetEntityCoords(ped), GetEntityCoords(vehLast), 1)
    local door = 4

    if IsPedInAnyVehicle(ped, false) then
        if GetVehicleDoorAngleRatio(veh, door) > 0 then
            SetVehicleDoorShut(veh, door, false)
            ESX.ShowNotification('<font color=#3498DB>اغلاق كبوت المركبة')
        else	
            SetVehicleDoorOpen(veh, door, false, false)
            ESX.ShowNotification('<font color=#3498DB>فتح كبوت المركبة')
        end
    else
        if distanceToVeh < 4 then
            if GetVehicleDoorAngleRatio(vehLast, door) > 0 then
                SetVehicleDoorShut(vehLast, door, false)
                ESX.ShowNotification('<font color=#3498DB>اغلاق كبوت المركبة')
            else	
                SetVehicleDoorOpen(vehLast, door, false, false)
                ESX.ShowNotification('<font color=#3498DB>فتح كبوت المركبة')
            end
        else
            ESX.ShowNotification('<font color=#3498DB>انت بعيد جدا عن المركبة')
        end
    end
end)

RegisterCommand("door", function(source, args, raw)
    local ped = PlayerPedId()
    local veh = GetVehiclePedIsUsing(ped)
    local vehLast = GetPlayersLastVehicle()
    local distanceToVeh = GetDistanceBetweenCoords(GetEntityCoords(ped), GetEntityCoords(vehLast), 1)
    
    if args[1] == "1" then -- Front Left Door
        door = 0
    elseif args[1] == "2" then -- Front Right Door
        door = 1
    elseif args[1] == "3" then -- Back Left Door
        door = 2
    elseif args[1] == "4" then -- Back Right Door
        door = 3
    else
        door = nil
        ShowInfo("Usage: ~n~~b~/door [door]")
        ShowInfo("~y~Possible doors:")
        ShowInfo("1: Front Left Door~n~2: Front Right Door")
        ShowInfo("3: Back Left Door~n~4: Back Right Door")
    end

    if door ~= nil then
        if IsPedInAnyVehicle(ped, false) then
            if GetVehicleDoorAngleRatio(veh, door) > 0 then
                SetVehicleDoorShut(veh, door, false)
                ESX.ShowNotification('<font color=#3498DB>اغلاق باب المركبة')
            else	
                SetVehicleDoorOpen(veh, door, false, false)
                ESX.ShowNotification('<font color=#3498DB>فتح باب المركبة')
            end
        else
            if distanceToVeh < 4 then
                if GetVehicleDoorAngleRatio(vehLast, door) > 0 then
                    SetVehicleDoorShut(vehLast, door, false)
                    ESX.ShowNotification('<font color=#3498DB>فتح باب المركبة')
                else	
                    SetVehicleDoorOpen(vehLast, door, false, false)
                    ESX.ShowNotification('<font color=#3498DB>اغلاق باب المركبة')
                end
            else
                ESX.ShowNotification('<font color=#3498DB>انت بعيد جدا عن المركبة')
            end
        end
    end
end)

if usingKeyPress then
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(10)
            local ped = PlayerPedId()
            local veh = GetVehiclePedIsUsing(ped)
            local vehLast = GetPlayersLastVehicle()
            local distanceToVeh = GetDistanceBetweenCoords(GetEntityCoords(ped), GetEntityCoords(vehLast), 1)
            local door = 5
            if IsControlPressed(1, 224) and IsControlJustPressed(1, togKey) then
                if not IsPedInAnyVehicle(ped, false) then
                    if distanceToVeh < 4 then
                        if GetVehicleDoorAngleRatio(vehLast, door) > 0 then
                            SetVehicleDoorShut(vehLast, door, false)
                            ShowInfo("[Vehicle] ~g~Trunk Closed.")
                        else	
                            SetVehicleDoorOpen(vehLast, door, false, false)
                            ShowInfo("[Vehicle] ~g~Trunk Opened.")
                        end
                    else
                        ShowInfo("[Vehicle] ~y~Too far away from vehicle.")
                    end
                end
            end
        end
    end)
end



--[[ SEAT SHUFFLE ]]--
--[[ BY JAF ]]--

local disableShuffle = true
function disableSeatShuffle(flag)
	disableShuffle = flag
end

Citizen.CreateThread(function()
	while true do
		if IsPedInAnyVehicle(PlayerPedId(), false) and disableShuffle then
			if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), 0) == PlayerPedId() then
				if GetIsTaskActive(PlayerPedId(), 165) then
					SetPedIntoVehicle(PlayerPedId(), GetVehiclePedIsIn(PlayerPedId(), false), 0)
				end
			end
		end

        Citizen.Wait(1)
	end
end)

RegisterNetEvent("SeatShuffle")
AddEventHandler("SeatShuffle", function()
	if IsPedInAnyVehicle(PlayerPedId(), false) then
		disableSeatShuffle(false)
		Citizen.Wait(5000)
		disableSeatShuffle(true)
	else
		CancelEvent()
	end
end)

RegisterCommand("shuff", function(source, args, raw) --change command here
    TriggerEvent("SeatShuffle")
end, false) --False, allow everyone to run it