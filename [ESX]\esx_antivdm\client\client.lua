ESX = exports["es_extended"]:getSharedObject()

local adding = false
local previousHealth = 100

-- متابعة صحة اللاعب كل ثانية
CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        previousHealth = GetEntityHealth(playerPed)
        Wait(1000)
    end
end)

-- معالجة الأضرار التي تلحق باللاعب
AddEventHandler("entityDamaged", function(entity, attacker, weapon, damage)
    if IsEntityAPed(entity) and entity == PlayerPedId() then
        local targetPed = entity
        local attackerPed = attacker

        -- التحقق من أن الهجوم تم بواسطة مركبة
        if weapon == 0 or weapon == -********** then
            if IsPedInAnyVehicle(attackerPed, false) and GetPedInVehicleSeat(attackerPed, -1) then
                -- التحقق مما إذا كانت الإصابة قاتلة
                local currentHealth = GetEntityHealth(targetPed)
                if currentHealth <= 0 and previousHealth > 0 then
                    if not adding then
                        adding = true
                        local attackerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(attackerPed))

                        -- إرسال حدث للسيرفر مع معلومات الهجوم
                        TriggerServerEvent("707store_antivdm:warn", "CODICEANTITRIGGERANTIVDM121683GDSAàòèPLàDS", attackerServerId)

                        SetTimeout(1500, function()
                            adding = false
                        end)
                    end
                end
            end
        end
    end
end)

-- حدث استجابة من السيرفر
RegisterNetEvent("707store_antivdm:warn", function(target, num)
    local playerPed = PlayerPedId()

    -- التأكد من وجود اللاعب في مركبة
    if IsPedInAnyVehicle(playerPed, false) then
        TriggerEvent(Config.levelxp, Config.removexp)
        ESX.ShowNotification(Config.removeMessage)
        TriggerServerEvent("wesam707")
    end
end)
