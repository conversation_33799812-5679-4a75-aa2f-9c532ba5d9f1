ESX = exports["es_extended"]:getSharedObject()

AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= "707store_antivdm") then
        return
    end
end)

---@param num playerId
---@param target securityCode
RegisterNetEvent("707store_antivdm:warn", function(target, num)
    if target ~= "CODICEANTITRIGGERANTIVDM121683GDSAàòèPLàDS" then
        return
    end
    TriggerClientEvent("707store_antivdm:warn", num, target, WEBHOOK)
end)

RegisterServerEvent("wesam707")
AddEventHandler("wesam707", function()
    local xTarget = ESX.GetPlayerFromId(source)
    nameh2rfjhdsr = xTarget.getName()
    TriggerClientEvent('chatMessage', -1, " ⭐الرقابة والتفتيش " ,  {249, 6, 6} ,  "خصم  ^8 " ..Config.removexp.. '^0 خبرة على ^3' .. nameh2rfjhdsr.."^7 استعمال المركبة كسلاح ^7")
end)
