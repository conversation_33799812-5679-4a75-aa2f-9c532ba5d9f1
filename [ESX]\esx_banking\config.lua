Config = {
	Debug = false,
	DrawMarker = 10,
	Locale = GetConvar('esx:locale', 'ar'),
	EnablePeds = false,
	AtmModels = {`prop_fleeca_atm`, `prop_atm_01`, `prop_atm_02`, `prop_atm_03`},
	Banks = {
		{
			Position = vector4(149.91, -1040.74, 29.374, 160),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(-1212.63, -330.78, 37.59, 210),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(-2962.47, 482.93, 15.5, 270),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(-113.01, 6470.24, 31.43, 315),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(314.16, -279.09, 53.97, 160),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(-350.99, -49.99, 48.84, 160),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(1175.02, 2706.87, 37.89, 0),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
		{
			Position = vector4(246.63, 223.62, 106.0, 160),
			Blip = {
				Enabled = true,
				Color = 69,
				Label = 'Bank',
				Sprite = 108,
				Scale = 0.7
			}
		},
	},
	Peds = {
		{
			Position = vector4(149.5513, -1042.1570, 29.3680, 341.6520),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(-1211.8585, -331.9854, 37.7809, 28.5983),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(-2961.0720, 483.1107, 15.6970, 88.1986),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(-112.2223, 6471.1128, 31.6267, 132.7517),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(313.8176, -280.5338, 54.1647, 339.1609),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(-351.3247, -51.3466, 49.0365, 339.3305),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(1174.9718, 2708.2034, 38.0879, 178.2974),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		},
		{
			Position = vector4(247.0348, 225.1851, 106.2875, 158.7528),
			Model = `U_M_M_BankMan`,
			Scenario = 'WORLD_HUMAN_CLIPBOARD'
		}
	}
}

