:root {
  --primary-color: #2e69d5;
  --secondary-color: #378cbf;
  --text-color: #ffff;
  --background-light: #232934;
  --background-light-2: #202020;
  --background-dark: #0a0e19;
  --accent-red: #378cbf;
  --accent-green: #45b157;
  --accent-gray: #474e57;
  --accent-gray-2: #545457;

  --columnsBackground: var(--background-dark);
  --allTitleColor: var(--text-color);
  --yourMoneyPriceTextColor: var(--accent-green);
  --bankCardBackgroundColor: linear-gradient(
    110deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  --buttonBackgroundColor: var(--background-light);
  --inputBackgroundColor: var(--background-light);
  --inputTextColor: var(--text-color);
  --inputPlaceholderColor: var(--accent-gray-2);
  --checkmarkColor: var(--text-color);
  --acceptExitButtonColorText: var(--text-color);
  --exitButtonHoverBackgroundColor: var(--primary-color);
  --acceptButtonHoverBackgroundColor: var(--secondary-color);
  --bankCardTextColor: var(--text-color);
  --bankCardNumberColor: var(--text-color);
  --moneyContainerBackgroundColor: var(--background-light);
  --moneyAndTransactionContainer: var(--background-light);
  --transactionContainerBackgroundColor: var(--background-light);
  --moneyAndTransactionallTitleColorText: var(--text-color);
  --transactionDataTextColor: var(--text-color);
  --cardSubtitleTextColor: var(--accent-gray);
  --depositColorText: var(--accent-green);
  --withDrawColorText: var(--primary-color);
  --moreHistoryButtonHoverColor: var(--secondary-color);
  --moreGraphButtonHoverColor: var(--secondary-color);
  --hrRowColor: var(--accent-gray);
  --loadingCircle: var(--secondary-color);
  --tableAllTextColor: var(--text-color);
  --tableBackgroundColorPaginationButton: var(--secondary-color);
  --tableBackgroundColorPaginationButtonHover: var(--secondary-color);
  --tableRowHoverBackgroundColor: var(--secondary-color);
  --tableScrollBarBackgroundColor: var(--background-light);
  --tableScrollBarBackgroundColorHover: var(--secondary-color);
  --tableSelectBoxBackgroundColor: var(--background-light);
  --tableSearchInputBackgroundColor: var(--background-light);
  --tableScrollBarBackgroundColor2: var(--background-light);
  --disableButtonBackgroundColor: var(--background-light-2);
  --graphLineColor: var(--secondary-color);
  --graphLineBackgroundColor: var(--secondary-color);
}

@import url("https://fonts.googleapis.com/css2?family=Raleway:wght@100;400;500;800&display=swap");

* {
  margin: 0;
  padding: 0;
}

#wrapper {
  width: 100%;
  height: 93.24vmin;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Raleway", sans-serif;
  user-select: none;
  min-width: 42vmin;
  font-size: 2vmin;
  display: none;
}

#container {
  height: 73vmin;
  width: 139.41vmin;
  cursor: default;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 150vmin;
  flex-wrap: wrap;
  margin: 2vmin;
  position: relative;
  display: none;
}

#menu {
  display: flex;
  flex-direction: row;
  gap: 3vmin;
  flex-wrap: wrap;
  height: 68.15vmin;
}

/* FIRST COLUMN START */

#menu #first-column {
  width: 36vmin;
  background: var(--columnsBackground);
  padding: 1.85vmin;
  display: flex;
  flex-direction: column;
  gap: 1.79vmin;
  border-radius: 1.38vmin;
  flex: 1 1 27.7vmin;
  height: 72.56vmin;
}

#menu #first-column hr {
  border: 1px solid var(--hrRowColor);
  width: 80%;
  margin: 0 auto;
}

#bankcard {
  height: 16.6vmin;
  width: 25vmin;
  margin: 0 auto;
  background: var(--bankCardBackgroundColor);
  border-radius: 1.38vmin;
  padding: 1.85vmin 1.6vmin 0px 1.85vmin;
  position: relative;
}

#bankcard .content p {
  margin: 0;
  color: var(--bankCardTextColor);
  font-size: 0.92vmin;
}

#bankcard .title {
  display: flex;
  justify-content: space-between;
}

#bankcard .title span {
  color: var(--bankCardTextColor);
}

#bankcard .title svg {
  width: 4.16vmin;
  height: 4.16vmin;
}

#bankcard .content {
  position: absolute;
  bottom: 1.66vmin;
}

#bankcard .content .card-data {
  display: flex;
  justify-content: space-between;
}

#bankcard .content .cardnumber {
  color: var(--bankCardNumberColor);
  font-size: 1.85vmin;
  letter-spacing: 0.37vmin;
  margin-bottom: 0.46vmin;
}

#bankcard .content .card-data:nth-child(2) {
  margin-right: 0.27vmin;
}

#bankcard .content .card-data p {
  font-size: 1.48vmin;
}

#your-money-panel #money-containers {
  display: flex;
  flex-direction: column;
  gap: 0.92vmin;
}

.exit-button,
.accept-button,
#pincode-container .button-groups button {
  background-color: var(--buttonBackgroundColor);
  padding: 1.85vmin 1.85vmin;
  /*width: 35vmin;*/
  width: 100%;
  outline: none;
  border: none;
  color: var(--acceptExitButtonColorText);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: bold;
  border-radius: 0.27vmin;
  font-family: "Raleway", sans-serif;
  font-size: 1.5vmin;
}

.exit-button:hover,
#modal-container #title-container button:hover {
  background-color: var(--exitButtonHoverBackgroundColor);
  box-shadow: -1vmin 1vmin 1vmin -1vmin var(--exitButtonHoverBackgroundColor);
}

.accept-button:hover {
  background-color: var(--acceptButtonHoverBackgroundColor);
  box-shadow: -1vmin 1vmin 1vmin -1vmin var(--acceptButtonHoverBackgroundColor);
}

/* FIRST COLUMN END */

/* SECOND COLUMN START*/

#menu #second-column {
  width: 35.18vmin;
  border-radius: 1.38vmin;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 0.92vmin;
  flex: 1 1 27.77vmin;
  padding-right: 1.85vmin;
  padding-left: 1.85vmin;
}

#withdraw,
#deposit,
#transfer {
  background: var(--columnsBackground);
  padding: 1.85vmin;
  border-radius: 1.38vmin;
  width: 35.18vmin;
}

#menu #second-column #withdraw {
  height: 18.48vmin;
}

#menu #second-column #deposit {
  height: 18.48vmin;
}

#menu #second-column #transfer {
  height: 26.63vmin;
}

/* SECOND COLUMN END*/

/* THIRD COLUMN START*/

#menu #third-column {
  width: 32.77vmin;
  background: var(--columnsBackground);
  border-radius: 1.38vmin;
  padding: 1.85vmin;
  flex: 1 1 37.03vmin;
}

#menu #third-column #graph-container {
  width: calc(100% - 0.46vmin);
  height: 38vmin;
}

#menu #third-column #buttons-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px 1.9vmin;
  margin-top: 1.48vmin;
  gap: 0.92vmin;
}

#menu #third-column #buttons-container button {
  padding: 0.92vmin;
  width: 20.92vmin;
}

#menu #third-column #buttons-container #more_history:hover {
  background-color: var(--moreHistoryButtonHoverColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--moreHistoryButtonHoverColor);
}

#menu #third-column #buttons-container #more_graph:hover {
  background-color: var(--moreGraphButtonHoverColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--moreGraphButtonHoverColor);
}

#menu #third-column #transactions-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.92vmin;
  padding: 1.85vmin;
}

#menu #third-column #transactions-container .transaction-container {
  /* width: 40.37vmin; */
  width: 100%;
  background-color: var(--transactionContainerBackgroundColor);
  padding: 0.92vmin 0.92vmin 0.92vmin 1.85vmin;
  font-size: 1.8vmin;
}

#menu
  #third-column
  #transactions-container
  .transaction-container
  .transaction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.5vmin;
  margin-bottom: 0.8vmin;
}

#menu
  #third-column
  #transactions-container
  .transaction-container
  .transaction
  span {
  color: var(--transactionDataTextColor);
  background: var(--columnsBackground);
  border-radius: 0.27vmin;
  padding: 0.46vmin;
  font-size: 1.2vmin;
}

/* THIRD COLUMN END*/

/* GENERAL START*/

.money-container,
.transaction-container {
  background-color: var(--moneyAndTransactionContainer);
  padding: 0.92vmin 0.92vmin 0.92vmin 1.85vmin;
  border-radius: 0.27vmin;
}

.money-container {
  background-color: var(--moneyContainerBackgroundColor);
}

.money-container h4 {
  margin-bottom: 0.5vmin;
}

.money-container h4,
.transaction h4 {
  color: var(--moneyAndTransactionallTitleColorText);
  text-shadow: 0.09vmin 0.09vmin 0.18vmin black;
  font-size: 1.5vmin;
  width: 25vmin;
}

.money-container span,
.transaction-container span {
  color: var(--yourMoneyPriceTextColor);
  text-shadow: 0.09vmin 0.09vmin 0.18vmin black;
  font-size: 1.5vmin;
}

.accept-button svg {
  height: 1.38vmin;
  fill: var(--checkmarkColor);
}

.green-text {
  color: var(--depositColorText) !important;
}

.red-text {
  color: var(--withDrawColorText) !important;
}

.disable-button {
  background-color: var(--disableButtonBackgroundColor) !important;
  pointer-events: none;
}

h3 {
  color: #ecedef;
  color: var(--allTitleColor);
  font-size: 2.31vmin;
  text-shadow: 0.09vmin 0.09vmin 0.92vmin rgb(255 255 255 / 47%);
  text-transform: uppercase;
}

p {
  color: var(--cardSubtitleTextColor);
  margin-bottom: 0.92vmin;
  font-size: 1.5vmin;
}

input {
  width: 100%;
  font-family: "Raleway", sans-serif;
  font-size: 1.38vmin;
  background-color: var(--inputBackgroundColor);
  padding: 1.85vmin 1.85vmin;
  outline: none;
  border: none;
  color: var(--inputTextColor);
  margin-bottom: 1.85vmin;
  border-radius: 0.46vmin;
}

input::placeholder {
  color: var(--inputPlaceholderColor);
  opacity: 1;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* GENERAL END*/

#modal-container {
  background-color: var(--columnsBackground);
  position: absolute;
  top: 50%;
  height: 100%;
  width: 100%;
  z-index: 2;
  transform: translateY(-50%);
  display: none;
  border-radius: 1.38vmin;
}

#more-modal-container {
  height: 93%;
  padding: 0 6vmin;
}

#modal-container h3 {
  text-align: center;
  width: 100%;
  margin-top: 1vmin;
}

#modal-container #title-container button {
  background-color: var(--buttonBackgroundColor);
  padding: 1.85vmin 1.85vmin;
  transition: all 0.3s ease;
  font-weight: bold;
  border-radius: 0.27vmin 0.27vmin 0.27vmin 1.27vmin;
  font-family: "Raleway", sans-serif;
  font-size: 1.5vmin;
  border: none;
  color: var(--inputTextColor);
  margin-left: auto;
  position: absolute;
  right: 0vmin;
  top: 0vmin;
  width: 14vmin;
}

/*DATA TABLES DESIGN */

.dataTables_wrapper .dataTables_info {
  clear: both;
  float: left;
  padding-top: 0.755em;
  color: var(--tableAllTextColor) !important;
}

#example_wrapper {
  margin-top: 4vmin;
  color: var(--tableAllTextColor) !important;
  padding: 1vmin;
  font-family: "Raleway", sans-serif !important;
}

#example_filter input {
  display: flex;
  gap: 1vmin;
  border: none;
  padding: 2vmin;
  background-color: var(--tableSearchInputBackgroundColor);
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
  color: var(--tableAllTextColor) !important;
}

table.dataTable thead > tr > th.sorting,
table.dataTable thead > tr > th.sorting_asc,
table.dataTable thead > tr > th.sorting_desc,
table.dataTable thead > tr > th.sorting_asc_disabled,
table.dataTable thead > tr > th.sorting_desc_disabled,
table.dataTable thead > tr > td.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting_asc_disabled,
table.dataTable thead > tr > td.sorting_desc_disabled,
table.dataTable thead > tr > td.sorting_disabled {
  cursor: pointer;
  position: relative;
  padding-right: 26px;
  color: var(--tableAllTextColor) !important;
}

table.dataTable tbody tr td {
  color: var(--tableAllTextColor);
  font-weight: 500;
}

.greenTableText {
  color: var(--depositColorText) !important;
}

.redTableText {
  color: var(--withDrawColorText) !important;
}

table.dataTable.no-footer {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  cursor: default;
  color: var(--tableAllTextColor) !important;
  border: 1px solid #828282;
  background: transparent;
  box-shadow: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  background: none;
  color: var(--tableAllTextColor) !important;
  border-radius: 4px;
  border: 1px solid #828282;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  color: white !important;
  background: var(--tableBackgroundColorPaginationButtonHover) !important;
  background: var(--tableBackgroundColorPaginationButtonHover) !important;
  background: var(--tableBackgroundColorPaginationButtonHover) !important;
  background: var(--tableBackgroundColorPaginationButtonHover) !important;
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--tableBackgroundColorPaginationButtonHover) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--tableBackgroundColorPaginationButton) !important;
  background: var(--tableBackgroundColorPaginationButton) !important;
  background: var(--tableBackgroundColorPaginationButton) !important;
  background: var(--tableBackgroundColorPaginationButton) !important;
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--tableBackgroundColorPaginationButton) !important;
  color: var(--tableAllTextColor) !important;
}

.dataTables_wrapper .dataTables_length select {
  border-radius: 3px;
  padding: 5px;
  background-color: var(--tableSelectBoxBackgroundColor) !important;
  padding: 4px;
  color: var(--tableAllTextColor) !important;
  padding: 1vmin;
  border: none;
  outline: none;
}

table.dataTable tbody tr {
  height: 50px !important;
}

table.dataTable tbody tr:hover {
  background-color: var(--tableRowHoverBackgroundColor) !important;
}

#example_wrapper
  > div.dataTables_scroll
  > div.dataTables_scrollHead
  > div
  > table
  > thead
  > tr
  > th.sorting_disabled {
  color: var(--tableAllTextColor) !important;
}

/*DATA TABLE DESIGN END*/

/*SCROLLBAR DESIGN*/
::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background: var(--tableScrollBarBackgroundColor2);
}
::-webkit-scrollbar-thumb {
  background: var(--tableScrollBarBackgroundColor);
}
::-webkit-scrollbar-thumb:hover {
  background: var(--tableScrollBarBackgroundColorHover);
}

/* LOADING CIRCLE*/

:root {
  --rotation-animation-speed: 2s;
  --rotation-animation-easing: linear;
  --stroke-animation-speed: 1.5s;
  --stroke-animation-easing: ease-in-out;
  --stroke-width: 3px;
  --stroke-start-dasharray: 1, 200;
  --stroke-end-dasharray: 89, 200;
}

.loader {
  margin: 0px auto;
  width: 23.14vmin;
  height: 23.14vmin;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  display: none;
}

.circular-loader {
  animation: rotate var(--rotation-animation-speed)
    var(--rotation-animation-easing) infinite;
}

.loader-path {
  fill: none;
  stroke-width: var(--stroke-width);
  animation: animate-stroke var(--stroke-animation-speed)
    var(--stroke-animation-easing) infinite;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes animate-stroke {
  0% {
    stroke-dasharray: var(--stroke-start-dasharray);
    stroke-dashoffset: 0;
    stroke: var(--columnsBackground);
  }
  50% {
    stroke-dasharray: var(--stroke-end-dasharray);
    stroke-dashoffset: -35;
    stroke: var(--loadingCircle);
  }
  100% {
    stroke-dasharray: var(--stroke-end-dasharray);
    stroke-dashoffset: -124;
    stroke: var(--loadingCircle);
  }
}

#pincode-container {
  background-color: var(--columnsBackground);
  height: 80%;
  width: 40vmin;
  color: white;
  display: flex;
  border-radius: 3vmin;
  flex-direction: column;
  justify-content: center;
  gap: 5vmin;
}

#pincode-container h4 {
  text-align: center;
  margin-top: 2vmin;
}

#pincode-container .pincode-input {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 15vmin;
  width: 100%;
  gap: 10px;
}

#pincode-container .pincode-input span {
  display: block;
  width: 3vmin;
  height: 3vmin;
  border-radius: 50%;
  border: 2px solid var(--secondary-color);
  transition: all 0.2s ease;
}

#pincode-container .pincode-numbers {
  display: grid;
  grid-template-columns: 10vmin 10vmin 10vmin;
  grid-template-rows: 10vmin 10vmin 10vmin;
  place-items: center;
  justify-content: center;
}

#pincode-container .pincode-numbers div {
  background-color: var(--background-light);
  padding: 2vmin 2vmin;
  border-radius: 50%;
  width: 3vmin;
  height: 3vmin;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

#pincode-container .pincode-numbers div:hover {
  background-color: var(--secondary-color);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--secondary-color);
}

#pincode-container .pincode-numbers div:nth-child(11):hover {
  background-color: var(--exitButtonHoverBackgroundColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--exitButtonHoverBackgroundColor);
}

#pincode-container .pincode-numbers div:nth-child(12):hover {
  background-color: var(--acceptButtonHoverBackgroundColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--acceptButtonHoverBackgroundColor);
}

#pincode-container .button-groups {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2vmin;
  margin-top: 2vmin;
}

#pincode-container .button-groups button {
  width: 80%;
}

#pincode-container .button-groups button:hover {
  background-color: var(--exitButtonHoverBackgroundColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--exitButtonHoverBackgroundColor);
}

#pincode-container .button-groups .login:hover {
  background-color: var(--acceptButtonHoverBackgroundColor);
  box-shadow: 0px 3.42vmin 9.25vmin -1.2vmin var(--acceptButtonHoverBackgroundColor);
}

.circleBackground {
  background-color: var(--secondary-color);
}

/* ATM DESIGNS*/

#atm-container {
  display: grid;
  grid-template-columns: 60% 60%;
  gap: 3vmin;
  height: 58vh;
}

.firstGridColumn,
.secondGridColumn {
  display: flex;
  flex-direction: column;
  padding: 1.85vmin;
}

.firstGridColumn {
  gap: 1.79vmin;
  border-radius: 1.38vmin;
  background: var(--columnsBackground);
}

.secondGridColumn {
  gap: 3vmin;
  justify-content: center;
}

.secondGridColumn div {
  background: var(--columnsBackground);
  padding: 3vmin;
  border-radius: 1.38vmin;
}

/*MEDIA QUERY*/
@media only screen and (max-width: 1280px) {
  .dataTables_scrollBody {
    max-height: 310px !important;
  }
}
