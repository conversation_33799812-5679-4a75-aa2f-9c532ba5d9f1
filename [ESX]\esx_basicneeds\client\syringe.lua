local isReviving = false

RegisterNetEvent('esx_basicneeds:playReviveAnimation')
AddEventHandler('esx_basicneeds:playReviveAnimation', function()
    local playerPed = PlayerPedId()
    isReviving = true

    -- Load animation dictionary
    RequestAnimDict('mini@cpr@char_a@cpr_str')
    while not HasAnimDictLoaded('mini@cpr@char_a@cpr_str') do
        Citizen.Wait(100)
    end

    -- Play animation
    TaskPlayAnim(playerPed, 'mini@cpr@char_a@cpr_str', 'cpr_pumpchest', 8.0, -8.0, 6000, 0, 0, false, false, false)

    -- Create syringe prop
    local coords = GetEntityCoords(playerPed)
    local prop = CreateObject(GetHashKey('prop_syringe_01'), coords.x, coords.y, coords.z + 0.2, true, true, true)
    AttachEntityToEntity(prop, playerPed, GetPedBoneIndex(playerPed, 57005), 0.13, 0.02, 0.0, -60.0, 0.0, 0.0, true, true, false, true, 1, true)

    -- Add particle effects
    UseParticleFxAssetNextCall('core')
    local effect = StartParticleFxLoopedOnEntity('ent_sht_electrical_box', prop, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, false, false, false)

    Citizen.Wait(6000)

    -- Clean up
    DeleteObject(prop)
    StopParticleFxLooped(effect, 0)
    ClearPedTasks(playerPed)
    isReviving = false
end)

-- Add target functionality
CreateThread(function()
    while true do
        Citizen.Wait(0)
        if not isReviving then
            if IsControlJustPressed(0, 47) then -- G key
                local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                if closestPlayer ~= -1 and closestDistance <= 3.0 then
                    local targetPed = GetPlayerPed(closestPlayer)
                    if IsEntityDead(targetPed) then
                        TriggerServerEvent('esx_basicneeds:setReviveTarget', GetPlayerServerId(closestPlayer))
                    end
                end
            end
        end
    end
end)