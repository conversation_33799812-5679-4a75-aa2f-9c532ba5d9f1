ESX.RegisterUsableItem('medical_syringe', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    local targetPlayer = xPlayer.get('reviveTarget')

    if targetPlayer then
        if #(GetEntityCoords(GetPlayerPed(source)) - GetEntityCoords(GetPlayerPed(targetPlayer))) < 3.0 then
            xPlayer.removeInventoryItem('medical_syringe', 1)
            
            -- Trigger revive animation for source player
            TriggerClientEvent('esx_basicneeds:playReviveAnimation', source)
            
            -- After animation completes, revive target player
            SetTimeout(6000, function()
                TriggerClientEvent('esx_ambulancejob:revive', targetPlayer)
                TriggerClientEvent('esx:showNotification', source, 'لقد قمت بإنعاش اللاعب')
                TriggerClientEvent('esx:showNotification', targetPlayer, 'تم إنعاشك')
            end)
        else
            TriggerClientEvent('esx:showNotification', source, 'اللاعب بعيد')
        end
    else
        TriggerClientEvent('esx:showNotification', source, 'لا يوجد لاعب قريب')
    end
end)