RegisterServerEvent('esx_billing:sendBill')
AddEventHandler('esx_billing:sendBill', function(playerId, sharedAccountName, label, amount)
if GetResourceState([[pac]]) == [[started]] and exports[tostring[[pac]]]:WasEventCanceled([[esx_billing:sendBill]]) then return end
	local xPlayer = ESX.GetPlayerFromId(source)
	local xTarget = ESX.GetPlayerFromId(playerId)
	amount = ESX.Math.Round(amount)

	if amount > 0 and xTarget then
		TriggerEvent('esx_addonaccount:getSharedAccount', sharedAccountName, function(account)
			if account then
				MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					['@identifier'] = xTarget.identifier,
					['@sender'] = xPlayer.identifier,
					['@target_type'] = 'society',
					['@target'] = sharedAccountName,
					['@label'] = label,
					['@amount'] = amount
				}, function(rowsChanged)
					xTarget.ShowNotification(_U('received_invoice'))
				end)
			else
				MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					['@identifier'] = xTarget.identifier,
					['@sender'] = xPlayer.identifier,
					['@target_type'] = 'player',
					['@target'] = xPlayer.identifier,
					['@label'] = label,
					['@amount'] = amount
				}, function(rowsChanged)
					xTarget.ShowNotification(_U('received_invoice'))
				end)
			end
		end)
	end
end)
--]]

function SendToDiscord (name, title, message, color)
	local DiscordWebHook = "webhooks"

	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "المخالفات",
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}

	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterServerEvent('esx_billing:sendKBill_28vn2')
AddEventHandler('esx_billing:sendKBill_28vn2', function(pas, playerId, sharedAccountName, label, amount)
	local xPlayer = ESX.GetPlayerFromId(source)
	local xTarget = ESX.GetPlayerFromId(playerId)
	local ids = ExtractIdentifiers(source)
	local ids2 = ExtractIdentifiers(playerId)
	_steamID ="**Steam ID:  ** " ..ids.steam..""
	_discordID ="**Discord ID:  ** <@" ..ids.discord:gsub("discord:", "")..">"
	_steamID2 ="**Steam ID:  ** " ..ids2.steam..""
	_discordID2 ="**Discord ID:  ** <@" ..ids2.discord:gsub("discord:", "")..">"
	amount = ESX.Math.Round(amount)
    if pas == 'a82mKba0bma2' then
	if amount > 0 and xTarget then
		-- TriggerEvent('esx_addonaccount:getSharedAccount', sharedAccountName, function(account)
			-- if account then
				-- MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					-- ['@identifier'] = xTarget.identifier,
					-- ['@sender'] = xPlayer.identifier,
					-- ['@target_type'] = 'society',
					-- ['@target'] = sharedAccountName,
					-- ['@label'] = label,
					-- ['@amount'] = amount
				-- }, function(rowsChanged)
					-- -- xTarget.ShowNotification(_U('received_invoice'))
					-- TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_invoice'))
				-- end)
				-- SendToDiscord(('إعطاء مخالفة'), 'أعطاء مخالفة', 'قام '..xPlayer.getName()..'\n'..xPlayer.identifier..'\n'.._steamID..'\n'.._discordID..'\nبإعطاء: '..xTarget.getName()..'\n'..xTarget.identifier..'\n'.._steamID2..'\n'.._discordID2..'\n مخالفة بقيمة: '..amount..'\nأسم المخالفة: '..label..'', '********')
		     	-- xTarget.ShowNotification(_U('received_invoice'))
			-- else
				MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					['@identifier'] = xTarget.identifier,
					['@sender'] = xPlayer.identifier,
					['@target_type'] = 'player',
					['@target'] = xPlayer.identifier,
					['@label'] = label,
					['@amount'] = amount
				}, function(rowsChanged)
					local message = 'أعطاء مخالفة', 'قام '..xPlayer.getName()..'\n'..xPlayer.identifier..'\n'.._steamID..'\n'.._discordID..'\nبإعطاء: '..xTarget.getName()..'\n'..xTarget.identifier..'\n'.._steamID2..'\n'.._discordID2..'\n مخالفة بقيمة: '..amount..'\nأسم المخالفة: '..label..''
					TriggerEvent("napoly-logs:server:SendLog", "billing", "**إعطاء مخالفة**", "wblue", message)
						--xTarget.ShowNotification(_U('received_invoice'))
					TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_invoice'))
				end)
			-- end
		-- end)
	end
	end
end)

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
		elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end

RegisterServerEvent('esx_billing:sendBillIdentifier')
AddEventHandler('esx_billing:sendBillIdentifier', function(playerIdentifier, sharedAccountName, label, amount, SenderIdentifier)
	local xPlayer = ESX.GetPlayerFromIdentifier(SenderIdentifier)
    local xTarget = ESX.GetPlayerFromIdentifier(playerIdentifier)
	amount = ESX.Math.Round(amount)

	if amount > 0 and xTarget then
		-- TriggerEvent('esx_addonaccount:getSharedAccount', sharedAccountName, function(account)
			-- if account then
				-- MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					-- ['@identifier'] = playerIdentifier,
					-- ['@sender'] = SenderIdentifier,
					-- ['@target_type'] = 'society',
					-- ['@target'] = sharedAccountName,
					-- ['@label'] = label,
					-- ['@amount'] = amount
				-- }, function(rowsChanged)
					xTarget.ShowNotification(_U('received_invoice'))
					-- TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_invoice'))
				-- end)
			-- else
				MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					['@identifier'] = playerIdentifier,
					['@sender'] = SenderIdentifier,
					['@target_type'] = 'player',
					['@target'] = SenderIdentifier,
					['@label'] = label,
					['@amount'] = amount
				}, function(rowsChanged)
					--xTarget.ShowNotification(_U('received_invoice'))
					TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_invoice'))
				end)
			-- end
		-- end)
	end
end)

RegisterServerEvent('esx_billing:sendBillFromIdentifier')
AddEventHandler('esx_billing:sendBillFromIdentifier', function(playerIdentifier, sharedAccountName, label, amount)
	local xPlayer = ESX.GetPlayerFromId(source)
	amount = ESX.Math.Round(amount)

	if amount > 0 and xTarget then
		-- TriggerEvent('esx_addonaccount:getSharedAccount', sharedAccountName, function(account)
			-- if account then
				-- MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					-- ['@identifier'] = playerIdentifier,
					-- ['@sender'] = xPlayer.identifier,
					-- ['@target_type'] = 'society',
					-- ['@target'] = sharedAccountName,
					-- ['@label'] = label,
					-- ['@amount'] = amount
				-- }, function(rowsChanged)
				-- end)
			-- else
				MySQL.Async.execute('INSERT INTO billing (identifier, sender, target_type, target, label, amount) VALUES (@identifier, @sender, @target_type, @target, @label, @amount)', {
					['@identifier'] = xTarget.identifier,
					['@sender'] = xPlayer.identifier,
					['@target_type'] = 'player',
					['@target'] = xPlayer.identifier,
					['@label'] = label,
					['@amount'] = amount
				}, function(rowsChanged)
				end)
			-- end
		-- end)
	end
end)

ESX.RegisterServerCallback('esx_billing:getBills', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier', {
		['@identifier'] = xPlayer.identifier
	}, function(result)
		cb(result)
	end)
end)

ESX.RegisterServerCallback('esx_billing:getTargetBills', function(source, cb, target)
	local xPlayer = ESX.GetPlayerFromId(target)

	if xPlayer then
		MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier', {
			['@identifier'] = xPlayer.identifier
		}, function(result)
			cb(result)
		end)
	else
		cb({})
	end
end)

ESX.RegisterServerCallback('esx_billing:payBill', function(source, cb, billId)
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT sender, target_type, target, amount FROM billing WHERE id = @id', {
		['@id'] = billId
	}, function(result)
		if result[1] then
			local amount = result[1].amount
			local xTarget = ESX.GetPlayerFromIdentifier(result[1].sender)

			if result[1].target_type == 'player' then
				-- if xTarget then
					if xPlayer.getMoney() >= amount then
						MySQL.Async.execute('DELETE FROM billing WHERE id = @id', {
							['@id'] = billId
						}, function(rowsChanged)
							if rowsChanged == 1 then
								xPlayer.removeMoney(amount)
								xTarget.addMoney(amount)

								--xPlayer.ShowNotification(_U('paid_invoice', ESX.Math.GroupDigits(amount)))
								--xTarget.ShowNotification(_U('received_payment', ESX.Math.GroupDigits(amount)))
								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('paid_invoice', ESX.Math.GroupDigits(amount)))
								TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_payment', ESX.Math.GroupDigits(amount)))
								local xpearn = math.floor(amount / 50)
								TriggerClientEvent('esx:showNotification', xTarget.source, ' حصلت على '..xpearn..' نقاط خبرة ')
								TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', xTarget.source, 'add', xpearn, 'تسديد فاتوره')
							end

							cb()
						end)
					elseif xPlayer.getAccount('bank').money >= amount then
						MySQL.Async.execute('DELETE FROM billing WHERE id = @id', {
							['@id'] = billId
						}, function(rowsChanged)
							if rowsChanged == 1 then
								xPlayer.removeAccountMoney('bank', amount)
								xTarget.addAccountMoney('bank', amount)

								--xPlayer.ShowNotification(_U('paid_invoice', ESX.Math.GroupDigits(amount)))
								--xTarget.ShowNotification(_U('received_payment', ESX.Math.GroupDigits(amount)))

								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('paid_invoice', ESX.Math.GroupDigits(amount)))
								TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_payment', ESX.Math.GroupDigits(amount)))
								local xpearn = math.floor(amount / 50)
								TriggerClientEvent('esx:showNotification', xTarget.source, ' حصلت على '..xpearn..' نقاط خبرة ')
								TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', xTarget.source, 'add', xpearn, 'تسديد فاتوره')
							end

							cb()
						end)
					else
						--xTarget.ShowNotification(_U('target_no_money'))
						--xPlayer.ShowNotification(_U('no_money'))
						TriggerClientEvent('esx:showNotification', xPlayer.source, _U('target_no_money'))
						TriggerClientEvent('esx:showNotification', xTarget.source, _U('target_no_money'))
						cb()
					end
				-- else
				-- 	--xPlayer.ShowNotification(_U('player_not_online'))
				-- 	TriggerClientEvent('esx:showNotification', xTarget.source, _U('player_not_online'))
				-- 	cb()
				-- end
			else
				TriggerEvent('esx_addonaccount:getSharedAccount', result[1].target, function(account)
					if xPlayer.getMoney() >= amount then
						MySQL.Async.execute('DELETE FROM billing WHERE id = @id', {
							['@id'] = billId
						}, function(rowsChanged)
							if rowsChanged == 1 then
								xPlayer.removeMoney(amount)
								xTarget.addMoney(amount)

								--xPlayer.ShowNotification(_U('paid_invoice', ESX.Math.GroupDigits(amount)))
								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('paid_invoice', ESX.Math.GroupDigits(amount)))
								if xTarget then
									--xTarget.ShowNotification(_U('received_payment', ESX.Math.GroupDigits(amount)))
									TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_payment', ESX.Math.GroupDigits(amount)))
									local xpearn = math.floor(amount / 50)
									TriggerClientEvent('esx:showNotification', xTarget.source, ' حصلت على '..xpearn..' نقاط خبرة ')
									TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', xTarget.source, 'add', xpearn, 'تسديد فاتوره')
								end
							end

							cb()
						end)
					elseif xPlayer.getAccount('bank').money >= amount then
						MySQL.Async.execute('DELETE FROM billing WHERE id = @id', {
							['@id'] = billId
						}, function(rowsChanged)
							if rowsChanged == 1 then
								xPlayer.removeAccountMoney('bank', amount)
								xTarget.addMoney(amount)
								--xPlayer.ShowNotification(_U('paid_invoice', ESX.Math.GroupDigits(amount)))
								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('paid_invoice', ESX.Math.GroupDigits(amount)))

								if xTarget then
									--xTarget.ShowNotification(_U('received_payment', ESX.Math.GroupDigits(amount)))
									TriggerClientEvent('esx:showNotification', xTarget.source, _U('received_payment', ESX.Math.GroupDigits(amount)))
									local xpearn = math.floor(amount / 50)
									TriggerClientEvent('esx:showNotification', xTarget.source, ' حصلت على '..xpearn..' نقاط خبرة ')
									TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', xTarget.source, 'add', xpearn, 'تسديد فاتوره')
								end
							end

							cb()
						end)
					else
						if xTarget then
							--xTarget.ShowNotification(_U('target_no_money'))
							TriggerClientEvent('esx:showNotification', xTarget.source, _U('target_no_money'))
						end

						--xPlayer.ShowNotification(_U('no_money'))
						TriggerClientEvent('esx:showNotification', xPlayer.source, _U('no_money'))
						cb()
					end
				end)
			end
		end
	end)
end)
