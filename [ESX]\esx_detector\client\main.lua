local Keys = {
  ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
  ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
  ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
  ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
  ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
  ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
  ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
  ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
}

--- action functions
local CurrentAction           = nil
local CurrentActionMsg        = ''
local CurrentActionData       = {}
local HasAlreadyEnteredMarker = false
local LastZone                = nil

--- esx
local GUI = {}
GUI.Time                      = 0
local PlayerData              = {}

Citizen.CreateThread(function ()
  PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
  PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
  PlayerData.job = job
end)

----markers
AddEventHandler('esx_detector:hasEnteredMarker', function (zone)
  if zone ~= nil then
    CurrentAction     = 'detector'
    CurrentActionMsg  = _U('press_e')
    CurrentActionData = {}
  end
end)

AddEventHandler('esx_detector:hasExitedMarker', function (zone)
  CurrentAction = nil
end)

--keycontrols
Citizen.CreateThread(function ()
	--local x,y,z = table.unpack(GetEntityCoords(PlayerPedId(), false))
	local found = false
	
	while true do
		if CurrentAction then
			--print('# CurrentAction')
			Citizen.Wait(0)
			found = false
			
			for i=1, #Config.Weapons, 1 do
				local playerPed = PlayerPedId()
				local weaponHash = GetHashKey(Config.Weapons[i].name)
			
				if HasPedGotWeapon(playerPed,  weaponHash,  false) then
					--nothings happens if police
					if PlayerData.job ~= nil and PlayerData.job.name == 'police' then
					elseif PlayerData.job ~= nil and PlayerData.job.name == 'admin' then
					elseif PlayerData.job ~= nil and PlayerData.job.name == 'agent' then
					elseif PlayerData.job ~= nil and PlayerData.job.name == 'army' then
						--
					else
						found = true
						--print(Config.Weapons[i].name .. ' - ' .. GetPlayerName(id))
					end
				end
			end
			
			if found then
				--[[sends message to police
				local coords      = GetEntityCoords(PlayerPedId())
				TriggerServerEvent('esx_phone:send', 'police', _U('police_message'), true, {
					x = coords.x,
					y = coords.y,
					z = coords.z
					})
				]]
				
				--sends message to client
				sendNotification(_U('busted'), 'error', 7000)
				
				--makes sound to everyone around him
				TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 12, "alarm", 0.1)
				
				Citizen.Wait(1000*60)
			else
				Citizen.Wait(3000)
			end
		else
			Citizen.Wait(1000)
		end
	end
end)

--[[Display markers
Citizen.CreateThread(function ()
  while true do
    Wait(0)

    local coords = GetEntityCoords(PlayerPedId())

    for k,v in pairs(Config.Zones) do
      if(v.Type ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < Config.DrawDistance) then
        DrawMarker(v.Type, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0, 0.0, 0.0, v.Size.x, v.Size.y, v.Size.z, v.Color.r, v.Color.g, v.Color.b, 100, false, true, 2, false, false, false, false)
      end
    end
  end
end)]]

-- Enter / Exit marker events
Citizen.CreateThread(function ()
	while true do
		Citizen.Wait(200)
	
		local coords      = GetEntityCoords(PlayerPedId())
		local currentZone = nil
		local isInMarker  = false
		
		for k,v in pairs(Config.Zones) do
			if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < v.Size.x) then
				isInMarker  = true
				currentZone = k
			end
		end
	
		if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
			HasAlreadyEnteredMarker = true
			LastZone = currentZone
			TriggerEvent('esx_detector:hasEnteredMarker', currentZone)
		end
	
		if not isInMarker and HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = false
			TriggerEvent('esx_detector:hasExitedMarker', LastZone)
		end
	end
end)

---- FUNCTIONS ----
function Notify(text)
	SetNotificationTextEntry('STRING')
	AddTextComponentString(text)
	DrawNotification(false, false)
end

function DisplayHelpText(str)
	SetTextComponentFormat("STRING")
	AddTextComponentString(str)
	DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

--notification
function sendNotification(message, messageType, messageTimeout)
	exports.pNotify:SetQueueMax("left", 1)
	TriggerEvent("pNotify:SendNotification", {
		--text = message,
		text = "<h1 color=white><center>جهاز كشف الأسلحة</center></h1>"..
		       "<font color=white size=4><p align=right><b>انت تحمل اسلحة ممنوعة ".."<font color=red> تم ابلاغ الجهات الأمنية",
		type = messageType,
		queue = "left",
		timeout = messageTimeout,
		layout = "CenterLeft",
		killer = true
	})
end