local show3DText = false
ESX = nil

Citizen.CreateThread(function()
	while ESX == nil do
		ESX = exports["es_extended"]:getSharedObject()
		Citizen.Wait(500)
	end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

end)

RegisterNetEvent("pixel_antiCL:show")
AddEventHandler("pixel_antiCL:show", function()
    if show3DText then
        show3DText = false
    else
        show3DText = true
        if Config.AutoDisableDrawing then
            if tonumber(Config.AutoDisableDrawing) then
                Citizen.Wait(Config.AutoDisableDrawingTime)
            else
                Citizen.Wait(15000)
            end
            show3DText = false
        end
    end
end)

local Cooldown_count = 0
	
local function Cooldown(sec)
	CreateThread(function()
		Cooldown_count = sec
		while Cooldown_count ~= 0 do
			Cooldown_count = Cooldown_count - 1
			Wait(1000)
		end	
		Cooldown_count = 0
	end)	
end

RegisterNetEvent("pixel_anticl")
AddEventHandler("pixel_anticl", function(id, crds, identifier, reason)
    Display(id, crds, identifier, reason)
end)

function Display(id, crds, identifier, reason)
    local displaying = true

    Citizen.CreateThread(function()
        Wait(Config.DrawingTime)
        displaying = false
    end)
    
    Citizen.CreateThread(function()
        while displaying do

            local sleep_disp = 1000

            local pcoords = GetEntityCoords(PlayerPedId())
            if GetDistanceBetweenCoords(crds.x, crds.y, crds.z, pcoords.x, pcoords.y, pcoords.z, true) < 5.0 then
                sleep_disp = 0
                
                -- رسم الدائرة تحت المعلومات
                DrawMarker(1, crds.x, crds.y, crds.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.1, 
                    Config.TextColor.r, Config.TextColor.g, Config.TextColor.b, 100, false, true, 2, false, nil, nil, false)
                
                DrawText3DSecond_DEFCON(crds.x, crds.y, crds.z+0.30, "<FONT FACE='A9eelsh'>[E] ﻂﻐﺿا تﺎﻣﻮﻠﻌﻤﻟا ﺦﺴﻨﻟ")
                DrawText3DSecond(crds.x, crds.y, crds.z+0.15, "<FONT FACE='A9eelsh'>ﻞﺼﻓ")
                DrawText3D(crds.x, crds.y, crds.z, id.." ("..identifier..")" .. "<FONT FACE='A9eelsh'>فﺮﻌﻤﻟا ﻢﻗﺭ \n"..reason.."ﺐﺒﺴﻟا ")
                
                if IsControlJustReleased(0, 38) then
                    if Cooldown_count == 0 then
                        SendNUIMessage({type = 'clipboard', data = 'رقم الأستيم  : ' .. identifier .. " | رقم المعرف : " .. id})
                        ESX.ShowNotification("تم نسخ المعلومات")
                        Cooldown(10)
                    else
                        ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية')
                    end
                end
            end
            Citizen.Wait(sleep_disp)
        end
    end)
end

function DrawText3DSecond(x,y,z, text)
    local onScreen,_x,_y=World3dToScreen2d(x,y,z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    SetTextScale(0.45, 0.45)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(Config.AlertTextColor.r, Config.AlertTextColor.g, Config.AlertTextColor.b, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
end

function DrawText3DSecond_DEFCON(x,y,z, text)
    local onScreen,_x,_y=World3dToScreen2d(x,y,z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    SetTextScale(0.45, 0.45)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(102, 178, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
end

function DrawText3D(x,y,z, text)
    local onScreen,_x,_y=World3dToScreen2d(x,y,z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    SetTextScale(0.45, 0.45)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(Config.TextColor.r, Config.TextColor.g, Config.TextColor.b, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
end
