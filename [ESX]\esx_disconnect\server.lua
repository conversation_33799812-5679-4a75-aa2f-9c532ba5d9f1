local webhook = "https://discord.com/api/webhooks/1368699778774335539/GI0PSi5tOtRQaR75g0OQlfVB1Rj9uKuGO3vRWcEUffy8bUMLIYODaZP5fe60T5UoKJbk"

-- إضافة جدول لتخزين معلومات الفصل المؤقتة
local disconnectMarkers = {}

-- إضافة أمر لعرض الفصل على الشاشة للمشرفين فقط
RegisterCommand("combat", function(source, args, rawcmd)
    TriggerClientEvent("pixel_antiCL:show", source)
end)

-- إضافة حدث عند فصل اللاعب
AddEventHandler("playerDropped", function(reason)
    local crds = GetEntityCoords(GetPlayerPed(source))
    local id = source
    local identifier = ""
    
    -- التحقق من استخدام الاسم الشخصي أو الاسم الشخصي للاعب
    if Config.UseSteam then
        identifier = GetPlayerIdentifier(source, 0)
    else
        identifier = GetPlayerIdentifier(source, 1)
    end
    
    -- تخزين معلومات الفصل
    table.insert(disconnectMarkers, {
        id = id,
        crds = crds,
        identifier = identifier,
        reason = reason,
        expireTime = os.time() + (Config.DrawingTime / 1000) -- وقت انتهاء العرض بالثواني
    })
    
    -- إرسال المعلومات للاعبين المتصلين
    TriggerClientEvent("pixel_anticl", -1, id, crds, identifier, reason)
    
    if Config.LogSystem then
        SendLog(id, crds, identifier, reason)
    end
end)

-- إضافة حدث للاعبين عند الاتصال
RegisterNetEvent("esx:playerLoaded")
AddEventHandler("esx:playerLoaded", function(source)
    local currentTime = os.time()
    
    -- إرسال معلومات الفصل النشطة فقط للاعب الجديد
    for i, info in ipairs(disconnectMarkers) do
        if currentTime < info.expireTime then
            TriggerClientEvent("pixel_anticl", source, info.id, info.crds, info.identifier, info.reason)
        end
    end
end)

-- تنظيف معلومات الفصل القديمة
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000) -- التحقق كل دقيقة
        local currentTime = os.time()
        local newDisconnectMarkers = {}
        
        for i, info in ipairs(disconnectMarkers) do
            if currentTime < info.expireTime then
                table.insert(newDisconnectMarkers, info)
            end
        end
        
        disconnectMarkers = newDisconnectMarkers
    end
end)

function SendLog(id, crds, identifier, reason)
    local name = GetPlayerName(id)
    local date = os.date('*t')
    print("id:"..id)
    print("X: "..crds.x..", Y: "..crds.y..", Z: "..crds.z)
    print("identifier:"..identifier)
    print("reason:"..reason)
    if date.month < 10 then date.month = '0' .. tostring(date.month) end
    if date.day < 10 then date.day = '0' .. tostring(date.day) end
    if date.hour < 10 then date.hour = '0' .. tostring(date.hour) end
    if date.min < 10 then date.min = '0' .. tostring(date.min) end
    if date.sec < 10 then date.sec = '0' .. tostring(date.sec) end
    local date = (''..date.day .. '.' .. date.month .. '.' .. date.year .. ' - ' .. date.hour .. ':' .. date.min .. ':' .. date.sec..'')
    local embeds = {
        {
            ["title"] = "Player Disconnected",
            ["type"]="rich",
            ["color"] = 4777493,
            ["fields"] = {
                {
                    ["name"] = "Identifier",
                    ["value"] = identifier,
                    ["inline"] = true,
                },{
                    ["name"] = "Nickname",
                    ["value"] = name,
                    ["inline"] = true,
                },{
                    ["name"] = "Player's ID",
                    ["value"] = id,
                    ["inline"] = true,
                },{
                    ["name"] = "Cordinates",
                    ["value"] = "X: "..crds.x..", Y: "..crds.y..", Z: "..crds.z,
                    ["inline"] = true,
                },{
                    ["name"] = "Reason",
                    ["value"] = reason,
                    ["inline"] = true,
                },
            },
            ["footer"]=  {
                ["icon_url"] = "https://forum.fivem.net/uploads/default/original/4X/7/5/e/75ef9fcabc1abea8fce0ebd0236a4132710fcb2e.png",
                ["text"]= "Sent: " ..date.."",
            },
        }
    }
    PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({ username = Config.LogBotName,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end
