<head>
  <link href="https://fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" type="text/css">
</head>

<body>
  <div class="full-screen">
    <div class="question-container">
      <!-- header -->
      <div class="header">
        <h1>اختبار رخصة القيادة مرور<font color=#2383DC> مقاطعة نابولي </font></h1>
      </div>
      <!-- home -->
      <div class="body home">
        <div class="content">
          <!-- ICI BREVE PRESENTATION -->
          <center><img src="dmv.png" class="logo"><br><p class="bold-text">مرحبا بك في معهد تعليم القيادة</center>
          <br><center>على جميع المواطنين إجتياز الأختبارات للحصول على رخصة قيادة.<br>اقرأ الأسئلة بعناية واختر الإجابة الصحيحة. تجنب الإجابة بشكل عشوائي<br><br>
          الأختبار النظري<br>
          - رسوم الأختبار النظري 200$ غير قابلة للإسترجاع في حالة الرسوب.<br>
          - يمكنك الدفع باستخدام بطاقة البنك. ولكن انتبه من ان يكون رصيدك غير كافي وتتحول إلى مدين للبنك.<br>
          - في حالة الرسوب في الأختبار النظري، عليك الانتظار لفترة من الوقت حتى تتمكن من التقديم مرة أخرى.<br><br>
          أختبار القيادة العملي<br>
          - رسم أختبار القيادة العملي 500$، غير قابلة للإسترجاع في حالة الرسوب.<br>
          - انتبه جيدا وتجنب المخالفات والحوادث حتى تتمكن من إجتياز الأختبار</p></center>
        </div>
        <div class="buttonspot">
        <a href="#" class="button btnQuestion">أبـــدا</a>
        </div>
        <div class ="barre-progression" >
          <h2>سير الأختبار</h2>
          <progress class="progression" value="0" max="10" >
        </div>
      </div>
      <!-- Question-->
      <div class="body questionnaire-container">
        <div class="content">
          <h2 id="questionNumero"></h2>
          <p id="question"></p>
          <form class="form" id="question-form">
            <div>
              <input type="radio" name="question" id="answerA" value="A">
              <label class="answerA"></label>
            </div>
            <div>
              <input type="radio" name="question" id="answerB" value="B">
              <label class="answerB"></label>
            </div>
            <div>
              <input type="radio" name="question" id="answerC" value="C">
              <label class="answerC"></label>
            </div>
            <div>
              <input type="radio" name="question" id="answerD" value="D">
              <label class="answerD"></label>
            </div>
              <br>
              <br>
              <br>
              <br>
                    <button type="submit" id="submit" class="submit">السؤال التالي</button>
                </form>
        </div>
        <div class ="barre-progression">
          <h2>سير الأختبار</h2>
          <progress class="progression" value="0" max="10" >
        </div>
      </div>
      <!-- Résults -->
      <div class="body resultGood">
        <div class="content">
<center><font color=Lime><p class="bold-text">مبروك</p></font><br><br><p>نجحت في الإختبار.<br><br>يمكن إغلاق هذه النافذه والإنتقال إلى إختبار القيادة العملي.</center>
        </div>
        <div class="buttonspot">
        <a href="#" class="button btnClose">إغلاق</a>
        </div>
        <div class ="barre-progression" >
          <h2>سير الأختبار</h2>
          <progress class="progression" value="10" max="10" >
        </div>
      </div>
      <div class="body resultBad">
        <div class="content">
<center><p class="bold-text"><font color=red>مع الأسف، أنت رسبت في الأختبار</font></p></center>
<center><p class="bold-text"><font color=#0094FF></font> <font color=black>راجع قوانين المرور</font></p></center>
<center><p class="bold-text"><br> و حاول مره أخرى</center>
          </div>
        <div class="buttonspot">
        <a href="#" class="button btnKick">اغلاق</a>
        </div>
        <div class ="barre-progression" >
          <h2>سير الأختبار</h2>
          <progress class="progression" value="10" max="10" >
        </div>
      </div>
    </div>
  </div>

  <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
  <script src="questions.js" type="text/javascript"></script>
  <script src="scripts.js" type="text/javascript"></script>
  <script src="debounce.min.js" type="text/javascript"></script>

</body>