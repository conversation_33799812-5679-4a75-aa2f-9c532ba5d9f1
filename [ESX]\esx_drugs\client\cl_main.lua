RegisterFontFile(Config['ESX'].fontid)
fontId = RegisterFontId(Config['ESX'].fontid)

local playerInService = false
local playerInWaiting = false
local inServiceTime = 0

local cokeQTE = 0
local PlayerData = {}
-- local LeoJobs = {'police', 'agent', 'ambulance'}
local currentPort = 1
local currentZones = Config.Zones[currentPort]
local coke_poochQTE = 0
local weedQTE = 0
local weed_poochQTE = 0
local methQTE = 0
local meth_poochQTE = 0
local opiumQTE = 0
local opium_poochQTE = 0
local myJob = nil
local HasAlreadyEnteredMarker = false
local LastZone = nil
local isInZone = false
local CurrentAction = nil
local CurrentActionMsg = ''
local CurrentActionData = {}

local cant_sell_drugs = false

-- متغيرات لتتبع حالة البيع لكل نوع من المخدرات
local isSellingWeed = false
local isSellingOpium = false
local isSellingCoke = false
local isSellingMeth = false

local drugsBlips = {}

Citizen.CreateThread(function()
    _2rayan.Functions.loadShared()
    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(500)
    end
    PlayerData = ESX.GetPlayerData()

    refreshBLips()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    PlayerData.job = job

    deleteBlips()
    refreshBLips()
end)

RegisterNetEvent('esx_misc:currentPortNum')
AddEventHandler('esx_misc:currentPortNum', function(port)
    -- print('# port number changed ['..port..']')
    currentPort = port
    currentZones = Config.Zones[currentPort]
    Citizen.Wait(1)
    deleteBlips()
    refreshBLips()
end)

RegisterNetEvent('esx_drugs:Update')
AddEventHandler('esx_drugs:Update', function(Update)
    cant_sell_drugs = Update
end)

AddEventHandler('esx_drugs:hasEnteredMarker', function(zone)
    -- print(zone, myJob)
    -- if myJob == 'police' or myJob == 'ambulance' or myJob == 'admin' or myJob == 'agent' then
    if myJob == 'police' or myJob == 'ambulance' or myJob == 'agent' then
        return
    end

    -- ESX.UI.Menu.CloseAll()

    if zone == 'LoginZone' then
        CurrentAction = zone
        CurrentActionMsg = _U('press_login')
        CurrentActionData = {}
    end

    if zone == 'exitMarker' then
        CurrentAction = zone
        CurrentActionMsg = _U('exit_marker')
        CurrentActionData = {}
    end

    if zone == 'CokeField' then
        CurrentAction = zone
        CurrentActionMsg = _U('press_collect_coke')
        CurrentActionData = {}
    end

    if zone == 'CokeProcessing' then
        -- if cokeQTE >= 5 then
        CurrentAction = zone
        CurrentActionMsg = _U('press_process_coke')
        CurrentActionData = {}
        -- end
    end

    if zone == 'CokeDealer' then
        -- لا تظهر الرسالة إذا كان اللاعب بالفعل يبيع الكوكايين
        if not isSellingCoke then
            CurrentAction = zone
            CurrentActionMsg = _U('press_sell_coke')
            CurrentActionData = {}
        end
    end

    if zone == 'MethField' then
        CurrentAction = zone
        CurrentActionMsg = _U('press_collect_meth')
        CurrentActionData = {}
    end

    if zone == 'MethProcessing' then
        -- if methQTE >= 5 then
        CurrentAction = zone
        CurrentActionMsg = _U('press_process_meth')
        CurrentActionData = {}
        -- end
    end

    if zone == 'MethDealer' then
        -- لا تظهر الرسالة إذا كان اللاعب بالفعل يبيع الميث
        if not isSellingMeth then
            CurrentAction = zone
            CurrentActionMsg = _U('press_sell_meth')
            CurrentActionData = {}
        end
    end

    if zone == 'WeedField' then
        CurrentAction = zone
        CurrentActionMsg = _U('press_collect_weed')
        CurrentActionData = {}
    end

    if zone == 'WeedProcessing' then
        -- if weedQTE >= 5 then
        CurrentAction = zone
        CurrentActionMsg = _U('press_process_weed')
        CurrentActionData = {}
        -- end
    end

    if zone == 'WeedDealer' then
        -- لا تظهر الرسالة إذا كان اللاعب بالفعل يبيع الحشيش
        if not isSellingWeed then
            CurrentAction = zone
            CurrentActionMsg = _U('press_sell_weed')
            CurrentActionData = {}
        end
    end

    if zone == 'OpiumField' then
        CurrentAction = zone
        CurrentActionMsg = _U('press_collect_opium')
        CurrentActionData = {}
    end

    if zone == 'OpiumProcessing' then
        -- if opiumQTE >= 5 then
        CurrentAction = zone
        CurrentActionMsg = _U('press_process_opium')
        CurrentActionData = {}
        -- end
    end

    if zone == 'OpiumDealer' then
        -- لا تظهر الرسالة إذا كان اللاعب بالفعل يبيع الأفيون
        if not isSellingOpium then
            CurrentAction = zone
            CurrentActionMsg = _U('press_sell_opium')
            CurrentActionData = {}
        end
    end
end)

AddEventHandler('esx_drugs:hasExitedMarker', function(zone)
    CurrentAction = nil
    ESX.UI.Menu.CloseAll()

    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'Stop')

    -- إعادة تعيين حالة البيع عند الخروج من منطقة البيع
    if zone == 'WeedDealer' then
        isSellingWeed = false
    elseif zone == 'OpiumDealer' then
        isSellingOpium = false
    elseif zone == 'CokeDealer' then
        isSellingCoke = false
    elseif zone == 'MethDealer' then
        isSellingMeth = false
    end
end)

-- Weed Effect
RegisterNetEvent('esx_drugs:onPot')
AddEventHandler('esx_drugs:onPot', function()
    RequestAnimSet("MOVE_M@DRUNK@SLIGHTLYDRUNK")
    while not HasAnimSetLoaded("MOVE_M@DRUNK@SLIGHTLYDRUNK") do
        Citizen.Wait(0)
    end
    TaskStartScenarioInPlace(GetPlayerPed(-1), "WORLD_HUMAN_SMOKING_POT", 0, true)
    Citizen.Wait(10000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    ClearPedTasksImmediately(GetPlayerPed(-1))
    SetTimecycleModifier("spectator5")
    SetPedMotionBlur(GetPlayerPed(-1), true)
    SetPedMovementClipset(GetPlayerPed(-1), "MOVE_M@DRUNK@SLIGHTLYDRUNK", true)
    SetPedIsDrunk(GetPlayerPed(-1), true)
    DoScreenFadeIn(1000)
    Citizen.Wait(600000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    DoScreenFadeIn(1000)
    ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(GetPlayerPed(-1), 0)
    SetPedIsDrunk(GetPlayerPed(-1), false)
    SetPedMotionBlur(GetPlayerPed(-1), false)
end)

-- Opium Effect
RegisterNetEvent('esx_drugs:onOpium')
AddEventHandler('esx_drugs:onOpium', function()
    RequestAnimSet("MOVE_M@DRUNK@SLIGHTLYDRUNK")
    while not HasAnimSetLoaded("MOVE_M@DRUNK@SLIGHTLYDRUNK") do
        Citizen.Wait(0)
    end
    TaskStartScenarioInPlace(GetPlayerPed(-1), "WORLD_HUMAN_SMOKING_POT", 0, true)
    Citizen.Wait(10000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    ClearPedTasksImmediately(GetPlayerPed(-1))
    SetTimecycleModifier("spectator5")
    SetPedMotionBlur(GetPlayerPed(-1), true)
    SetPedMovementClipset(GetPlayerPed(-1), "MOVE_M@DRUNK@SLIGHTLYDRUNK", true)
    SetPedIsDrunk(GetPlayerPed(-1), true)
    DoScreenFadeIn(1000)
    Citizen.Wait(600000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    DoScreenFadeIn(1000)
    ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(GetPlayerPed(-1), 0)
    SetPedIsDrunk(GetPlayerPed(-1), false)
    SetPedMotionBlur(GetPlayerPed(-1), false)
end)

-- Meth Effect
RegisterNetEvent('esx_drugs:onMeth')
AddEventHandler('esx_drugs:onMeth', function()
    RequestAnimSet("MOVE_M@DRUNK@SLIGHTLYDRUNK")
    while not HasAnimSetLoaded("MOVE_M@DRUNK@SLIGHTLYDRUNK") do
        Citizen.Wait(0)
    end
    TaskStartScenarioInPlace(GetPlayerPed(-1), "mp_player_intdrink", 0, true)
    Citizen.Wait(10000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    ClearPedTasksImmediately(GetPlayerPed(-1))
    SetTimecycleModifier("spectator5")
    SetPedMotionBlur(GetPlayerPed(-1), true)
    SetPedMovementClipset(GetPlayerPed(-1), "MOVE_M@DRUNK@SLIGHTLYDRUNK", true)
    SetPedIsDrunk(GetPlayerPed(-1), true)
    DoScreenFadeIn(1000)
    Citizen.Wait(600000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    DoScreenFadeIn(1000)
    ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(GetPlayerPed(-1), 0)
    SetPedIsDrunk(GetPlayerPed(-1), false)
    SetPedMotionBlur(GetPlayerPed(-1), false)
end)

-- Coke Effect
RegisterNetEvent('esx_drugs:onCoke')
AddEventHandler('esx_drugs:onCoke', function()
    RequestAnimSet("MOVE_M@DRUNK@SLIGHTLYDRUNK")
    while not HasAnimSetLoaded("MOVE_M@DRUNK@SLIGHTLYDRUNK") do
        Citizen.Wait(0)
    end
    TaskStartScenarioInPlace(GetPlayerPed(-1), "WORLD_HUMAN_SMOKING_POT", 0, true)
    Citizen.Wait(10000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    ClearPedTasksImmediately(GetPlayerPed(-1))
    SetTimecycleModifier("spectator5")
    SetPedMotionBlur(GetPlayerPed(-1), true)
    SetPedMovementClipset(GetPlayerPed(-1), "MOVE_M@DRUNK@SLIGHTLYDRUNK", true)
    SetPedIsDrunk(GetPlayerPed(-1), true)
    DoScreenFadeIn(1000)
    Citizen.Wait(600000)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    DoScreenFadeIn(1000)
    ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(GetPlayerPed(-1), 0)
    SetPedIsDrunk(GetPlayerPed(-1), false)
    SetPedMotionBlur(GetPlayerPed(-1), false)
end)

local draw = false
-- Render markers
function refreshDrawMarker()
    while ESX == nil or PlayerData == nil do
        Citizen.Wait(5)
    end

    local sleep = 0

    Citizen.CreateThread(function()
        while drugsBlips[1] ~= nil do
            local coords = GetEntityCoords(GetPlayerPed(-1))
            draw = false

            for k, v in pairs(currentZones) do
                local dist = GetDistanceBetweenCoords(coords, v.x, v.y, v.z, true)
                if dist < Config.DrawDistance then
                    if v.MarkerType ~= -1 then
                        DrawMarker(v.MarkerType, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0, 0.0, 0.0, v.ZoneSize.x, v.ZoneSize.y,
                            v.ZoneSize.z, v.MarkerColor.r, v.MarkerColor.g, v.MarkerColor.b, v.Opacity, false, true, 2,
                            v.Rotate, false, false, false)
                    end
                    draw = true
                end
            end

            for i = 1, #Config.LoginZone, 1 do
                local dist = GetDistanceBetweenCoords(coords, Config.LoginZone[i].x, Config.LoginZone[i].y,
                    Config.LoginZone[i].z, true)
                if dist < Config.DrawDistance then
                    DrawMarker(22, Config.LoginZone[i].x, Config.LoginZone[i].y, Config.LoginZone[i].z, 0.0, 0.0, 0.0,
                        0, 0.0, 0.0, Config.ZoneSize.x, Config.ZoneSize.y, Config.ZoneSize.z, 255, 0, 0, 50, false,
                        true, 2, false, false, false, false)

                    draw = true
                end
            end

            if draw then
                sleep = 1
            else
                sleep = 5
            end

            Citizen.Wait(sleep)
        end
    end)
end

-- Create blips
function refreshBLips(state)
    while ESX == nil or PlayerData.job == nil do
        Citizen.Wait(500)
    end

    if Config.LoginZoneBlip then
        local blip = AddBlipForCoord(Config.LoginZone[1].x, Config.LoginZone[1].y, Config.LoginZone[1].z)

        SetBlipSprite(blip, 429)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.9)
        SetBlipColour(blip, 59)
        SetBlipAsShortRange(blip, true)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(_U('login_name'))
        EndTextCommandSetBlipName(blip)
        -- table.insert(drugsBlips, blip)
    end

    if Config.ShowBlips then
        -- if not isLoeJob() then
        for k, v in pairs(currentZones) do
            if string.lower(string.sub(k, -6)) == 'dealer' and state or not state and string.lower(string.sub(k, -6)) ~=
                'dealer' then
                local blip = AddBlipForCoord(v.x, v.y, v.z)

                SetBlipSprite(blip, v.sprite)
                SetBlipDisplay(blip, 4)
                SetBlipScale(blip, 0.9)
                SetBlipColour(blip, v.color)
                SetBlipAsShortRange(blip, true)

                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString(v.name)
                EndTextCommandSetBlipName(blip)
                table.insert(drugsBlips, blip)
            end
        end
        -- end
    end
    refreshDrawMarker()
end

-- check if player job is leo
-- function isLoeJob()
--     while PlayerData == nil do
--         Citizen.Wait(500)
--     end

--     local check = false

--     for i = 1, #LeoJobs, 1 do
--         if PlayerData.job.name == LeoJobs[i] then
--             check = true
--             break
--         end
--     end

--     if check then
--         return true
--     else
--         return false
--     end
-- end

function deleteBlips()
    if drugsBlips[1] ~= nil then
        for i = 1, #drugsBlips, 1 do
            RemoveBlip(drugsBlips[i])
            drugsBlips[i] = nil
        end
    end
end

-- RETURN NUMBER OF ITEMS FROM SERVER
RegisterNetEvent('esx_drugs:ReturnInventory')
AddEventHandler('esx_drugs:ReturnInventory',
    function(cokeNbr, cokepNbr, methNbr, methpNbr, weedNbr, weedpNbr, opiumNbr, opiumpNbr, jobName, currentZone)
        cokeQTE = cokeNbr
        coke_poochQTE = cokepNbr
        methQTE = methNbr
        meth_poochQTE = methpNbr
        weedQTE = weedNbr
        weed_poochQTE = weedpNbr
        opiumQTE = opiumNbr
        opium_poochQTE = opiumpNbr
        myJob = jobName
        TriggerEvent('esx_drugs:hasEnteredMarker', currentZone)
    end)

-- Activate menu when player is inside marker
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        local coords = GetEntityCoords(GetPlayerPed(-1))
        local isInMarker = false
        local currentZone = nil

        for k, v in pairs(currentZones) do
            -- print(GetDistanceBetweenCoords(coords, v.x, v.y, v.z, true), GetDistanceBetweenCoords(coords, v.x, v.y, v.z, true) < Config.ZoneSize.x + 1.5, Config.ZoneSize.x, Config.ZoneSize, Config.ZoneSize.x, v.x, v.y, v.z, coords)
            if (GetDistanceBetweenCoords(coords, v.x, v.y, v.z, true) < Config.ZoneSize.x + 1.5) then
                isInMarker = true
                currentZone = k
                TriggerEvent('esx_drugs:hasEnteredMarker', currentZone)
            end
        end

        for i = 1, #Config.LoginZone, 1 do
            if (GetDistanceBetweenCoords(coords, Config.LoginZone[i].x, Config.LoginZone[i].y, Config.LoginZone[i].z,
                true) < Config.ZoneSize.x / 2) then
                isInMarker = true
                currentZone = 'LoginZone'
                TriggerEvent('esx_drugs:hasEnteredMarker', currentZone)
            end
        end

        if isInMarker and not hasAlreadyEnteredMarker then
            hasAlreadyEnteredMarker = true
            lastZone = currentZone
            TriggerServerEvent('esx_K20drugs:GetUserInventory_H', currentZone)
        end

        if not isInMarker and hasAlreadyEnteredMarker then
            hasAlreadyEnteredMarker = false
            TriggerEvent('esx_drugs:hasExitedMarker', lastZone)
        end

        if isInMarker and isInZone then
            TriggerEvent('esx_drugs:hasEnteredMarker', 'exitMarker')
        end
    end
end)

-- Key Controls
Citizen.CreateThread(function()
    while true do
        if draw then
            Citizen.Wait(10)
            if CurrentAction ~= nil then
                ESX.ShowHelpNotification(CurrentActionMsg)

                if IsControlJustReleased(0, _2rayan.Keys['E']) and IsPedOnFoot(PlayerPedId()) then
                    local data = exports.esx_misc:isNoCrimetime() -- return table with 3 values {active(boolen),location,label}
                    local data2 = exports.esx_misc:isNoCrimetime2() -- return table with 3 values {active(boolen),name,label}
                    -- local data3 = exports.esx_misc:restart_time() --return table with 3 values {active(boolen),name,label}

                    if data then
                        --print('# 1')
                         openLoginMenu()
                        ESX.ShowNotification('لايمكن العمل بالممنوعات اثناء اعلان</br><font color=B748E2>' .. data.label)
                        -- elseif data2 then
                        -- ESX.ShowNotification('لايمكن العمل بالممنوعات اثناء اعلان</br><font color=red>'..data.label)
                    else
                        if CurrentAction == 'LoginZone' then
                            openLoginMenu()
                        elseif CurrentAction == 'CokeField' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startHarvest', 'Coke')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'CokeProcessing' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startTransFromDrug', 'Coke')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'MethField' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startHarvest', 'Meth')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'MethProcessing' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startTransFromDrug', 'Meth')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'WeedField' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startHarvest', 'Weed')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'WeedProcessing' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startTransFromDrug', 'Weed')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'OpiumField' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startHarvest', 'Opium')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                        elseif CurrentAction == 'OpiumProcessing' then
                            TriggerEvent('esx_status:getStatus', 'drunk', function(status)
                                if status.val >= 250000 then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startTransFromDrug', 'Opium')
                                else
                                    ESX.ShowNotification(
                                        '<font color=red> يجب أن تكون في حالة سكر بنسبة %25 </font>')
                                end
                            end)
                      elseif data and data.active and data.location == 'sea_port_close' or data.location == 'internationa_close' or data.location == 'seaport_west_close' then
                        --print('# 2')
                        ESX.ShowNotification('لايمكن بيع الممنوعات اثناء اعلان</br><font color=gray>اغلاق '..data.label)
                      elseif data2 and data2.active and data2.location == 'NoCrimetime' or data2.location == 'SellDrugs' then
                        ESX.ShowNotification('لايمكن بيع الممنوعات اثناء اعلان</br><font color=red> '..data2.label)

                        else
                            if playerInService then
                                if CurrentAction == 'WeedDealer' and not isSellingWeed then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startSell', 'Weed', GetEntityCoords(PlayerPedId()))
                                    isSellingWeed = true -- تحديث حالة بيع الحشيش
                                elseif CurrentAction == 'OpiumDealer' and not isSellingOpium then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startSell', 'Opium', GetEntityCoords(PlayerPedId()))
                                    isSellingOpium = true -- تحديث حالة بيع الأفيون
                                elseif CurrentAction == 'CokeDealer' and not isSellingCoke then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startSell', 'Coke', GetEntityCoords(PlayerPedId()))
                                    isSellingCoke = true -- تحديث حالة بيع الكوكايين
                                elseif CurrentAction == 'MethDealer' and not isSellingMeth then
                                    TriggerServerEvent('esx_K6dr2H2ugs:Server', 'startSell', 'Meth', GetEntityCoords(PlayerPedId()))
                                    isSellingMeth = true -- تحديث حالة بيع الميث
                                end
                            end
                        end
                    end

                    CurrentAction = nil
                end
            end

            if letSleep then
                Citizen.Wait(3000)
            end
        else
            Citizen.Wait(3000)
        end
    end
end)

-- إضافة في حدث playerSpawned أو في Citizen.CreateThread
AddEventHandler('playerSpawned', function()
    -- طلب حالة التهريب من السيرفر
    TriggerServerEvent('esx_drugs:getSellingStatus')
end)

RegisterNetEvent("abdulrhman:esx_drugs:cantselldrugs_cl_aldih376")
AddEventHandler("abdulrhman:esx_drugs:cantselldrugs_cl_aldih376", function(selldrugs)
    cant_sell_drugs = selldrugs
    TriggerEvent("esx_misc:updatePromotionStatus", "selldrugs", selldrugs)
    if cant_sell_drugs then
        PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
        _2rayan.Functions.DisplayScaleform("<FONT FACE='" .. Config['ESX'].fontid .. "'>~r~ﺕﺎﻋﻮﻨﻤﻤﻟﺍ",
            "<FONT FACE='" .. Config['ESX'].fontid .. "'>~r~ﻥﻷﺍ ﺕﺎﻋﻮﻨﻤﻤﻟﺍ ﺐﻳﺮﻬﺗ ﻦﻜﻤﻳ ﻻ ﻖﻠﻐﻣ ﺐﻳﺮﻬﺘﻟﺍ</font>")
    else
        PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
        _2rayan.Functions.DisplayScaleform("<FONT FACE='" .. Config['ESX'].fontid .. "'>~w~ﺡﺎﺘﻣ ﺐﻳﺮﻬﺘﻟﺍ",
            "<FONT FACE='" .. Config['ESX'].fontid .. "'>~g~ﻥﻷﺍ ﺕﺎﻋﻮﻨﻤﻤﻟﺍ ﺐﻳﺮﻬﺗ ﻦﻜﻤﻳ ﺡﺎﺘﻣ ﺐﻳﺮﻬﺘﻟﺍ</font>")
    end
end)

----------------------------------------------------------
--------------------HARVEST WEED ANIMATION----------------
----------------------------------------------------------
RegisterNetEvent('esx_drugs:HarvestWeedAnimation')
AddEventHandler('esx_drugs:HarvestWeedAnimation', function()
    local ped = GetPlayerPed(-1)
    local x, y, z = table.unpack(GetEntityCoords(playerPed, true))
    if not IsEntityPlayingAnim(ped, "anim@amb@business@weed@weed_inspecting_lo_med_hi@",
        "weed_stand_checkingleaves_kneeling_01_inspector", 3) then
        RequestAnimDict("anim@amb@business@weed@weed_inspecting_lo_med_hi@")
        while not HasAnimDictLoaded("anim@amb@business@weed@weed_inspecting_lo_med_hi@") do
            Citizen.Wait(100)
        end
        SetEntityCoords(PlayerPedId(), 2094.31, 4918.51, 41.04)
        SetEntityHeading(PlayerPedId(), 227.87)
        Wait(100)
        TaskPlayAnim(ped, "anim@amb@business@weed@weed_inspecting_lo_med_hi@",
            "weed_stand_checkingleaves_kneeling_01_inspector", 8.0, -8, -1, 49, 0, 0, 0, 0)
        Wait(2000)
        while IsEntityPlayingAnim(ped, "anim@amb@business@weed@weed_inspecting_lo_med_hi@",
            "weed_stand_checkingleaves_kneeling_01_inspector", 3) do
            Wait(1)
            if (IsControlPressed(0, 32) or IsControlPressed(0, 33) or IsControlPressed(0, 34) or IsControlPressed(0, 35)) then
                ClearPedTasksImmediately(ped)
                break
            end
        end
    end
end)

RegisterNetEvent('esx_drugs:TransformWeedAnimation')
AddEventHandler('esx_drugs:TransformWeedAnimation', function()
    local ped = GetPlayerPed(-1)
    local x, y, z = table.unpack(GetEntityCoords(playerPed, true))
    if not IsEntityPlayingAnim(ped, "anim@amb@business@weed@weed_sorting_seated@", "sorter_right_sort_v3_sorter02", 3) then
        RequestAnimDict("anim@amb@business@weed@weed_sorting_seated@")
        while not HasAnimDictLoaded("anim@amb@business@weed@weed_sorting_seated@") do
            Citizen.Wait(100)
        end
        SetEntityCoords(PlayerPedId(), -1368.09, -318.305, 39.516)
        SetEntityHeading(PlayerPedId(), 293.52)
        Wait(100)
        ----TaskStartScenarioInPlace(playerPed, "PROP_HUMAN_SEAT_CHAIR", 0, false) ---Does not work
        TaskPlayAnim(ped, "anim@amb@business@weed@weed_sorting_seated@", "sorter_right_sort_v3_sorter02", 8.0, -8, -1,
            49, 0, 0, 0, 0)
        Wait(2000)
        while IsEntityPlayingAnim(ped, "anim@amb@business@weed@weed_sorting_seated@", "sorter_right_sort_v3_sorter02", 3) do
            Wait(1)
            if (IsControlPressed(0, 32) or IsControlPressed(0, 33) or IsControlPressed(0, 34) or IsControlPressed(0, 35)) then
                ClearPedTasksImmediately(ped)
                break
            end
        end
    end
end)

-- Disable Controls
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1)
        local playerPed = PlayerPedId(-1)

        if HasAlreadyEnteredMarker then
            DisableControlAction(0, 24, true) -- Attack
            DisableControlAction(0, 257, true) -- Attack 2
            DisableControlAction(0, 25, true) -- Aim
            DisableControlAction(0, 263, true) -- Melee Attack 1
            DisableControlAction(0, 47, true) -- Disable weapon
            DisableControlAction(0, 264, true) -- Disable melee
            DisableControlAction(0, 257, true) -- Disable melee
            DisableControlAction(0, 140, true) -- Disable melee
            DisableControlAction(0, 141, true) -- Disable melee
            DisableControlAction(0, 142, true) -- Disable melee
            DisableControlAction(0, 143, true) -- Disable melee
        else
            Citizen.Wait(500)
        end
    end
end)

-- إضافة متغير عام لتتبع حالة القائمة
local isMenuOpen = false

openLoginMenu = function()
    ESX.UI.Menu.CloseAll()

    Citizen.SetTimeout(1, function()
        ESX.TriggerServerCallback('_2rayan:check_login', function(result)
            if not result then return end

            local elements = {}

            if playerInService or (result.orderActive and result.orderActive > 0) then
                elements = {
                    {
                        label = 'عدد المهربين بالخدمة: ' .. (result.totalActive or 0)
                    },
                    {
                        label = 'عدد المهربين بطابور انتظار التهريب: ' .. (result.totalWaiting or 0)
                    }
                }

                if inServiceTime == 0 or inServiceTime == nil then
                    table.insert(elements, {
                        label = 'تسجيل الخروج من التهريب',
                        value = 'logoutService'
                    })
                end
            elseif playerInWaiting or (result.orderWaiting and result.orderWaiting > 0) then
                elements = {
                    {
                        label = 'عدد المهربين بالخدمة: ' .. (result.totalActive or 0)
                    },
                    {
                        label = 'عدد المهربين بطابور انتظار التهريب: ' .. (result.totalWaiting or 0)
                    },
                    {
                        label = 'ترتيبك بطابور الانتظار: ' .. (result.orderWaiting or 0)
                    },
                    {
                        label = 'تسجيل الخروج من طابور الانتظار',
                        value = 'logoutWaiting'
                    }
                }

                -- تحديث الووتر مارك مع معلومات الطابور
                local queueText = result.orderWaiting .. '/' .. result.totalWaiting .. ' ﺐﻳﺮﻬﺘﻟﺍ ﻞﻴﺠﺴﺗ ﺭﻮﺑﺎﻃ'
                TriggerEvent("esx_misc:updatePromotionStatus", "WaitingDrugs", true, nil, queueText)
            else
                elements = {
                    {
                        label = 'عدد المهربين بالخدمة: ' .. (result.totalActive or 0)
                    },
                    {
                        label = 'عدد المهربين بطابور انتظار التهريب: ' .. (result.totalWaiting or 0)
                    },
                    {
                        label = 'تسجيل الدخول بطابور الانتظار',
                        value = 'loginWaiting'
                    }
                }
            end

            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'drugs_login', {
                title = 'قائمة التهريب',
                align = 'top-right',
                elements = elements
            }, function(data, menu)
                if data.current.value then
                    menu.close()
                    ESX.TriggerServerCallback('_2rayan:login_proccess', function(state, msg, orderWaiting, totalWaiting)
                        if state then
                            if data.current.value == 'loginActive' then
                                playerInService = true
                                TriggerEvent("esx_misc:watermark_promotion", 'selldrugs', true)
                            elseif data.current.value == 'loginWaiting' then
                                playerInWaiting = true
                                local queueText = orderWaiting .. '/' .. totalWaiting .. ' ﺐﻳﺮﻬﺘﻟﺍ ﻞﻴﺠﺴﺗ ﺭﻮﺑﺎﻃ'
                                TriggerEvent("esx_misc:updatePromotionStatus", "WaitingDrugs", true, nil, queueText)
                            elseif data.current.value == 'logoutService' then
                                playerInService = false
                                inServiceTime = 0
                                TriggerEvent("esx_misc:watermark_promotion", 'selldrugs', false)
                            elseif data.current.value == 'logoutWaiting' then
                                playerInWaiting = false
                                TriggerEvent("esx_misc:updatePromotionStatus", "WaitingDrugs", false)
                            end
                        end
                        if msg then
                            ESX.ShowNotification(msg)
                        end
                    end, data.current.value)
                end
            end, function(data, menu)
                menu.close()
            end)
        end)
    end)
end

drawingText = function(text)
  local minutes = math.floor((text / (1000 * 60)) % 60)
	local seconds = math.floor((text / 1000) % 60)
	SetTextFont(fontId)
	SetTextScale(0.3, 0.3) -- Size of text
	SetTextColour(251,192,147,255) -- RGBA
	SetTextDropshadow(271,212,167,255)
	SetTextDropShadow()
	SetTextOutline()
	SetTextCentre(true)
	SetTextEntry("STRING")
	AddTextComponentString(minutes ..':'.. seconds ..' ﺕﺎﻋﻮﻨﻤﻣ ﺐﻳﺮﻬﺗ') -- Main Text string
	DrawText(0.065, 0.5) -- x,y of the screen
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		local letSleep = true

		if inServiceTime > 0 then
			letSleep = false
			drawingText(inServiceTime)

		end

		if letSleep then
			Citizen.Wait(1000)
		end
	end
end)

RegisterNetEvent('_2rayan:update_time')
AddEventHandler('_2rayan:update_time', function(time)
    if playerInService then
        inServiceTime = time
    end
end)

function getRandomCoordinate(port, dealer)
    local positions = Config.SellingPos[port][dealer]
    if positions and #positions > 0 then
        return positions[math.random(#positions)]
    else
        return nil
    end
end

RegisterNetEvent('_2rayan:refresh_blip')
AddEventHandler('_2rayan:refresh_blip', function(state)
    deleteBlips()
    
    -- تعديل الكود لعرض رسالة مختلفة حسب الحالة
    if state then
        -- رسالة تسجيل الدخول
        ESX.ShowNotification('<font color="green">تم تسجيل دخولك في التهريب وتم تحديد موقع عشوائي لك</font>')
    else
        -- رسالة الخروج من التهريب
        ESX.ShowNotification('<font color="red">تم تسجيل خروجك من التهريب</font>')
    end
    
    playerInService = state
    if playerInService then
        refreshZones(currentPort)
    end
    refreshBLips(state)
end)

function setPort(port)
    currentPort = port
    refreshZones(currentPort)
    ESX.ShowNotification('<font color="green">تم تغيير المنفذ إلى ' .. currentPort .. ' وتم تحديث الإحداثيات</font>')
end

function refreshZones(port)
    currentZones = Config.Zones[port]
    for dealer, _ in pairs(currentZones) do
        if string.lower(string.sub(dealer, -6)) == 'dealer' then
            local randomPos = getRandomCoordinate(port, dealer)
            if randomPos then
                local zone = currentZones[dealer]
                currentZones[dealer] = {
                    x = randomPos.x, 
                    y = randomPos.y, 
                    z = randomPos.z, 
                    name = zone.name, 
                    sprite = zone.sprite, 
                    color = zone.color, 
                    MarkerType = zone.MarkerType, 
                    ZoneSize = zone.ZoneSize, 
                    MarkerColor = zone.MarkerColor, 
                    Opacity = zone.Opacity, 
                    Rotate = zone.Rotate
                }
            end
        end
    end
end

RegisterNetEvent('_2rayan:remove_player_action')
AddEventHandler('_2rayan:remove_player_action', function(action)
    if action == 'active' then
        playerInService = false
    elseif action == 'waiting' then
        playerInWaiting = false
    end
    inServiceTime = 0
end)

RegisterNetEvent('_2rayan:do_proccess')
AddEventHandler('_2rayan:do_proccess', function(state, msg)
	playerInService = state
	title = '<FONT FACE="A9eelsh">~s~ﺕﺎﻋﻮﻨﻤﻤﻟﺍ ﺐﻳﺮﻬﺗ'

	if state then
		msg = '<FONT FACE="A9eelsh">~g~'.. msg
	else
		msg = '<FONT FACE="A9eelsh">~r~'.. msg
	end

	PlaySoundFrontend(-1, "Mission_Pass_Notify", '"DLC_HEISTS_GENERAL_FRONTEND_SOUNDS"', true)
  ESX.Scaleform.ShowFreemodeMessage('<FONT FACE="A9eelsh">~w~'.. title, '<FONT FACE="A9eelsh">'.. msg, 5)
end)

RegisterNetEvent('_2rayan:open_seller_menu')
AddEventHandler('_2rayan:open_seller_menu', function()
    if PlayerData.job.name ~= 'admin' then
        return
    end

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'open_seller_menu', {
        title = 'خيارات مهربين الممنوعات',
        elements = {{
            label = 'مهربين مسجلين دخول',
            value = 'active'
        }, {
            label = 'مهربين بطابور الانتظار',
            value = 'waiting'
        }},
        align = 'bottom-right'
    }, function(data, menu)
        -- menu.close()

        if data.current.value == 'active' or data.current.value == 'waiting' then
            ESX.TriggerServerCallback('_2rayan:fetch_sellers_option', function(result)
                -- if result then
                local elements2 = {}

                for i = 1, #result, 1 do
                    if data.current.value == 'waiting' then
                        result[i].time = '<font color=grey>بالانتظار</font>'
                    else
                        result[i].time = math.floor((result[i].time / (1000 * 60)) % 60) .. ':' ..
                                             math.floor((result[i].time / 1000) % 60)
                    end
                    table.insert(elements2, {
                        label = '[' .. i .. '] <font color=orange>' .. result[i].name .. '</font> (' .. result[i].time ..
                            ')',
                        name = '[' .. i .. '] ' .. result[i].name,
                        value = result[i].identifier
                    })
                end

                if #elements2 == 0 then
                    table.insert(elements2, {
                        label = 'لايوجد لاعبين'
                    })
                end

                ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'open_seller_menu2', {
                    title = 'خيارات مهربين الممنوعات',
                    elements = elements2,
                    align = 'bottom-right'
                }, function(data2, menu2)
                    menu2.close()

                    if data2.current.value ~= nil then
                        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'open_seller_menu3', {
                            title = 'هل أنت متأكد من إزالة <font color=yellow>' .. data2.current.name ..
                                '</font> من قائمة التهريب؟',
                            elements = {{
                                label = '<font color=orange>عودة</font>'
                            }, {
                                label = '<font color=red>نعم</font>',
                                value = true
                            }},
                            align = 'bottom-right'
                        }, function(data3, menu3)
                            menu3.close()

                            if data3.current.value then
                                ESX.TriggerServerCallback('_2rayan:remove_player_from_list', function(res)
                                    ESX.ShowNotification(res)
                                end, data.current.value, data2.current.value)
                            end
                        end, function(data2, menu3)
                            menu3.close()
                        end)
                    end
                end, function(data2, menu2)
                    menu2.close()
                end)
                -- end
            end, data.current.value)
        end
    end, function(data, menu)
        menu.close()
    end)
end)

RegisterNetEvent('_2rayan:reset_drug_service')
AddEventHandler('_2rayan:reset_drug_service', function(isDeath)
    playerInService = false
    playerInWaiting = false
    inServiceTime = 0

    -- إزالة البليبات والعلامات
    deleteBlips()

    -- إلغاء أي تأثيرات مخدرات نشطة
    ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(GetPlayerPed(-1), 0)
    SetPedIsDrunk(GetPlayerPed(-1), false)
    SetPedMotionBlur(GetPlayerPed(-1), false)

    -- إلغاء أي مهام نشطة
    ClearPedTasksImmediately(GetPlayerPed(-1))

    -- إعادة تعيين حالة البيع
    isSellingWeed = false
    isSellingOpium = false
    isSellingCoke = false
    isSellingMeth = false

    -- استرجاع استدعاءات esx_misc
    TriggerEvent("esx_misc:updatePromotionStatus", "WaitingDrugs", false)
    TriggerEvent("esx_misc:watermark_promotion", 'selldrugs', false)
end)

-- إضافة متغير عام للتحكم في إرسال الإشعار
local deathNotificationSent = false

-- تعديل الكود للتحقق من موت اللاعب وإرسال الإشعار مرة واحدة فقط
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500)

        -- التحقق مما إذا كان اللاعب في خدمة التهريب (نشط أو في الانتظار)
        if playerInService or playerInWaiting then
            local playerPed = PlayerPedId()

            -- التحقق مما إذا كان اللاعب ميتًا ولم يتم إرسال الإشعار بعد
            if IsEntityDead(playerPed) and not deathNotificationSent then
                -- تعيين المتغير لمنع تكرار الإشعار
                deathNotificationSent = true

                -- إرسال حدث للخادم لإلغاء التهريب بسبب الموت
                TriggerServerEvent('esx_drugs:removePlayerFromDrugService', nil, true)

                -- عرض إشعار فوري للاعب
                _2rayan.Functions.DisplayScaleform("<FONT FACE='" .. Config['ESX'].fontid .. "'>~r~ﺕﺎﻋﻮﻨﻤﻤﻟﺍ",
                    "<FONT FACE='" .. Config['ESX'].fontid .. "'>~w~ﻯﺮﺧﺃ ﺓﺮﻣ ﻞﻴﺠﺴﺘﻟﺍ ﻚﻴﻠﻋ ،ﻚﺗﻮﻣ ﺐﺒﺴﺑ ﺐﻳﺮﻬﺘﻟﺍ ���ﺎﻐﻟﺍ ﻢﺗ</font>", 5000)

                -- إضافة صوت للإشعار
                PlaySoundFrontend(-1, "LOSER", "HUD_AWARDS", true)
            elseif not IsEntityDead(playerPed) and deathNotificationSent then
                -- إعادة تعيين المتغير عندما يكون اللاعب حارًا مرة أخرى
                deathNotificationSent = false
            end
        else
            -- إذا لم يكن اللاعب في خدمة التهريب، إعادة تعيين المتغير
            deathNotificationSent = false
            -- انتظار فترة أطول للتحقق
            Citizen.Wait(2000)
        end
    end
end)

-- إضافة أمر للاعب لإلغاء التهريب
RegisterCommand('canceldrugservice', function()
    TriggerServerEvent('esx_drugs:removePlayerFromDrugService')
end, false)

-- إضافة حدث لتحديث الووتر مارك عندما يتغير ترتيب الطابور
RegisterNetEvent('_2rayan:update_queue_position')
AddEventHandler('_2rayan:update_queue_position', function(orderWaiting, totalWaiting)
    if playerInWaiting then
        local queueText = orderWaiting .. '/' .. totalWaiting .. ' ﺐﻳﺮﻬﺘﻟﺍ ﻞﻴﺠﺴﺗ ﺭﻮﺑﺎﻃ'
        TriggerEvent("esx_misc:updatePromotionStatus", "WaitingDrugs", true, nil, queueText)
    end
end)










