_2rayan.Functions.loadShared()

local playersInService = {
    active = {},
    waiting = {}
}

local CopsConnected = 0
local PlayersHarvestingCoke, PlayersTransformingCoke, PlayersSellingCoke, PlayersHarvestingMeth,
    PlayersTransformingMeth, PlayersSellingMeth, PlayersHarvestingWeed, PlayersTransformingWeed, PlayersSellingWeed,
    PlayersHarvestingOpium, PlayersTransformingOpium, PlayersSellingOpium = {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
    {}
-- متغيرات لتتبع ما إذا كان الإشعار قد ظهر بالفعل
local NotifiedSellingWeed, NotifiedSellingOpium, NotifiedSellingCoke, NotifiedSellingMeth = {}, {}, {}, {}
local isAllowedSell = true

function CountCops()
    local xPlayers = ESX.GetPlayers()

    CopsConnected = 0

    for i = 1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])

        if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' then
            CopsConnected = CopsConnected + 1
        end
    end

    SetTimeout(120 * 1000, CountCops)
end

CountCops()

function DiscordLog(name, title, message, color)
    local DiscordWebHook =
        "https://discord.com/api/webhooks/1272946903956590623/tRxte8sbSFYgLDw9g36dJi867Rc35FVnP6KWrzN6PfT8h-1E_BHLeVqcJLKQTFm9Hs4M"

    local embeds = {{
        ["title"] = title,
        ["type"] = "rich",
        ["description"] = message,
        ["color"] = color,
        ["footer"] = {
            ["text"] = "سجل بيع الممنوعات",
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"
        }
    }}

    if message == nil or message == '' then
        return FALSE
    end
    PerformHttpRequest(DiscordWebHook, function(err, text, headers)
    end, 'POST', json.encode({
        username = name,
        embeds = embeds
    }), {
        ['Content-Type'] = 'application/json'
    })
end

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
        elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end

-- Weed
local function HarvestWeed(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsWeed then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsWeed))
        return
    end

    SetTimeout(Config.TimeToFarmWeed, function()
        if PlayersHarvestingWeed[source] == true then
            local weed = xPlayer.getInventoryItem('weed')

            if not xPlayer.canCarryItem('weed', weed.weight) then
                xPlayer.showNotification(_U('inv_full_weed'))
            else
                xPlayer.addInventoryItem('weed', 1)
                HarvestWeed(source)
            end

        end
    end)
end

local function TransformWeed(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsWeed then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsWeed))
        return
    end

    SetTimeout(Config.TimeToProcessWeed, function()
        if PlayersTransformingWeed[source] == true then
            local weedQuantity = xPlayer.getInventoryItem('weed').count
            local poochQuantity = xPlayer.getInventoryItem('weed_pooch').count

            if poochQuantity > 100 then
                xPlayer.showNotification(_U('too_many_pouches'))
            elseif weedQuantity < 5 then
                xPlayer.showNotification(_U('not_enough_weed'))
            else
                xPlayer.removeInventoryItem('weed', 5)
                xPlayer.addInventoryItem('weed_pooch', 1)

                TransformWeed(source)
            end
        end
    end)
end

local function SellWeed(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Money = 0

    if CopsConnected < Config.RequiredCopsWeed then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsWeed))
        return
    end

    if not isAllowedSell then
        xPlayer.showNotification('تهريب الممنوعات مغلق')
        return
    end

    -- إظهار الإشعار فقط في المرة الأولى
    if not NotifiedSellingWeed[source] then
        xPlayer.showNotification(_U('sale_in_prog'))
        NotifiedSellingWeed[source] = true
    end

    SetTimeout(Config.TimeToSellWeed, function()
        if PlayersSellingWeed[source] == true then
            local poochQuantity = xPlayer.getInventoryItem('weed_pooch').count
            if poochQuantity == 0 then
                xPlayer.showNotification(_U('no_pouches_weed_sale'))
                -- إعادة تعيين حالة الإشعار عند نفاد المخزون
                NotifiedSellingWeed[source] = nil
            else
                xPlayer.removeInventoryItem('weed_pooch', 1)

                if CopsConnected == 0 then
                    Money = 3000
                elseif CopsConnected == 1 then
                    Money = 3000
                elseif CopsConnected == 2 then
                    Money = 3000
                elseif CopsConnected == 3 then
                    Money = 3000
                elseif CopsConnected >= 4 then
                    Money = 3000
                elseif CopsConnected >= 5 then
                    Money = 3000
                elseif CopsConnected >= 6 then
                    Money = 3000
                elseif CopsConnected >= 7 then
                    Money = 3000
                elseif CopsConnected >= 8 then
                    Money = 3000
                elseif CopsConnected >= 9 then
                    Money = 3000
                elseif CopsConnected >= 10 then
                    Money = 3000
                end
                xPlayer.addAccountMoney('black_money', Money)

                -- لا نظهر الإشعار هنا لتجنب التكرار

                local ids = ExtractIdentifiers(source)
                _discordID = "<@" .. ids.discord:gsub("discord:", "") .. ">"
                _identifierID = "**identifier:  ** " .. xPlayer.identifier .. ""
                DiscordLog('بيع الممنوعات', 'بيع شدة حشيش', '' .. xPlayer.getName() .. '\n' ..
                    _discordID .. '\n' .. _identifierID .. '\nكسب أموال قذرة: $' .. Money)

                SellWeed(source)
            end
        end
    end)
end

-- Weed

-- Opium
local function HarvestOpium(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsOpium then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsOpium))
        return
    end

    SetTimeout(Config.TimeToFarmOpium, function()
        if PlayersHarvestingOpium[source] == true then
            local opium = xPlayer.getInventoryItem('opium')

            if not xPlayer.canCarryItem('opium', opium.weight) then
                xPlayer.showNotification(_U('inv_full_opium'))
            else
                xPlayer.addInventoryItem('opium', 1)
                HarvestOpium(source)
            end
        end
    end)
end

local function TransformOpium(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsOpium then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsOpium))
        return
    end

    SetTimeout(Config.TimeToProcessOpium, function()
        if PlayersTransformingOpium[source] == true then
            local opiumQuantity = xPlayer.getInventoryItem('opium').count
            local poochQuantity = xPlayer.getInventoryItem('opium_pooch').count

            if poochQuantity > 100 then
                xPlayer.showNotification(_U('too_many_pouches'))
            elseif opiumQuantity < 5 then
                xPlayer.showNotification(_U('not_enough_opium'))
            else
                xPlayer.removeInventoryItem('opium', 5)
                xPlayer.addInventoryItem('opium_pooch', 1)

                TransformOpium(source)
            end
        end
    end)
end

local function SellOpium(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Money = 0

    if CopsConnected < Config.RequiredCopsOpium then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsOpium))
        return
    end

    if not isAllowedSell then
        xPlayer.showNotification('تهريب الممنوعات مغلق')
        return
    end

    -- إظهار الإشعار فقط في المرة الأولى
    if not NotifiedSellingOpium[source] then
        xPlayer.showNotification(_U('sale_in_prog'))
        NotifiedSellingOpium[source] = true
    end

    SetTimeout(Config.TimeToSellOpium, function()
        if PlayersSellingOpium[source] == true then
            local poochQuantity = xPlayer.getInventoryItem('opium_pooch').count
            if poochQuantity == 0 then
                xPlayer.showNotification(_U('no_pouches_opium_sale'))
                -- إعادة تعيين حالة الإشعار عند نفاد المخزون
                NotifiedSellingOpium[source] = nil
            else
                xPlayer.removeInventoryItem('opium_pooch', 1)

                if CopsConnected == 0 then
                    Money = 3000
                elseif CopsConnected == 1 then
                    Money = 3000
                elseif CopsConnected == 2 then
                    Money = 3000
                elseif CopsConnected == 3 then
                    Money = 3000
                elseif CopsConnected == 4 then
                    Money = 3000
                elseif CopsConnected >= 5 then
                    Money = 3000
                elseif CopsConnected >= 6 then
                    Money = 3000
                elseif CopsConnected >= 7 then
                    Money = 3000
                elseif CopsConnected >= 8 then
                    Money = 3000
                elseif CopsConnected >= 9 then
                    Money = 3000
                elseif CopsConnected >= 10 then
                    Money = 3000
                end
                xPlayer.addAccountMoney('black_money', Money)
                -- لا نظهر الإشعار هنا لتجنب التكرار

                local ids = ExtractIdentifiers(source)
                _discordID = "<@" .. ids.discord:gsub("discord:", "") .. ">"
                _identifierID = "**identifier:  ** " .. xPlayer.identifier .. ""
                DiscordLog('بيع الممنوعات', 'بيع شدة أفيون', '' .. xPlayer.getName() .. '\n' ..
                    _discordID .. '\n' .. _identifierID .. '\nكسب أموال قذرة: $' .. Money)

                SellOpium(source)
            end
        end
    end)
end

-- Opium

-- Coke
local function HarvestCoke(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsCoke then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsCoke))
        return
    end

    SetTimeout(Config.TimeToFarmCoke, function()
        if PlayersHarvestingCoke[source] == true then
            local coke = xPlayer.getInventoryItem('coke')

            if not xPlayer.canCarryItem('coke', coke.weight) then
                xPlayer.showNotification(_U('inv_full_coke'))
            else
                xPlayer.addInventoryItem('coke', 1)
                HarvestCoke(source)
            end
        end
    end)
end

local function TransformCoke(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsCoke then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsCoke))
        return
    end

    SetTimeout(Config.TimeToProcessCoke, function()
        if PlayersTransformingCoke[source] == true then
            local cokeQuantity = xPlayer.getInventoryItem('coke').count
            local poochQuantity = xPlayer.getInventoryItem('coke_pooch').count

            if poochQuantity > 100 then
                xPlayer.showNotification(_U('too_many_pouches'))
            elseif cokeQuantity < 5 then
                xPlayer.showNotification(_U('not_enough_coke'))
            else
                xPlayer.removeInventoryItem('coke', 5)
                xPlayer.addInventoryItem('coke_pooch', 1)

                TransformCoke(source)
            end
        end
    end)
end

local function SellCoke(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Money = 0

    if CopsConnected < Config.RequiredCopsCoke then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsCoke))
        return
    end

    if not isAllowedSell then
        xPlayer.showNotification('تهريب الممنوعات مغلق')
        return
    end

    -- إظهار الإشعار فقط في المرة الأولى
    if not NotifiedSellingCoke[source] then
        xPlayer.showNotification(_U('sale_in_prog'))
        NotifiedSellingCoke[source] = true
    end

    SetTimeout(Config.TimeToSellCoke, function()
        if PlayersSellingCoke[source] == true then
            local poochQuantity = xPlayer.getInventoryItem('coke_pooch').count
            if poochQuantity == 0 then
                xPlayer.showNotification(_U('no_pouches_coke_sale'))
                -- إعادة تعيين حالة الإشعار عند نفاد المخزون
                NotifiedSellingCoke[source] = nil
            else
                xPlayer.removeInventoryItem('coke_pooch', 1)

                if CopsConnected == 0 then
                    Money = 3000
                elseif CopsConnected == 1 then
                    Money = 3000
                elseif CopsConnected == 2 then
                    Money = 3000
                elseif CopsConnected == 3 then
                    Money = 3000
                elseif CopsConnected == 4 then
                    Money = 3000
                elseif CopsConnected >= 5 then
                    Money = 3000
                elseif CopsConnected >= 6 then
                    Money = 3000
                elseif CopsConnected >= 7 then
                    Money = 3000
                elseif CopsConnected >= 8 then
                    Money = 3000
                elseif CopsConnected >= 9 then
                    Money = 3000
                elseif CopsConnected >= 10 then
                    Money = 3000
                end
                xPlayer.addAccountMoney('black_money', Money)
                -- لا نظهر الإشعار هنا لتجنب التكرار

                local ids = ExtractIdentifiers(source)
                _discordID = "<@" .. ids.discord:gsub("discord:", "") .. ">"
                _identifierID = "**identifier:  ** " .. xPlayer.identifier .. ""
                DiscordLog('بيع الممنوعات', 'بيع شدة كوكايين',
                    '' .. xPlayer.getName() .. '\n' .. _discordID .. '\n' .. _identifierID ..
                        '\nكسب أموال قذرة: $' .. Money)

                SellCoke(source)
            end
        end
    end)
end

-- Coke

-- Meth
local function HarvestMeth(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsMeth then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsMeth))
        return
    end

    SetTimeout(Config.TimeToFarmMeth, function()
        if PlayersHarvestingMeth[source] == true then
            local meth = xPlayer.getInventoryItem('meth')

            if not xPlayer.canCarryItem('meth', meth.weight) then
                xPlayer.showNotification(_U('inv_full_meth'))
            else
                xPlayer.addInventoryItem('meth', 1)
                HarvestMeth(source)
            end
        end
    end)
end

local function TransformMeth(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if CopsConnected < Config.RequiredCopsMeth then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsMeth))
        return
    end

    SetTimeout(Config.TimeToProcessMeth, function()
        if PlayersTransformingMeth[source] == true then
            local methQuantity = xPlayer.getInventoryItem('meth').count
            local poochQuantity = xPlayer.getInventoryItem('meth_pooch').count

            if poochQuantity > 100 then
                xPlayer.showNotification(_U('too_many_pouches'))
            elseif methQuantity < 5 then
                xPlayer.showNotification(_U('not_enough_meth'))
            else
                xPlayer.removeInventoryItem('meth', 5)
                xPlayer.addInventoryItem('meth_pooch', 1)

                TransformMeth(source)
            end
        end
    end)
end

local function SellMeth(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Money = 0

    if CopsConnected < Config.RequiredCopsMeth then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsMeth))
        return
    end

    if not isAllowedSell then
        xPlayer.showNotification('تهريب الممنوعات مغلق')
        return
    end

    -- إظهار الإشعار فقط في المرة الأولى
    if not NotifiedSellingMeth[source] then
        xPlayer.showNotification(_U('sale_in_prog'))
        NotifiedSellingMeth[source] = true
    end

    SetTimeout(Config.TimeToSellMeth, function()
        if PlayersSellingMeth[source] == true then
            local poochQuantity = xPlayer.getInventoryItem('meth_pooch').count
            if poochQuantity == 0 then
                xPlayer.showNotification(_U('no_pouches_meth_sale'))
                -- إعادة تعيين حالة الإشعار عند نفاد المخزون
                NotifiedSellingMeth[source] = nil
            else
                xPlayer.removeInventoryItem('meth_pooch', 1)

                if CopsConnected == 0 then
                    Money = 3000
                elseif CopsConnected == 1 then
                    Money = 3000
                elseif CopsConnected == 2 then
                    Money = 3000
                elseif CopsConnected == 3 then
                    Money = 3000
                elseif CopsConnected == 4 then
                    Money = 3000
                elseif CopsConnected == 5 then
                    Money = 3000
                elseif CopsConnected >= 6 then
                    Money = 3000
                elseif CopsConnected >= 7 then
                    Money = 3000
                elseif CopsConnected >= 8 then
                    Money = 3000
                elseif CopsConnected >= 9 then
                    Money = 3000
                elseif CopsConnected >= 10 then
                    Money = 3000
                end
                xPlayer.addAccountMoney('black_money', Money)
                -- لا نظهر الإشعار هنا لتجنب التكرار

                local ids = ExtractIdentifiers(source)
                _discordID = "<@" .. ids.discord:gsub("discord:", "") .. ">"
                _identifierID = "**identifier:  ** " .. xPlayer.identifier .. ""
                DiscordLog('بيع الممنوعات', 'بيع شدة شبو', '' .. xPlayer.getName() .. '\n' ..
                    _discordID .. '\n' .. _identifierID .. '\nكسب أموال قذرة: $' .. Money)

                SellMeth(source)
            end
        end
    end)
end

RegisterServerEvent('esx_K6dr2H2ugs:Server') -- esx_drugs:Server
AddEventHandler('esx_K6dr2H2ugs:Server', function(Action, drug)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local isCrimeActive = exports.esx_misc.isNoCrimetime()


    if isCrimeActive.active then
        xPlayer.showNotification('يمنع تهريب الممنوعات أثناء اعلان ' .. isCrimeActive.label)
        PlayersHarvestingWeed[_source] = false
        PlayersHarvestingOpium[_source] = false
        PlayersHarvestingCoke[_source] = false
        PlayersHarvestingMeth[_source] = false

        PlayersTransformingWeed[_source] = false
        PlayersTransformingOpium[_source] = false
        PlayersTransformingCoke[_source] = false
        PlayersTransformingMeth[_source] = false

        PlayersSellingWeed[_source] = false
        PlayersSellingOpium[_source] = false
        PlayersSellingCoke[_source] = false
        PlayersSellingMeth[_source] = false
        return false
    end

    if not isAllowedSell then
        xPlayer.showNotification('تهريب الممنوعات مغلق')
        PlayersHarvestingWeed[_source] = false
        PlayersHarvestingOpium[_source] = false
        PlayersHarvestingCoke[_source] = false
        PlayersHarvestingMeth[_source] = false

        PlayersTransformingWeed[_source] = false
        PlayersTransformingOpium[_source] = false
        PlayersTransformingCoke[_source] = false
        PlayersTransformingMeth[_source] = false

        PlayersSellingWeed[_source] = false
        PlayersSellingOpium[_source] = false
        PlayersSellingCoke[_source] = false
        PlayersSellingMeth[_source] = false
        return false
    end

    if CopsConnected < Config.RequiredCopsWeed then
        xPlayer.showNotification(_U('act_imp_police', CopsConnected, Config.RequiredCopsWeed))
        PlayersHarvestingWeed[_source] = false
        PlayersHarvestingOpium[_source] = false
        PlayersHarvestingCoke[_source] = false
        PlayersHarvestingMeth[_source] = false

        PlayersTransformingWeed[_source] = false
        PlayersTransformingOpium[_source] = false
        PlayersTransformingCoke[_source] = false
        PlayersTransformingMeth[_source] = false

        PlayersSellingWeed[_source] = false
        PlayersSellingOpium[_source] = false
        PlayersSellingCoke[_source] = false
        PlayersSellingMeth[_source] = false
        return false
    end

    -- # start Harvest The drug
    if Action == 'startHarvest' then
        if drug == 'Weed' then
            PlayersHarvestingWeed[_source] = true
            HarvestWeed(_source)
        elseif drug == 'Opium' then
            PlayersHarvestingOpium[_source] = true
            HarvestOpium(_source)
        elseif drug == 'Coke' then
            PlayersHarvestingCoke[_source] = true
            HarvestCoke(_source)
        elseif drug == 'Meth' then
            PlayersHarvestingMeth[_source] = true
            HarvestMeth(_source)
        end
        xPlayer.showNotification(_U('pickup_in_prog'))
        -- # start Trans From drug
    elseif Action == 'startTransFromDrug' then
        if drug == 'Weed' then
            PlayersTransformingWeed[_source] = true
            TransformWeed(_source)
        elseif drug == 'Opium' then
            PlayersTransformingOpium[_source] = true
            TransformOpium(_source)
        elseif drug == 'Coke' then
            PlayersTransformingCoke[_source] = true
            TransformCoke(_source)
        elseif drug == 'Meth' then
            PlayersTransformingMeth[_source] = true
            TransformMeth(_source)
        end
        xPlayer.showNotification(_U('packing_in_prog'))
        -- # start Sell The drug
    elseif Action == 'startSell' then
        if drug == 'Weed' then
            PlayersSellingWeed[_source] = true
            SellWeed(_source)
        elseif drug == 'Opium' then
            PlayersSellingOpium[_source] = true
            SellOpium(_source)
        elseif drug == 'Coke' then
            PlayersSellingCoke[_source] = true
            SellCoke(_source)
        elseif drug == 'Meth' then
            PlayersSellingMeth[_source] = true
            SellMeth(_source)
        end
        xPlayer.showNotification(_U('sale_in_prog'))
        -- # Stop All
    elseif Action == 'Stop' then
        PlayersHarvestingWeed[_source] = false
        PlayersHarvestingOpium[_source] = false
        PlayersHarvestingCoke[_source] = false
        PlayersHarvestingMeth[_source] = false

        PlayersTransformingWeed[_source] = false
        PlayersTransformingOpium[_source] = false
        PlayersTransformingCoke[_source] = false
        PlayersTransformingMeth[_source] = false

        PlayersSellingWeed[_source] = false
        PlayersSellingOpium[_source] = false
        PlayersSellingCoke[_source] = false
        PlayersSellingMeth[_source] = false

        -- إعادة تعيين حالة الإشعارات عند التوقف
        NotifiedSellingWeed[_source] = nil
        NotifiedSellingOpium[_source] = nil
        NotifiedSellingCoke[_source] = nil
        NotifiedSellingMeth[_source] = nil
    end
end)

-- Meth
RegisterServerEvent('esx_K20drugs:GetUserInventory_H') -- esx_drugs:GetUserInventory
AddEventHandler('esx_K20drugs:GetUserInventory_H', function(currentZone)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    TriggerClientEvent('esx_drugs:ReturnInventory', _source, xPlayer.getInventoryItem('coke').count,
        xPlayer.getInventoryItem('coke_pooch').count, xPlayer.getInventoryItem('meth').count,
        xPlayer.getInventoryItem('meth_pooch').count, xPlayer.getInventoryItem('weed').count,
        xPlayer.getInventoryItem('weed_pooch').count, xPlayer.getInventoryItem('opium').count,
        xPlayer.getInventoryItem('opium_pooch').count, xPlayer.job.name, currentZone)
end)

RegisterServerEvent('esx_drugs:toggleselldrugs')
AddEventHandler('esx_drugs:toggleselldrugs', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    -- تغيير حالة التهريب
    isAllowedSell = not isAllowedSell
    
    -- إرسال الحالة الجديدة لجميع اللاعبين
    TriggerClientEvent("abdulrhman:esx_drugs:cantselldrugs_cl_aldih376", -1, not isAllowedSell)
end)

-- إضافة حدث للحصول على حالة التهريب الحالية (مفيد عند دخول لاعب جديد)
RegisterServerEvent('esx_drugs:getSellingStatus')
AddEventHandler('esx_drugs:getSellingStatus', function()
    local _source = source
    -- إرسال الإشعار فقط إذا كان البيع غير مسموح
    if not isAllowedSell then
        TriggerClientEvent("abdulrhman:esx_drugs:cantselldrugs_cl_aldih376", _source, true)
    end
end)

ESX.RegisterUsableItem('weed', function(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    xPlayer.removeInventoryItem('weed', 1)

    TriggerClientEvent('esx_drugs:onPot', _source)
    xPlayer.showNotification(_U('used_one_weed'))
end)

ESX.RegisterUsableItem('meth', function(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    xPlayer.removeInventoryItem('meth', 1)

    TriggerClientEvent('esx_drugs:onMeth', _source)
    xPlayer.showNotification(_U('used_one_meth'))
end)

ESX.RegisterUsableItem('opium', function(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    xPlayer.removeInventoryItem('opium', 1)

    TriggerClientEvent('esx_drugs:onOpium', _source)
    xPlayer.showNotification(_U('used_one_opium'))
end)

ESX.RegisterUsableItem('coke', function(source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    xPlayer.removeInventoryItem('coke', 1)

    TriggerClientEvent('esx_drugs:onCoke', _source)
    xPlayer.showNotification(_U('used_one_coke'))
end)

-- إزالة الدالة بالكامل لأنها لم تعد مستخدمة

ESX.RegisterServerCallback('_2rayan:check_login', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    local toReturn = {
        totalActive = 0,
        totalWaiting = 0,
        orderActive = 0,
        orderWaiting = 0
    }

    for i = 1, #playersInService.active, 1 do
        toReturn.totalActive = toReturn.totalActive + 1

        if playersInService.active[i].identifier == xPlayer.identifier then
            toReturn.orderActive = i
        end
    end

    for i = 1, #playersInService.waiting, 1 do
        toReturn.totalWaiting = toReturn.totalWaiting + 1

        if playersInService.waiting[i].identifier == xPlayer.identifier then
            toReturn.orderWaiting = i
        end
    end

    cb(toReturn)
end)

ESX.RegisterServerCallback('_2rayan:login_proccess', function(source, cb, action)
    local xPlayer = ESX.GetPlayerFromId(source)
    toReturnState, toReturnMsg = false, nil

    -- منع الوظائف الحكومية من التسجيل في التهريب
    local blockedJobs = {
        ['taxi'] = true,
        ['police'] = true,
        ['mechanic'] = true,
        ['ambulance'] = true,
        ['agent'] = false,
        ['admin'] = false
    }

    if blockedJobs[xPlayer.job.name] then
        toReturnMsg = 'لا يمكن للوظائف الحكومية التسجيل في التهريب'
        return cb(false, toReturnMsg, 0, 0)
    end

    if action == 'loginActive' then
        if isAllowedSell then
            table.insert(playersInService.active, {
                identifier = xPlayer.identifier,
                time = Config.TimeSell * 60000
            })
            TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, true)
            TriggerClientEvent('_2rayan:update_time', xPlayer.source, Config.TimeSell * 60000)
            toReturnMsg = 'تم تسجيل دخولك بالتهريب'
            toReturnState = true
        else
            toReturnMsg = 'لايمكن تسجيل دخولك بالتهريب الآن'
        end

    elseif action == 'loginWaiting' then
        table.insert(playersInService.waiting, {
            identifier = xPlayer.identifier
        })
        toReturnMsg = 'تم تسجيل دخولك بطابور انتظار التهريب'
        toReturnState = true

    elseif action == 'logoutService' then
        toReturnMsg = 'تم تسجيل خروجك من التهريب'

        for k, v in pairs(playersInService.active) do
            if v.identifier == xPlayer.identifier then
                table.remove(playersInService.active, k)
                TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, false)
            end
        end
        toReturnState = true
    elseif action == 'logoutWaiting' then
        toReturnMsg = 'تم تسجيل خروجك من طابور انتظار التهريب'

        for k, v in pairs(playersInService.waiting) do
            if v.identifier == xPlayer.identifier then
                table.remove(playersInService.waiting, k)
            end
        end
        toReturnState = true
    end

    local totalActive_2 = #playersInService.active
    local totalWaiting_2 = 0
    local orderWaiting_2 = 0

    for i = 1, #playersInService.waiting, 1 do
        totalWaiting_2 = totalWaiting_2 + 1

        if playersInService.waiting[i].identifier == xPlayer.identifier then
            orderWaiting_2 = i
        end

        local __xPlayer = ESX.GetPlayerFromIdentifier(playersInService.waiting[i].identifier)
        TriggerClientEvent("esx_misc:updatePromotionStatus", __xPlayer.source, "WaitingDrugs", true, nil,
            "" .. orderWaiting_2 .. ' / ' .. totalWaiting_2 .. " :ﺐﻳﺮﻬﺗ ﻞﻴﺠﺴﺗ رﻮﺑﺎﻃ")
    end

    cb(toReturnState, toReturnMsg, orderWaiting_2, totalWaiting_2)
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        -- التحقق من اللاعبين النشطين وتحديث الوقت
        for k, v in pairs(playersInService.active) do
            local xPlayer = ESX.GetPlayerFromIdentifier(v.identifier)

            if xPlayer == nil then
                table.remove(playersInService.active, k)
            else
                v.time = v.time - 1000
                TriggerClientEvent('_2rayan:update_time', xPlayer.source, v.time)

                if v.time == 0 then
                    -- STOP SELL
                    table.remove(playersInService.active, k)
                    TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, false)
                    TriggerClientEvent("esx_misc:updatePromotionStatus", xPlayer.source, "WaitingDrugs", false)
                    TriggerClientEvent("esx_misc:watermark_promotion", xPlayer.source, 'selldrugs', false)
                    TriggerClientEvent('_2rayan:do_proccess', xPlayer.source, false, 'ﺐﻳﺮﻬﺘﻟﺍ ﻦﻣ ﻚﺟﻭﺮﺧ ﻞﻴﺠﺴﺗ ﻢﺗ')

                    -- إيقاف جميع عمليات التهريب
                    PlayersHarvestingWeed[xPlayer.source] = false
                    PlayersHarvestingOpium[xPlayer.source] = false
                    PlayersHarvestingCoke[xPlayer.source] = false
                    PlayersHarvestingMeth[xPlayer.source] = false

                    PlayersTransformingWeed[xPlayer.source] = false
                    PlayersTransformingOpium[xPlayer.source] = false
                    PlayersTransformingCoke[xPlayer.source] = false
                    PlayersTransformingMeth[xPlayer.source] = false

                    PlayersSellingWeed[xPlayer.source] = false
                    PlayersSellingOpium[xPlayer.source] = false
                    PlayersSellingCoke[xPlayer.source] = false
                    PlayersSellingMeth[xPlayer.source] = false

                    -- إعادة تعيين حالة الإشعارات
                    NotifiedSellingWeed[xPlayer.source] = nil
                    NotifiedSellingOpium[xPlayer.source] = nil
                    NotifiedSellingCoke[xPlayer.source] = nil
                    NotifiedSellingMeth[xPlayer.source] = nil
                end
            end
        end

        -- التحقق من إمكانية إضافة لاعب جديد من قائمة الانتظار
        if isAllowedSell and #playersInService.waiting > 0 then
            local activeCount = #playersInService.active

            if activeCount < 1 then
                local nextPlayer = playersInService.waiting[1]
                if nextPlayer then
                    local xPlayer = ESX.GetPlayerFromIdentifier(nextPlayer.identifier)
                    if xPlayer then
                        -- إضافة اللاعب إلى القائمة النشطة
                        table.insert(playersInService.active, {
                            identifier = nextPlayer.identifier,
                            time = Config.TimeSell * 60000
                        })

                        -- إزالة اللاعب من قائمة الانتظار
                        table.remove(playersInService.waiting, 1)

                        -- START SELL
                        -- إخفاء حالة الطابور للاعب الذي بدأ التهريب
                        TriggerClientEvent("esx_misc:updatePromotionStatus", xPlayer.source, "WaitingDrugs", false)
                        TriggerClientEvent("esx_misc:watermark_promotion", xPlayer.source, 'selldrugs', true)
                        TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, true)
                        TriggerClientEvent('_2rayan:do_proccess', xPlayer.source, true, 'ﺡﺎﺠﻨﺑ ﺐﻳﺮﻬﺘﻟﺎﺑ ﻚﻟﻮﺧﺩ ﻞﻴﺠﺴﺗ ﻢﺗ')

                        -- تحديث حالة الطابور للاعبين المتبقين فقط
                        for i = 1, #playersInService.waiting do
                            local waitingPlayer = ESX.GetPlayerFromIdentifier(playersInService.waiting[i].identifier)
                            if waitingPlayer then
                                TriggerClientEvent("esx_misc:updatePromotionStatus", waitingPlayer.source, "WaitingDrugs", true, nil,
                                    "" .. i .. ' / ' .. #playersInService.waiting .. " :ﺐﻳﺮﻬﺗ ﻞﻴﺠﺴﺗ رﻮﺑﺎﻃ")
                            end
                        end
                    end
                end
            end
        end
    end
end)

ESX.RegisterServerCallback('_2rayan:fetch_sellers_option', function(source, cb, action)
    local xPlayer = ESX.GetPlayerFromId(source)
    local toReturn = {
        active = playersInService.active,
        waiting = playersInService.waiting
    }

    if xPlayer.job.name ~= 'admin' then
        return false
    end

    if #toReturn[action] > 0 then
        for i = 1, #toReturn[action], 1 do
            local xPlayer = ESX.GetPlayerFromIdentifier(toReturn[action][i].identifier)
            toReturn[action][i].name = xPlayer.getName()
        end
    end

    cb(toReturn[action])
end)

ESX.RegisterServerCallback('_2rayan:remove_player_from_list', function(source, cb, action, player)
    local xPlayer = ESX.GetPlayerFromId(source)
    local toReturn = 'حدث خطأ في ازالة الاعب'

    if xPlayer.job.name ~= 'admin' then
        return false
    end

    local yPlayer = ESX.GetPlayerFromIdentifier(player)
    for k, v in pairs(playersInService[action]) do
        if v.identifier == player then
            table.remove(playersInService[action], k)
            toReturn = 'تم حذف الاعب من القائمة بنجاح'
        end
    end

    if yPlayer then
        TriggerClientEvent('_2rayan:remove_player_action', yPlayer.source, action)
        TriggerClientEvent('_2rayan:do_proccess', yPlayer.source, false,
            'ﺐﻳﺮﻬﺘﻟﺍ ﺔﻤﺋﺎﻗ ﻦﻣ ﻚﺘﻟﺍﺯﺇ ﻢﺗ')
        TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, false)
    end

    cb(toReturn)
end)

RegisterServerEvent('esx_drugs:removePlayerFromDrugService')
AddEventHandler('esx_drugs:removePlayerFromDrugService', function(target, isDeath)
    local _source = source
    local xPlayer = nil

    -- تحديد اللاعب المستهدف (إما اللاعب نفسه أو لاعب آخر إذا كان المصدر مشرف)
    if target then
        local adminPlayer = ESX.GetPlayerFromId(_source)
        if adminPlayer.job.name == 'admin' then
            xPlayer = ESX.GetPlayerFromId(target)
        else
            xPlayer = adminPlayer -- إذا لم يكن مشرف، فقط يمكنه إزالة نفسه
        end
    else
        xPlayer = ESX.GetPlayerFromId(_source)
    end

    if not xPlayer then return end

    -- إيقاف جميع عمليات التهريب
    PlayersHarvestingWeed[xPlayer.source] = false
    PlayersHarvestingOpium[xPlayer.source] = false
    PlayersHarvestingCoke[xPlayer.source] = false
    PlayersHarvestingMeth[xPlayer.source] = false

    PlayersTransformingWeed[xPlayer.source] = false
    PlayersTransformingOpium[xPlayer.source] = false
    PlayersTransformingCoke[xPlayer.source] = false
    PlayersTransformingMeth[xPlayer.source] = false

    PlayersSellingWeed[xPlayer.source] = false
    PlayersSellingOpium[xPlayer.source] = false
    PlayersSellingCoke[xPlayer.source] = false
    PlayersSellingMeth[xPlayer.source] = false

    -- إعادة تعيين حالة الإشعارات
    NotifiedSellingWeed[xPlayer.source] = nil
    NotifiedSellingOpium[xPlayer.source] = nil
    NotifiedSellingCoke[xPlayer.source] = nil
    NotifiedSellingMeth[xPlayer.source] = nil

    -- تحقق مما إذا كان اللاعب في قائمة الانتظار أو النشطين
    local wasInService = false

    -- إزالة اللاعب من قائمة الانتظار
    for k, v in pairs(playersInService.waiting) do
        if v.identifier == xPlayer.identifier then
            table.remove(playersInService.waiting, k)
            TriggerClientEvent("esx_misc:updatePromotionStatus", xPlayer.source, "WaitingDrugs", false)
            wasInService = true
            break
        end
    end

    -- إزالة اللاعب من قائمة النشطين
    for k, v in pairs(playersInService.active) do
        if v.identifier == xPlayer.identifier then
            table.remove(playersInService.active, k)
            wasInService = true
            break
        end
    end

    -- تحديث حالة اللاعب على الجانب العميل
    TriggerClientEvent('_2rayan:refresh_blip', xPlayer.source, false)

    -- إرسال حدث لإعادة تعيين خدمة التهريب (بدون إشعار في حالة الموت)
    TriggerClientEvent('_2rayan:reset_drug_service', xPlayer.source, isDeath)

    -- في حالة الموت، لا نرسل إشعار هنا لأنه سيتم عرضه من جانب العميل
    if not isDeath then
        TriggerClientEvent('_2rayan:do_proccess', xPlayer.source, false, 'ﻯﺮﺧﺃ ��ﺮﻣ ﻞﻴﺠﺴﺘﻟﺍ ﻚﻴﻠﻋ ،ﻚﺗﻮﻣ ﺐﺒﺴﺑ ﺐﻳﺮﻬﺘﻟﺍ ﺀﺎﻐﻟﺍ ﻢﺗ')
    end

    -- تحديث طابور الانتظار لجميع اللاعبين المتبقين
    local totalWaiting = #playersInService.waiting
    for i = 1, totalWaiting do
        local waitingPlayerIdentifier = playersInService.waiting[i] and playersInService.waiting[i].identifier
        if waitingPlayerIdentifier then
            local waitingPlayer = ESX.GetPlayerFromIdentifier(waitingPlayerIdentifier)
            if waitingPlayer then
                TriggerClientEvent("esx_misc:updatePromotionStatus", waitingPlayer.source, "WaitingDrugs", true, nil, "" .. i .. ' / ' .. totalWaiting .. " :ﺐﻳﺮﻬﺗ ﻞﻴﺠﺴﺗ رﻮﺑﺎﻃ")
            end
        end
    end

    -- سجل العملية في السجلات إذا كان اللاعب في الخدمة
    if wasInService and DiscordLog then
        local ids = ExtractIdentifiers(xPlayer.source)
        local _discordID = ids.discord and ("<@" .. ids.discord:gsub("discord:", "") .. ">") or "غير معروف"
        local _identifierID = "**identifier:  ** " .. xPlayer.identifier .. ""
        local logTitle = 'إلغاء تسجيل من التهريب'
        if isDeath then
            logTitle = 'ﻯﺮﺧﺃ ﺓﺮﻣ ﻞﻴﺠﺴﺘﻟﺍ ���ﻴﻠﻋ ،ﻚﺗﻮﻣ ﺐﺒﺴﺑ ﺐﻳﺮﻬﺘﻟﺍ ﺀﺎﻐﻟﺍ ﻢﺗ'
        end
        DiscordLog('نظام التهريب', logTitle, '' .. xPlayer.getName() .. '\n' ..
            _discordID .. '\n' .. _identifierID)
    end
end)
