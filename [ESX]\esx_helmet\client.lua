local PlayerData = {}
local isWearingHelmet = false
local helmetHealth = 0
local helmetDisplay = false
RegisterFontFile('A9eelsh')
fontId = RegisterFontId('A9eelsh')

CreateThread(function()
    while ESX == nil do
        if Config.ESX.old then
            TriggerEvent(Config.ESX.EventName, function(obj) ESX = obj end)
        else
            ESX = exports['es_extended']:getSharedObject()
        end
        Wait(0)
    end

    while ESX.GetPlayerData().job == nil do
        Wait(10)
    end

    PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    PlayerData.job = job
end)

-- Function to check if player is LEO
local function isLeoJob()
    if PlayerData.job then
        for _, job in ipairs(Config.LeoJobs) do
            if PlayerData.job.name == job then
                return true
            end
        end
    end
    return false
end

-- Function to get helmet max health based on job
local function getMaxHelmetHealth()
    return isLeoJob() and Config.helmetHealthLeoJob or Config.helmetHealth
end

-- Helper function to draw 2D text - تم تعريفها مرة واحدة فقط
function DrawText2D(x, y, scale, text)
    SetTextFont(fontId)
    SetTextScale(scale, scale)
    SetTextColour(255, 255, 255, 255)
    SetTextDropShadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x, y)
end

-- Function to equip helmet
local function equipHelmet()
    local playerPed = PlayerPedId()
    
    -- تجميد الحركة بشكل كامل
    FreezeEntityPosition(playerPed, true)
    SetEntityInvincible(playerPed, true)  -- جعل اللاعب غير قابل للإصابة مؤقتاً
    SetPedCanRagdoll(playerPed, false)    -- منع اللاعب من التمايل
    
    -- تعطيل جميع الأزرار
    DisableAllControlActions(0)
    
    -- عرض شريط التقدم
    TriggerEvent('pogressBar:drawBar', 2000, '<font size=5>جاري ارتداء الخوذة')
    
    -- تشغيل الأنيميشن
    ESX.Streaming.RequestAnimDict('mp_masks@on_foot', function()
        TaskPlayAnim(playerPed, 'mp_masks@on_foot', 'put_on_mask', 8.0, -8.0, 2000, 49, 0, false, false, false)
    end)
    
    -- انتظار انتهاء الأنيميشن
    Wait(2000)
    
    -- إعادة تفعيل الحركة والأزرار
    FreezeEntityPosition(playerPed, false)
    SetEntityInvincible(playerPed, false)
    SetPedCanRagdoll(playerPed, true)
    EnableAllControlActions(0)
    
    -- وضع الخوذة
    local playerModel = GetEntityModel(playerPed)
    local propIndex, propTexture
    
    if Config.HelmetProp[playerModel] then
        propIndex = Config.HelmetProp[playerModel].id
        propTexture = Config.HelmetProp[playerModel].texture
    else
        -- نموذج آخر، استخدم الإعدادات الافتراضية
        propIndex = 0
        propTexture = 0
    end
    
    SetPedPropIndex(playerPed, 0, propIndex, propTexture, true)
    
    -- تعيين حالة الخوذة وصحتها
    isWearingHelmet = true
    helmetHealth = getMaxHelmetHealth()
    helmetDisplay = true
    
    -- إشعار
    ESX.ShowNotification('لقد ارتديت خوذة')
end

-- Function to remove helmet
local function removeHelmet()
    local playerPed = PlayerPedId()
    
    -- تجميد الحركة بشكل كامل
    FreezeEntityPosition(playerPed, true)
    SetEntityInvincible(playerPed, true)
    SetPedCanRagdoll(playerPed, false)
    
    -- تعطيل جميع الأزرار
    DisableAllControlActions(0)
    
    -- عرض شريط التقدم
    TriggerEvent('pogressBar:drawBar', 2000, '<font size=5>جاري إزالة الخوذة')
    
    -- تشغيل الأنيميشن
    ESX.Streaming.RequestAnimDict('mp_masks@on_foot', function()
        TaskPlayAnim(playerPed, 'mp_masks@on_foot', 'put_on_mask', 8.0, -8.0, 2000, 49, 0, false, false, false)
    end)
    
    -- انتظار انتهاء الأنيميشن
    Wait(2000)
    
    -- إعادة تفعيل الحركة والأزرار
    FreezeEntityPosition(playerPed, false)
    SetEntityInvincible(playerPed, false)
    SetPedCanRagdoll(playerPed, true)
    EnableAllControlActions(0)
    
    -- إزالة الخوذة
    ClearPedProp(playerPed, 0)
    
    -- تعيين حالة الخوذة
    isWearingHelmet = false
    helmetDisplay = false
    
    -- إشعار
    ESX.ShowNotification('لقد قمت بإزالة الخوذة')
end

-- Register event for using helmet
RegisterNetEvent('esx_helmet:useHelmet')
AddEventHandler('esx_helmet:useHelmet', function()
    if not isWearingHelmet then
        equipHelmet()
    else
        removeHelmet()
    end
end)

-- Display helmet health
CreateThread(function()
    while true do
        Wait(0)
        if helmetDisplay and isWearingHelmet then
            local maxHealth = getMaxHelmetHealth()
            local healthPercent = (helmetHealth / maxHealth) * 100
            
            -- تحديد موقع وحجم الشريط (تم تقليل baseX لإزاحة الشريط إلى اليسار)
            local baseX = 0.03  -- تم تغييرها من 0.05 إلى 0.03
            local baseY = 0.7
            local bgWidth = 0.15
            local bgHeight = 0.025
            
            -- رسم خلفية الشريط (أسود شفاف)
            DrawRect(baseX + (bgWidth/2), baseY, bgWidth, bgHeight, 0, 0, 0, 150)
            
            -- حساب عرض شريط الصحة بناءً على النسبة المئوية
            local barWidth = (healthPercent / 100) * bgWidth
            
            -- رسم شريط الصحة (أخضر فيروزي) فقط إذا كانت النسبة أكبر من صفر
            if healthPercent > 0 then
                -- نحسب موقع منتصف الشريط بدقة
                local barX = baseX + (barWidth / 2)
                DrawRect(barX, baseY, barWidth, bgHeight - 0.005, 0, 238, 79, 200) -- اللون #00EE4F
            end
            
            -- رسم النص بدون رموز خاصة
            local textToShow = 'ﺓﺫﻮﺨﻟﺍ ﺔﻟﺎﺣ: ' .. math.floor(healthPercent) .. '%'
            DrawText2D(baseX, baseY - 0.005, 0.3, textToShow)  -- تم تغييرها من -0.015 إلى -0.005
        end
    end
end)

-- Key binding for LEO quick equip (F10 بدلاً من F6)
CreateThread(function()
    while true do
        Wait(0)
        if isLeoJob() then
            if IsControlJustReleased(0, 57) then -- F10 key (57 هو كود مفتاح F10)
                TriggerEvent('esx_helmet:useHelmet')
            end
        end
    end
end)

-- Damage handler for helmet
CreateThread(function()
    while true do
        Wait(0)
        local playerPed = PlayerPedId()
        
        if isWearingHelmet and HasEntityBeenDamagedByAnyPed(playerPed) then
            -- تقليل صحة الخوذة عند تلقي ضرر
            helmetHealth = helmetHealth - 10
            helmetDisplay = true
            
            -- إزالة الخوذة إذا تم تدميرها
            if helmetHealth <= 0 then
                removeHelmet()
                ESX.ShowNotification('تم تدمير خوذتك!')
            end
            
            -- إعادة تعيين حالة الضرر
            ClearEntityLastDamageEntity(playerPed)
        end
    end
end)