Config                  = {}
Config.Locale           = 'en'

Config.MarkerColor                = {r = 50, g = 50, b = 204}

Config.ChangeIdentityCoords = vector3(451.859344, -989.736267, 30.678345)
Config.ChangeIdentityPrice = 1500

Config.pas = 'oaddd2hKKd2bn'
-- [Config.EnableCommands]
-- Enables Commands Such As /char and /chardel
Config.EnableCommands   = false

-- [Config.UseSteamID]
-- Changes the identifiers from Rockstar License To Steam. In order to use this, you will have to have
-- made edits to your ESX in order to use Steam identifiers all around, because new ESX uses Rockstar
-- license. I just made it easier for people that convert to steam identifiers.
Config.UseSteamID       = false 

-- [Config.UseDeferrals]
-- EXPERIMENTAL Character Registration Method. This will allow players to create identities in deferrals
-- before they ever join the server. This still needs time of testing to see what issues that we have
-- in linux servers and windows servers, and which artifacts it will work on, and which artifacts it
-- will not work on. If you don't know a lot about this, I would suggest to set it false for now.
Config.UseDeferrals     = false -- EXPERIMENTAL Character Registration Method.


-- These values are for the second input validation in server/main.lua
Config.MaxNameLength    = 16
Config.MinHeight        = 140
Config.MaxHeight        = 200
Config.LowestYear       = 1900
Config.HighestYear      = 2021

Config.FullCharDelete   = false
Config.EnableDebugging  = false
