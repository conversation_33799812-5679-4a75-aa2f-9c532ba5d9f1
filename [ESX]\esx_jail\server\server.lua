ESX                = nil

ESX = exports["es_extended"]:getSharedObject()

RegisterCommand("jail", function(src, args, raw)

	local xPlayer = ESX.GetPlayerFromId(src)

	if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]
		local jailTime = tonumber(args[2])
		local jailReason = args[3]

		if GetPlayerName(jailPlayer) ~= nil then

			if jailTime ~= nil then
				JailPlayer(jailPlayer, jailTime)

				TriggerClientEvent("esx:showNotification", src, " دقيقة" .. jailTime .. " تم سجنه"..GetPlayerName(jailPlayer))
				TriggerEvent("Conker-logs:server:SendLog", "jail", "**لوق سجن لاعب**", "red", "`: اسم الاداري` \n ".. xPlayer.name .."\n \n `: إسم المواطن` \n ".. GetPlayerName(jailPlayer) .."\n \n `: السبب` \n ".. jailReason .."\n \n `: المدة` \n ".. jailTime .." دقيقة \n")

				if args[3] ~= nil then
					GetRPName(jailPlayer, function(Firstname, Lastname)
					    TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة والتفتيش " ,  {198, 40, 40} ,  " تم احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3مخالفة النظام العام^0 لمدة ^3"..jailTime.." ^0شهر ^3")
					end)
				end
			else
				TriggerClientEvent("esx:showNotification", src, "ﺊﻃﺎﺧ ﻦﺠﺴﻟﺍ ﺓﺪﻣ")
			end
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)

function SendToDiscord(source, target, reason)
	local xPlayer = ESX.GetPlayerFromId(target)
	local xTarget = ESX.GetPlayerFromId(source)
	local Killedids = ExtractIdentifiers(target)
  local Killerids = ExtractIdentifiers(source)
  Killedids_steamID =Killedids.steam
  Killedids_discordID ="<@" ..Killedids.discord:gsub("discord:", "")..">"
  Killerids_steamID =Killerids.steam
  Killerids_discordID ="<@" ..Killerids.discord:gsub("discord:", "")..">"
  local messgae = "سبب : **"..reason.."**"..
                  "اسم المواطن : **"..xPlayer.name..
				  "**\n معرف المواطن : **"..xPlayer.identifier..
				  "**\n معرف ستيم : **"..Killedids_steamID..
				  "** \n معرف ديسكورد : **"..Killedids_discordID..
				  "** \n --------------- \n اسم الشرطي : **"..xTarget.name..
				  "**\n معرف الشرطي : **"..xTarget.identifier..
				  "** \n شرطي ستيم : **"..Killerids_steamID..
				  "** \n الشرطي ديسكورد : **"..Killerids_discordID.."**"
				  TriggerEvent("napoly-logs:server:SendLog", "jailpolice", "**سجن لاعب**", "red", message)
end
RegisterServerEvent("esx_jail:unJailPlayerPrisonerBail")
AddEventHandler("esx_jail:unJailPlayerPrisonerBail", function(playerIdentifier, thePrice, theTime, theTimePlayerInPrison)
    local thePrisoner = ESX.GetPlayerFromIdentifier(playerIdentifier) -- اللاعب المسجون
    local payer = ESX.GetPlayerFromId(source) -- الشخص الذي دفع الكفالة

    if thePrisoner then
        -- إرسال الحدث فقط للاعب المسجون
        TriggerClientEvent("esx_jail:unJailPlayerPrisonerBail", thePrisoner.source, theTime, true)
        
        -- إشعارات للطرفين
        payer.showNotification("<font color=gray>لقد كفلت</font>: " .. thePrisoner.getName() .. 
            "<center><center><font color=gray>بمبلغ</font>: <font color=00FF00>$</font>" .. 
            ESX.Math.GroupDigits(thePrice))
            
        thePrisoner.showNotification("<font color=gray>لقد تم كفالتك من قبل</font>: " .. 
            payer.getName() .. "<center><center><font color=gray>بمبلغ</font>: <font color=00FF00>$</font>" .. 
            ESX.Math.GroupDigits(thePrice))

        -- إرسال رسالة في الشات العام
        GetRPName(thePrisoner.source, function(prisonerFirstname, prisonerLastname)
            GetRPName(payer.source, function(payerFirstname, payerLastname)
                TriggerClientEvent('chatMessage', -1, " 💰 نظام الكفالة " , 
                    {255, 215, 0}, -- لون ذهبي
                    string.format(" تم الإفراج عن ^3%s %s^0 بكفالة من ^2%s %s^0 بمبلغ ^3$%s^0", 
                        prisonerFirstname, 
                        prisonerLastname, 
                        payerFirstname, 
                        payerLastname,
                        ESX.Math.GroupDigits(thePrice)
                    )
                )
            end)
        end)

        -- تحديث قاعدة البيانات وإخراج السجين
        unJailPlayerPrisonerBail(thePrisoner.source, theTime)
    end
end)
function unJailPlayerPrisonerBail(jailPlayer, theTime)
    local xPlayer = ESX.GetPlayerFromId(jailPlayer)
    
    -- تحديث قاعدة البيانات
    MySQL.Async.execute('UPDATE users SET jail = @jail WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier,
        ['@jail'] = 0
    }, function()
        -- بعد تحديث قاعدة البيانات، نرسل الحدث للكلاينت
        TriggerClientEvent("esx_jail:unJailPlayerPrisonerBail", jailPlayer, theTime, true)
        
        -- تحديث متغيرات السجن
        EditJailTime(jailPlayer, 0)
        
        -- إرسال تحديث للسيرفر
        TriggerClientEvent("esx_jail:unJailPlayer", jailPlayer)
        
        -- تحديث حالة السجن في الذاكرة
        TriggerEvent("Abor7mh:setPlayerUnJailed", jailPlayer)
    end)
end
function sendToDiscordMain (name,title,message,color, webhook)
	local DiscordWebHook = webhook
	-- Modify here your discordWebHook username = name, content = message,embeds = embeds
  
  local embeds = {
	  {
		  ["title"]=title,
		  ["type"]="rich",
		  ["description"] = message,
		  ["color"] =color,
		  ["footer"]=  {
			  ["text"]= os.date("%x %X %p"),
		 },
	  }
  }
  
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end
RegisterCommand("removevisa", function(src, args, raw)

	local xPlayer = ESX.GetPlayerFromId(src)

	if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]
		local jailTime = tonumber(args[2])
		local jailledPlayer = ESX.GetPlayerFromId(jailPlayer)

		if GetPlayerName(jailPlayer) ~= nil then

			if jailTime ~= nil then
				JailPlayer(jailPlayer, jailTime)

				TriggerClientEvent("esx:showNotification", src, " ﺔﻘﻴﻗﺩ" .. jailTime .. " ﻪﻨﺠﺳ ﻢﺗ"..GetPlayerName(jailPlayer))
				--TriggerClientEvent("esx:showpNotifyNotification", jailledPlayer.source, "لقد تم سحب التأشيرة الخاصة بك يرجى تسجيل الخروج والدخول مجددا لإجتياز الإختبار مرة أخرى - سيتم الإفراج عنك بعد حصولك على التأشيرة مجددا")
		for k, v in pairs(Config.Msg["other"]["citizen"]["notification"]) do 
            Config.Options.text = v
            TriggerClientEvent("pNotify:SendNotification", jailledPlayer.source, Config.Options)
            --Citizen.Wait(5000)
        end
				
			else
				TriggerClientEvent("esx:showNotification", src, "ﺊﻃﺎﺧ ﻦﺠﺴﻟﺍ ﺓﺪﻣ")
			end
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)

RegisterCommand("unjail", function(src, args)

	local xPlayer = ESX.GetPlayerFromId(src)

	if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]

		if GetPlayerName(jailPlayer) ~= nil then
			UnJail(jailPlayer)
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)

RegisterServerEvent("esx_jail:jailPlayer")
AddEventHandler("esx_jail:jailPlayer", function(targetSrc, jailTime, jailReason)
	local src = source
	local targetSrc = tonumber(targetSrc)
	local xPlayer = ESX.GetPlayerFromId(src)
    local user = xPlayer.getName()
    local rpname = user	

	JailPlayer(targetSrc, jailTime)

	GetRPName(targetSrc, function(Firstname, Lastname)
	    if xPlayer["job"]["name"] == "police" then
		TriggerClientEvent('chatMessage', -1, " إدارة الشرطة 👮 | " .. rpname .." " ,  { 17, 69, 191 } ,  " تم احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
		elseif xPlayer["job"]["name"] == "agent" then
        TriggerClientEvent('chatMessage', -1, " حرس الحدود  💂 | " .. rpname .." " ,  { 78, 198, 78 } ,  " تم احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")		
		elseif xPlayer["job"]["name"] == "admin" then
        TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش " ,  { 255, 0, 0 } ,  " تم احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")		
		else
		TriggerClientEvent('chatMessage', -1, " وزارة الداخلية 👮 | " .. rpname .." " ,  { 17, 69, 191 } ,  " تم احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
		end
	end)

	TriggerClientEvent("esx:showNotification", src, GetPlayerName(targetSrc) .. " سجن لمدة " .. jailTime .. " دقيقة!")
end)

RegisterServerEvent("esx_jail:unJailPlayer")
AddEventHandler("esx_jail:unJailPlayer", function(targetIdentifier)
	local src = source
	local xPlayer = ESX.GetPlayerFromIdentifier(targetIdentifier)

	if xPlayer ~= nil then
		UnJail(xPlayer.source)
	else
		MySQL.Async.execute(
			"UPDATE users SET jail = @newJailTime WHERE identifier = @identifier",
			{
				['@identifier'] = targetIdentifier,
				['@newJailTime'] = 0
			}
		)
	end

	TriggerClientEvent("esx:showNotification", src, xPlayer.name .. " Unjailed!")
end)

RegisterServerEvent("esx_jail:updateJailTime")
AddEventHandler("esx_jail:updateJailTime", function(newJailTime)
	local src = source

	EditJailTime(src, newJailTime)
end)

RegisterServerEvent("esx_jail:prisonWorkReward")
AddEventHandler("esx_jail:prisonWorkReward", function()
	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)

	xPlayer.addMoney(math.random(13, 21))

	TriggerClientEvent("esx:showNotification", src, "Thanks, here you have som cash for food!")
end)

function JailPlayer(jailPlayer, jailTime)
	TriggerClientEvent("esx_jail:jailPlayer", jailPlayer, jailTime)

	EditJailTime(jailPlayer, jailTime)
end

function UnJail(jailPlayer)
	TriggerClientEvent("esx_jail:unJailPlayer", jailPlayer)

	EditJailTime(jailPlayer, 0)
end

function EditJailTime(source, jailTime)

	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)
	local Identifier = xPlayer.identifier

	MySQL.Async.execute(
       "UPDATE users SET jail = @newJailTime WHERE identifier = @identifier",
        {
			['@identifier'] = Identifier,
			['@newJailTime'] = tonumber(jailTime)
		}
	)
end

function GetRPName(playerId, data)
	local Identifier = ESX.GetPlayerFromId(playerId).identifier

	MySQL.Async.fetchAll("SELECT firstname, lastname FROM users WHERE identifier = @identifier", { ["@identifier"] = Identifier }, function(result)

		data(result[1].firstname, result[1].lastname)

	end)
end

ESX.RegisterServerCallback("esx_jail:retrieveJailedPlayers", function(source, cb)
	
	local jailedPersons = {}

	MySQL.Async.fetchAll("SELECT firstname, lastname, jail, identifier FROM users WHERE jail > @jail", { ["@jail"] = 0 }, function(result)

		for i = 1, #result, 1 do
			table.insert(jailedPersons, { name = result[i].firstname .. " " .. result[i].lastname, jailTime = result[i].jail, identifier = result[i].identifier })
		end

		cb(jailedPersons)
	end)
end)

ESX.RegisterServerCallback("esx_jail:retrieveJailTime", function(source, cb)

	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)
	local Identifier = xPlayer.identifier


	MySQL.Async.fetchAll("SELECT jail FROM users WHERE identifier = @identifier", { ["@identifier"] = Identifier }, function(result)

		local JailTime = tonumber(result[1].jail)

		if JailTime > 0 then

			cb(true, JailTime)
		else
			cb(false, 0)
		end

	end)
end)

-- إضافة حدث جديد للتأكد من تحديث حالة السجن
RegisterServerEvent("Abor7mh:setPlayerUnJailed")
AddEventHandler("Abor7mh:setPlayerUnJailed", function(playerId)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        MySQL.Async.execute('UPDATE users SET jail = @jail WHERE identifier = @identifier', {
            ['@identifier'] = xPlayer.identifier,
            ['@jail'] = 0
        })
    end
end)



