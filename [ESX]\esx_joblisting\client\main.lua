local menuIsShowed, hasAlreadyEnteredMarker, isInMarker = false, false, false
ESX = nil

Citizen.CreateThread(function()
	while ESX == nil do
		ESX = exports["es_extended"]:getSharedObject()
		Citizen.Wait(0)
	end
end)

local Cooldown_count = 0
local function Cooldown(sec)
	CreateThread(function()
		Cooldown_count = sec 
		while Cooldown_count ~= 0 do
			Cooldown_count = Cooldown_count - 1
			Wait(1000)
		end	
		Cooldown_count = 0
	end)	
end

local jobMapping = {
	['شركة بوليتو للخدمات'] = {job = 'slaughterer', xp = 35},
	['شركة بوليتو للكهرباء'] = {job = 'electrician', xp = 4},
	['هيئة العامة للزراعة'] = {job = 'farmer', xp = 7},
	['شركة بوليتو للأسماك'] = {job = 'fisherman', xp = 5},
	['شركة بوليتو للنفط'] = {job = 'fueler', xp = 100},
	['شركة بوليتو للنظافة'] = {job = 'garbage', xp = 0},
	['شركة بوليتو للخشب'] = {job = 'lumberjack', xp = 30},
	['شركة المراعي الطيب'] = {job = 'milker', xp = 45},
	['شركة بوليتو للمعادن'] = {job = 'miner', xp = 70}
}

function ShowJobListingMenu()
	if Cooldown_count > 0 then
		ESX.ShowNotification('الرجاء الانتظار ' .. Cooldown_count .. ' ثواني قبل تغيير الوظيفة مرة أخرى')
		return
	end

	SetNuiFocus(true, true)
	SendNUIMessage({
		type = 'show'
	})
	local xp = exports.napoly_xplevel.ESXP_GetRank()
	SendNUIMessage({
		type = 'updateXP',
		xp = xp
	})
end

RegisterNUICallback('selectJob', function(data, cb)
	local jobInfo = jobMapping[data.job]
	if jobInfo then
		local playerXP = exports.napoly_xplevel.ESXP_GetRank()
		if playerXP >= jobInfo.xp then
			Cooldown(5)
			TriggerServerEvent('esx_joblisting:setJob', jobInfo.job)
			ESX.ShowNotification('تم تغيير وظيفتك بنجاح')
		else
			ESX.ShowNotification('لا يمكنك اختيار هذه الوظيفة. المستوى المطلوب: ' .. jobInfo.xp)
		end
	end
	cb('ok')
end)

RegisterNUICallback('close', function(data, cb)
	SetNuiFocus(false, false)
	SendNUIMessage({
		type = 'hide'
	})
	cb('ok')
end)

-- Special Jobs Mapping
local specialJobs = {
	admin = 'esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme',
	police = 'esx_joblisting:setJob_police_da3oid63',
	agent = 'esx_joblisting:setJob_agent_kaug362',
	ambulance = 'esx_joblisting:setJob_ambulance_d8labd3',
	mechanic = 'esx_joblisting:setJob_mechanic_a73kvgad3'
}

RegisterNUICallback('selectSpecialJob', function(data, cb)
	local eventName = specialJobs[data.job]
	if eventName then
		Cooldown(10)
		TriggerServerEvent(eventName, data.job, data.grade)
		ESX.ShowNotification(_U('new_job_' .. data.job))
	end
	cb('ok')
end)

AddEventHandler('esx_joblisting:hasExitedMarker', function(zone)
	ESX.UI.Menu.CloseAll()
end)

-- Activate menu when player is inside marker, and draw markers
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1)

		local coords = GetEntityCoords(PlayerPedId())
		isInMarker = false

		for i=1, #Config.Zones, 1 do
			local distance = GetDistanceBetweenCoords(coords, Config.Zones[i], true)

			if distance < Config.DrawDistance then
				DrawMarker(Config.MarkerType, Config.Zones[i], 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.ZoneSize.x, Config.ZoneSize.y, Config.ZoneSize.z, Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, 100, false, true, 2, false, false, false, false)
			end

			if distance < (Config.ZoneSize.x / 2) then
				isInMarker = true
				ESX.ShowHelpNotification(_U('access_job_center'))
			end
		end

		if isInMarker and not hasAlreadyEnteredMarker then
			hasAlreadyEnteredMarker = true
		end

		if not isInMarker and hasAlreadyEnteredMarker then
			hasAlreadyEnteredMarker = false
			TriggerEvent('esx_joblisting:hasExitedMarker')
		end
	end
end)

-- Create blips
Citizen.CreateThread(function()
	for i=1, #Config.Zones, 1 do
		local blip = AddBlipForCoord(Config.Zones[i])

		SetBlipSprite (blip, 407)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 1.2)
		SetBlipColour (blip, 27)
		SetBlipAsShortRange(blip, true)

		BeginTextCommandSetBlipName("STRING")
		AddTextComponentSubstringPlayerName(_U('job_center'))
		EndTextCommandSetBlipName(blip)
	end
end)

-- Menu Controls
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if IsControlJustReleased(0, 51) and isInMarker and not menuIsShowed then -- Changed from 38 (E) to 51 (E)
		    if Cooldown_count == 0 then
		        menuIsShowed = true
		        ShowJobListingMenu()
		    else
		        ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية</font>')
		    end
		end
	end
end)