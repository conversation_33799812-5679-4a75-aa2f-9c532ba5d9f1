<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز التوظيف</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>الوظائف المتاحة</h2>
            <p>سيتم تحديث وظيفتك الوظيفية قريبا</p>
        </div>
        <div class="job-list">
            <!-- Special Jobs -->
            <div class="special-jobs">
                <div class="job-item special" data-job="police" data-special="true">
                    <span>الشرطة 👮‍♂️</span>
                </div>
                <div class="job-item special" data-job="ambulance" data-special="true">
                    <span>الإسعاف 🚑</span>
                </div>
                <div class="job-item special" data-job="mechanic" data-special="true">
                    <span>الميكانيكي 🔧</span>
                </div>
            </div>
            <!-- Regular Jobs -->
            <div class="job-item" data-job="شركة بوليتو للخدمات" data-xp="35">
                <span>شركة بوليتو للخدمات</span>
                <span class="xp">XP: 35</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للكهرباء" data-xp="4">
                <span>شركة بوليتو للكهرباء</span>
                <span class="xp">XP: 4</span>
            </div>
            <div class="job-item" data-job="هيئة العامة للزراعة" data-xp="7">
                <span>هيئة العامة للزراعة</span>
                <span class="xp">XP: 7</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للأسماك" data-xp="5">
                <span>شركة بوليتو للأسماك</span>
                <span class="xp">XP: 5</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للنفط" data-xp="100">
                <span>شركة بوليتو للنفط</span>
                <span class="xp">XP: 100</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للنظافة" data-xp="0">
                <span>شركة بوليتو للنظافة</span>
                <span class="xp">XP: 0</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للخشب" data-xp="30">
                <span>شركة بوليتو للخشب</span>
                <span class="xp">XP: 30</span>
            </div>
            <div class="job-item" data-job="شركة المراعي الطيب" data-xp="45">
                <span>شركة المراعي الطيب</span>
                <span class="xp">XP: 45</span>
            </div>
            <div class="job-item" data-job="شركة بوليتو للمعادن" data-xp="70">
                <span>شركة بوليتو للمعادن</span>
                <span class="xp">XP: 70</span>
            </div>
        </div>
        <div class="hire-button">توظف الآن</div>
    </div>

    <script>
        let currentXP = 0;
        let selectedJob = null;

        window.addEventListener('message', function(event) {
            if (event.data.type === 'show') {
                document.body.style.display = 'flex';
            } else if (event.data.type === 'hide') {
                document.body.style.display = 'none';
            } else if (event.data.type === 'updateXP') {
                currentXP = event.data.xp;
                updateJobItems();
            }
        });

        function updateJobItems() {
            document.querySelectorAll('.job-item').forEach(item => {
                const requiredXP = parseInt(item.dataset.xp);
                if (currentXP < requiredXP) {
                    item.classList.add('disabled');
                } else {
                    item.classList.remove('disabled');
                }
            });
        }

        document.querySelectorAll('.job-item').forEach(item => {
            item.addEventListener('click', function() {
                const isSpecial = this.dataset.special === 'true';
                if (!isSpecial && this.classList.contains('disabled')) return;
                
                document.querySelectorAll('.job-item').forEach(i => i.style.border = 'none');
                this.style.border = '2px solid #ffa500';
                selectedJob = this.dataset.job;
            });
        });

        document.querySelector('.hire-button').addEventListener('click', function() {
            if (!selectedJob) return;

            const selectedElement = document.querySelector(`[data-job="${selectedJob}"]`);
            const isSpecial = selectedElement.dataset.special === 'true';

            if (isSpecial) {
                fetch(`https://${GetParentResourceName()}/selectSpecialJob`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job: selectedJob,
                        grade: 0
                    })
                });
            } else {
                fetch(`https://${GetParentResourceName()}/selectJob`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job: selectedJob
                    })
                });
            }

            fetch(`https://${GetParentResourceName()}/close`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });
        });

        document.addEventListener('keyup', function(event) {
            if (event.key === 'Escape') {
                fetch(`https://${GetParentResourceName()}/close`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
            }
        });
    </script>
</body>
</html>