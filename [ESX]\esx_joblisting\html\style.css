* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    direction: rtl;
}

body {
    width: 100vw;
    height: 100vh;
    display: none;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.container {
    width: 400px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 10px;
    padding: 20px;
    color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h2 {
    color: #ffa500;
    margin-bottom: 5px;
}

.header p {
    color: #666;
    font-size: 14px;
}

.job-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.special-jobs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 165, 0, 0.3);
}

.job-item.special {
    flex: 1;
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid #ffa500;
    text-align: center;
    font-weight: bold;
}

.job-item.special:hover {
    background: rgba(255, 165, 0, 0.2);
    transform: translateY(-2px);
}

.job-list::-webkit-scrollbar {
    width: 5px;
}

.job-list::-webkit-scrollbar-track {
    background: rgba(50, 50, 50, 0.3);
    border-radius: 5px;
}

.job-list::-webkit-scrollbar-thumb {
    background: #ffa500;
    border-radius: 5px;
}

.job-item {
    background: rgba(50, 50, 50, 0.8);
    padding: 15px;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
}

.job-item:hover:not(.disabled) {
    background: rgba(70, 70, 70, 0.8);
    transform: translateX(-5px);
}

.xp {
    color: #ffa500;
    font-weight: bold;
}

.hire-button {
    background: #ffa500;
    color: black;
    padding: 15px;
    text-align: center;
    border-radius: 5px;
    margin-top: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: bold;
}

.hire-button:hover {
    background: #ff8c00;
    transform: scale(1.02);
}

.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
}

.disabled .xp {
    color: #ff4444;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
}

.disabled:hover {
    animation: shake 0.4s ease-in-out;
}