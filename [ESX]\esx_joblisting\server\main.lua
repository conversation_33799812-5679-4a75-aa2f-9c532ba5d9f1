ESX = nil
local availableJobs = {}

ESX = exports["es_extended"]:getSharedObject()

MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT name, label FROM jobs WHERE whitelisted = @whitelisted', {
        ['@whitelisted'] = false
    }, function(result)
        for i=1, #result, 1 do
            table.insert(availableJobs, {
                job = result[i].name,
                label = result[i].label,
                grade   = result[i].grade
            })
        end
    end)
end)

local disroles = {
    ['1300888332577800303'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ جندي متدرب", 0},
    ['1300888330841493569'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ جندي اول", 1},
    ['1300888329729872003'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ عريف", 2},
    ['1300888328165654568'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ وكيل رقيب" ,3},
    ['1300888326848647220'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ رقيب", 4},
    ['1300888325640425602'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ رقيب أول", 5},
    ['1300888324042395648'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ رئيس رقباء", 6},
    ['1300888322805203025'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ ملازم", 7},
    ['1300888321060503695'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ ملازم اول", 8},
    ['1300888319886102588'] = {"police", "<span style='color:#34aeeb;'> 👮🏼♂️ نقيب", 9},
    ['1300888312273305731'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ رائد", 10},
    ['1300888310444462090'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ مقدم", 11},
    ['1300888309081444372'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ عقيد", 12},
    ['1300888307596529778'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ عميد", 13},
    ['1300888306191433799'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ لواء", 14},
    ['1300888304413315072'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ فريق", 15},
    ['1300888303087652935'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ فريق اول", 16},
    ['1300888301720305745'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ نائب قائد الشرطة", 17},
    ['1300888300239982653'] = {"police","<span style='color:#34aeeb;'> 👮🏼♂️ قائد الشرطة", 18},
  
    ------------------------------------------------------------------------------------------------------------------------------------
  
    ['1300888419991556107'] = {"agent","<span style='color:#76ff03;'> ♂️ جندي", 0},
    ['1300888418594586665'] = {"agent","<span style='color:#76ff03;'> ♂️ جندي اول", 1},
    ['1300888417219117118'] = {"agent","<span style='color:#76ff03;'> ♂️ عريف", 2},
    ['1300888415969083462'] = {"agent","<span style='color:#76ff03;'> ♂️ وكيل رقيب", 3},
    ['1300888414756929637'] = {"agent","<span style='color:#76ff03;'> ♂️ رقيب", 4},
    ['1300888412080832662'] = {"agent","<span style='color:#76ff03;'> ♂️ رقيب اول", 5},
    ['1300888410168365101'] = {"agent","<span style='color:#76ff03;'> ♂️ رئيس رقباء", 6},
    ['1300888409325436949'] = {"agent","<span style='color:#76ff03;'> ♂️ ملازم", 7},
    ['1300888408096374815'] = {"agent","<span style='color:#76ff03;'> ♂️ ملازم اول", 8},
    ['1300888406796013598'] = {"agent","<span style='color:#76ff03;'> ♂️ نقيب", 9},
    ['1300888405546373241'] = {"agent","<span style='color:#76ff03;'> ♂️ رائد", 10},
    ['1300888404103270440'] = {"agent","<span style='color:#76ff03;'> ♂️ مقدم", 11},
    ['1300888402807357541'] = {"agent","<span style='color:#76ff03;'> ♂️ عقيد", 12},
    ['1300888401439887455'] = {"agent","<span style='color:#76ff03;'> ♂️ عميد", 13},
    ['1300888388043542650'] = {"agent","<span style='color:#76ff03;'> ♂️ لواء", 14},
    ['1301604406541815953'] = {"agent","<span style='color:#76ff03;'> ♂️ فريق", 15},
    ['1301604417589739651'] = {"agent","<span style='color:#76ff03;'> ♂️ نائب قائد امن المنشئات", 17},
    ['1300888391147323432'] = {"agent","<span style='color:#76ff03;'> ♂️  قائد امن المنشئات", 17},
  
    ------------------------------------------------------------------------------------------------------------------------------------
  
    ["1300888359777996821"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر  - متدرب", 0},
    ["1300888357789765643"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر - مستوى 1", 1},
    ["1300888356355444807"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر - مستوى 2 ", 2},
    ["1300888355013136415"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر - مستوى 3  ", 3},
    ["1300888353708965888"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر -  مستوى 4 ", 4},
    ["1300888352190631996"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر -  مستوى 5 ", 5},
    ["1300888350844129370"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر -  مستوى 6 ", 6},
    ["1300888349388574841"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر-  مستوى 7 ", 7},
    ["1300888347878625280"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر -  مستوى 8 ", 8},
    ["1300888346159222866"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر -  مستوى 9 ", 9},
    ["1300888337921605634"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر-  نائب قائد", 11},
    ["1300888336596074537"] = {"ambulance", "<span style='color:#DC143C;'> الهلال الاحمر - قائد", 12},
  
    ------------------------------------------------------------------------------------------------------------------------------------
  
    ['1300888386474737747'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - متدرب", 0},
    ['1300888385149341766'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 1", 1},
    ['1300888383798644929'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 2", 2},
    ['1300888382527770665'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 3", 3},
    ['1300888381055828028'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 4", 4},
    ['1300888379575111710'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 5", 5},
    ['1300888378186793071'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 6", 6},
    ['1300888376823779378'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 7", 7},
    ['1300888375519215646'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 8", 8},
    ['1300888374282031104'] = {"mechanic", "<span style='color:gray;'> كراج الميكانيك - مستوى 9", 9},
    ['1300888366153207868'] = {"mechanic","<span style='color:#808080;'> 🔧 نائب مدير كراج الميكانيك" , 10},
    ['1300888364592922624'] = {"mechanic","<span style='color:#808080;'> 🔧 مدير كراج الميكانيك", 11},
  
    ------------------------------------------------------------------------------------------------------------------------------------
  
    ['1301608322428047420'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق ", 0},
    ['1301608348734591026'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق معتمد", 1},
    ['1301608350538137651'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق محترف", 2},
    ['1301608325506400279'] = {"taxi","<span style='color:#ff9400;'> 🚗 نائب مدير شركة التاكسي", 3},
    ['1301608315234680912'] = {"taxi","<span style='color:#ff9400;'> 🚗 مدير شركة التاكسي", 4},
  
    ------------------------------------------------------------------------------------------------------------------------------------
  
    ['1300888208661282900'] = {"admin","<span style='color:#cc0000;'> ♂️ مفتش", 1},
    ['1300888207197737130'] = {"admin","<span style='color:#cc0000;'> ♂️ مراقب", 2},
    ['1300888205629067295'] = {"admin","<span style='color:#cc0000;'> ♂️ مراقب اول", 3},
    ['1300888205629067295'] = {"admin","<span style='color:#cc0000;'> ♂️ مراقب عام ", 4},
}

ESX.RegisterServerCallback('esx_joblisting:getJobsList', function(source, cb)
    local src = source
    local disjobs = {}
    for k, v in pairs(availableJobs) do 
        disjobs[k] = v 
    end

    for k, v in pairs(exports['discord_perms'].GetRoles(src, src)) do
        for i, j in pairs(disroles) do 
            if i == v then 
                table.insert(disjobs, {
                    job = j[1],
                    label = j[2],
                    grade = j[3],
                })
                break
            end
        end
    end
    cb(disjobs)
end)

RegisterServerEvent('esx_joblisting:setJob_kaugy36')
AddEventHandler('esx_joblisting:setJob_kaugy36', function(newjob, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	if xPlayer.job.name == "admin" then
	xPlayer.setJob(newjob, grade)
	local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة إدارة الرقابة و التفتيش الى وظيفة أخرى"

    TriggerEvent("napoly-logs:server:SendLog", "changadmin", "**تغير وظيفة من وظيفة الرقابة و التفتيش الى وظيفة اخرى**", "red", message)	
	elseif xPlayer.job.name == "police" then
	xPlayer.setJob(newjob, grade)
	local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة إدارة الشرطة الى وظيفة اخرى"

    TriggerEvent("napoly-logs:server:SendLog", "changaPolice", "**تغير وظيفة من وظيفة إدارة الشرطة الى وظيفة اخرى**", "red", message)	
	elseif xPlayer.job.name == "agent" then
	xPlayer.setJob(newjob, grade)
	local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة امن المنشات الى وظيفة أخرى"

    TriggerEvent("napoly-logs:server:SendLog", "changagent", "**تغير وظيفة من وظيفة امن المنشات الى وظيفة أخرى**", "red", message)	
	elseif xPlayer.job.name == "ambulance" then
	xPlayer.setJob(newjob, grade)
	local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة الهلال الاحمر الى وظيفة أخرى"

    TriggerEvent("napoly-logs:server:SendLog", "changambulance", "**تغير وظيفة من وظيفة الهلال الاحمر الى وظيفة أخرى**", "red", message)	
	elseif xPlayer.job.name == "mechanic" then
	xPlayer.setJob(newjob, grade)
	local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\nقام بتغير وظيفته من وظيفة كراج الميكانيك الى وظيفة اخرى"

    TriggerEvent("napoly-logs:server:SendLog", "changmechanic", "**تغير وظيفة من وظيفة كراج الميكانيكي الى وظيفة أخرى**", "red", message)	
	else
	xPlayer.setJob(newjob, grade)
    end
    end
end)

RegisterServerEvent('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme')
AddEventHandler('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
        
	        local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `الرقابة و التفتيش` \n المستوى الوظيفي : "..grade..""
            
            TriggerEvent("napoly-logs:server:SendLog", "changadmin", "**تغير وظيفة الى وظيفة الرقابة و التفتيش**", "green", message)
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_police_da3oid63')
AddEventHandler('esx_joblisting:setJob_police_da3oid63', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then

        local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `إدارة الشرطة` \n المستوى الوظيفي : "..grade..""

         TriggerEvent("napoly-logs:server:SendLog", "changaPolice", "**تغير وظيفة الى وظيفة إدارة الشرطة**", "green", message)
         xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_agent_kaug362')
AddEventHandler('esx_joblisting:setJob_agent_kaug362', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then

	        local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `امن المنشات` \n المستوى الوظيفي : "..grade..""

            TriggerEvent("napoly-logs:server:SendLog", "changagent", "**تغير وظيفة الى وظيفة امن المنشات**", "green", message)
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_ambulance_d8labd3')
AddEventHandler('esx_joblisting:setJob_ambulance_d8labd3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then

	        local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `الهلال الاحمر \n المستوى الوظيفي : "..grade..""
            TriggerEvent("napoly-logs:server:SendLog", "changambulance", "**تغير وظيفة الى وظيفة الهلال الاحمر**", "green", message)
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_mechanic_a73kvgad3')
AddEventHandler('esx_joblisting:setJob_mechanic_a73kvgad3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
            local message = "اللاعب \nname:`"..GetPlayerName(source).."` | `"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."`\n الهوية: `"..xPlayer.getName().."`\n من وظيفة: **"..xPlayer.job.label.." - "..xPlayer.job.grade_label.."** \n الى وظيفة: `كراج الميكانيك \n المستوى الوظيفي : "..grade..""
            TriggerEvent("napoly-logs:server:SendLog", "changmechanic", "**تغير وظيفة الى وظيفة كراج الميكانيك**", "green", message)
			xPlayer.setJob(job, grade)
    end
end)
