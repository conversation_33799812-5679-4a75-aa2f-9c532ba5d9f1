Citizen.CreateThread(function()
    
	while ESX.GetPlayerData().job == nil do
			Citizen.Wait(500)
	end
	Citizen.Wait(100)
	TriggerServerEvent('esx_misc:GetCache')
	Citizen.Wait(1000)
	TriggerServerEvent('esx_misc:StartAutoGift')
end)
-- CONFIG --
local playersOnline = 0
local id, name = GetPlayerServerId(PlayerId()), GetPlayerName(PlayerId())
local idoffset,idoffset2,idoffset3

local promotion = { --msg1 = big label : msg2 = small label true :  msg3 = small label false -- if You want to add custom coler Color={r=61,g=174,b=255}
	--[''] = false,

	['WaitingDrugs'] = {status=false,data='',label='طابور تسجيل تهريب',msg1='ﺐﻳﺮﻬﺗ ﻞﻴﺠﺴﺗ رﻮﺑﺎﻃ',msg2='',msg3='',DrawTextY=0,Color={r=247,g=0,b=255}, timer=false},
	['selldrugs'] = {status=false,label='تهريب ممنوعات',msg1='',msg2='',msg3='',DrawTextY=0,Color={r=222,g=110,b=4}, timer=false,min=0,sec=0},

	--# double
	['StartRobbery'] = {status=false,label='وقت السرقة',msg1='ﺔﻗﺮﺴﻟﺍ ﺖﻗﻭ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=179,g=119,b=0}, timer=false,min=0,sec=0},
	['doubleXP_store'] = {status=false,label='ضعف الخبرة متجر نابولي',msg1='ﺮﺠﺘﻤﻟﺍ ﺮﺠﺘﻣ ﺓﺮﺒﺨﻟﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0,Color={r=255,g=217,b=0}, timer=false, doubleStore=false,time=0, displayTime=""}, -- TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP', true)
	['ShowrobTimer'] = {status=false,label='وقت السرقة',msg1='ﺔﻗﺮﺴﻟﺍ ﺖﻗﻭ',msg2='ﺔﻗﺮﺴﻟﺍ ﺖﻗﻭ ﺃﺪﺑ',msg3='ﺔﻗﺮﺴﻟﺍ ﺖﻗﻭ ﻰﻬﺘﻧﺍ',DrawTextY=0,Color={r=187,g=90,b=17}, timer=false, robTimer=false,time=0, displayTime=""},
	['doubleXP'] = {status=false,label='ضعف الخبرة',msg1='ﺓﺮﺒﺨﻟﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false}, -- TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP', true)
	['doubleStoreBoxQty'] = {status=false,label='ضعف صندوق المتجر',msg1='ﺮﺠﺘﻤﻟﺍ ﻕﻭﺪﻨﺻ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['lumberjack'] = {status=false,label='ضعف الأجر أخشاب',msg1='ﺏﺎﺸﺧﺃ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['miner'] = {status=false,label='ضعف الأجر معادن',msg1='ﻥﺩﺎﻌﻣ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['slaughterer'] = {status=false,label='ضعف الأجر دواجن',msg1='ﻦﺟﺍﻭﺩ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false,},
	['tailor'] = {status=false,label='ضعف الأجر أقمشة',mCVVsg1='ﺔﺸﻤﻗﺃ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['fueler'] = {status=false,label='ضعف الأجر نفط وغاز',msg1='ﺯﺎﻏﻭ ﻂﻔﻧ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['farmer'] = {status=false,label='ضعف الأجر العنب',msg1='ﺐﻨﻌﻟﺍ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['fork'] = {status=false,label='ضعف الأجر خدمات الموانئ',msg1='ﺊﻧﺍﻮﻤﻟﺍ ﺕﺎﻣﺪﺧ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['fisherman'] = {status=false,label='ضعف الأجر الأسماك',msg1='ﻙﺎﻤﺳﻷﺍ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['vegetables'] = {status=false,label='ضعف الأجر الخضروات',msg1='ﺕﺍﻭﺮﻀﺨﻟﺍ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['police_level'] = {status=false,label='ضعف الخبره للوظايف الحكومية',msg1='ﺔﻴﻣﻮﻜﺤﻟﺍ ﻒﻳﺎﻇﻮﻠﻟ هﺮﺒﺨﻟﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=3, Color={r=27,g=114,b=211}, timer=false,numbers = false,show = true,Sprite = true,dict = "mprpsymbol",Spritename = "rp"},
	['beekeeper'] = {status=false,label='ضعف الأجر المناحل',msg1='ﻞﺣﺎﻨﻤﻟﺍ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	['milker'] = {status=false,label='ضعف الأجر المراعي',msg1='ﻲﻋﺍﺮﻤﻟﺍ ﺮﺟﻷﺍ ﻒﻌﺿ',msg2='ﺽﺮﻌﻟﺍ ﺃﺪﺑ',msg3='ﺽﺮﻌﻟﺍ ﻰﻬﺘﻧﺍ',DrawTextY=0, timer=false},
	--# Other
	['SafeZone'] = {status=false,label='منطقة آمنة',msg1='ﺔﻨﻣﺁ ﺔﻘﻄﻨﻣ',msg2='',msg3='',DrawTextY=0,Color={r=46,g=204,b=113}, timer=false},

	['opodartheist'] = {status = false,label = 'سرقة معرض الفنون',msg1 = 'ﻥﻮﻨﻔﻟﺍ ﺽﺮﻌﻣ ﺔﻗﺮﺳ',msg2 = 'ﻉﻮﻨﻤﻣ',msg3 = 'ﺡﻮﻤﺴﻣ',DrawTextY = 0,Color = {r=255, g=0, b=0},timer = false,min = 0,sec = 0},
	['NoCrimetime'] = {status=false,label='ممنوع الأجرام',msg1='ﻡﺍﺮﺟﻷﺍ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['NoCrimetimeSandy'] = {status=false,label='ممنوع الأجرام ساندي وبوليتو',msg1='ﻮﺘﻴﻟﻮﺑﻭ ﻱﺪﻧﺎﺳ ﻡﺍﺮﺟﻷﺍ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['NoCrimetimeCayo'] = {status=false,label='ممنوع الأجرام جزيرة نابولي',msg1='ﻲﻟﻮﺑﺎﻧ ةﺮﻳﺰﺟ ﻡﺍﺮﺟﻷﺍ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['NoCrimetimeLos'] = {status=false,label='ممنوع الأجرام لوس سانتوس',msg1='سﻮﺘﻧﺎﺳ سﻮﻟ ﻡﺍﺮﺟﻷﺍ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['NewScenario'] = {status=false,label='ممنوع بدأ سيناريو جديد',msg1='ﺪﻳﺪﺟ ﻮﻳﺭﺎﻨﻴﺳ ﺃﺪﺑ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},

	['MainBank'] = {status=false,label='ممنوع سرقة البنك المركزي',msg1='ﻱﺰﻛﺮﻤﻟﺍ ﻚﻨﺒﻟﺍ ﺔﻗﺮﺳ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['SmallBanks'] = {status=false,label='ممنوع سرقة البنوك الصغيرة',msg1='ﺓﺮﻴﻐﺼﻟﺍ ﻙﻮﻨﺒﻟﺍ ﺔﻗﺮﺳ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['Stores'] = {status=false,label='ممنوع سرقة المتاجر',msg1='ﺮﺟﺎﺘﻤﻟﺍ ﺔﻗﺮﺳ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['SellingDrugs'] = {status=false,label='ممنوع تهريب الممنوعات',msg1='ﺕﺎﻋﻮﻨﻤﻤﻟﺍ ﺐﻳﺮﻬﺗ ﻉﻮﻨﻤﻣ',msg2='ﻉﻮﻨﻤﻣ',msg3='ﺡﻮﻤﺴﻣ',DrawTextY=0,Color={r=255,g=0,b=0},timer=false,min=0,sec=0},
	['Deletecars'] = {status=false,label='وقت حذف المركبات',msg1='تﺎﺒﻛﺮﻤﻟﺍ فﺬﺣ ﺖﻗﻭ',msg2='أﺪﺑ',msg3='ءﺎﻬﺘﻧﺍ',DrawTextY=0,Color={r=0,g=120,b=255},timer=false,min=0,sec=0},

}

local portsControl = {
[1] = {status=true,label='الميناء البحري الرئيسي',msg1='ﻲﺴﻴﺋﺮﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[2] = {status=false,label='الميناء البحري الغربي',msg1='ﻲﺑﺮﻐﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[3] = {status=false,label='المطار الدولي',msg1='ﻲﻟﻭﺪﻟﺍ ﺭﺎﻄﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[4] = {status=false,label='القاعدة العسكرية',msg1='ﺔﻳﺮﻜﺴﻌﻟﺍ ﺓﺪﻋﺎﻘﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[11] = {status=false,label='توسعة 1',msg1='1 ﺔﻌﺳﻮﺗ يﺮﺤﺒﻟﺍ ءﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[12] = {status=false,label='توسعة 2',msg1='2 ﺔﻌﺳﻮﺗ يﺮﺤﺒﻟﺍ ءﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[13] = {status=false,label='توسعة 3',msg1='3 ﺔﻌﺳﻮﺗ يﺮﺤﺒﻟﺍ ءﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
[14] = {status=false,label='توسعة 4',msg1='4 ﺔﻌﺳﻮﺗ يﺮﺤﺒﻟﺍ ءﺎﻨﻴﻤﻟﺍ',msg2='ﻥﻵﺍ ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ',msg3='ﺮﻳﺪﺼﺘﻟﺍ ﻒﻗﻭ',DrawTextY=0},
}

--servername = "https://discord.gg/2ZTzJxu   (=) Keys Info   (L Shift + H) Voice Range   Players:"

-- The x and y offset (starting at the top left corner) --
-- Default: 0.005, 0.001
-- Down middle right: 0.45, 0.955
-- Top middle right: 0.53, 0.001
-- under compass: 0.174, 0.955
local offset = {x = 0.174, y = 0.955}

-- Text RGB Color --
-- Default: 64, 64, 64 (gray)
local rgb = {r = 100, g = 100, b = 100}

-- Text transparency --
-- Default: 255
local alpha = 255

-- Text scale
-- Default: 0.4
-- NOTE: Number needs to be a float (so instead of 1 do 1.0)
local scale = 0.31

-- Text Font --
-- 0 - 5 possible
-- Default: 1
--1 ok italic
--4 ok normal
local font = 4

-- Rainbow Text --
-- false: Turn off
-- true: Activate rainbow text (overrides color)
local bringontherainbows = true


-- CODE --

-- By ash
function RGBRainbow(frequency)
	local result = {}
	local curtime = GetGameTimer() / 1000

	result.r = math.floor(math.sin(curtime * frequency + 0) * 60 + 128)
	result.g = math.floor(math.sin(curtime * frequency + 2) * 60 + 128)
	result.b = math.floor(math.sin(curtime * frequency + 4) * 60 + 128)

	return result
end

function getPromotions()
data = {}
for k,v in pairs(promotion) do
	table.insert(data, {name = k, status = v.status, label = v.label})
end
return data
end

function getPorts()
data = {}
for k,v in pairs(portsControl) do
	table.insert(data, {name = k, status = v.status, label = v.label})
end
return data
end

RegisterNetEvent('esx_misc:updatePromotionStatus')
AddEventHandler('esx_misc:updatePromotionStatus', function(job, data)
if job ~= 'jobs' then
	if promotion[job] then
		promotion[job].status = data
	end
else
	for k,v in pairs(data) do
		promotion[k].status = v
	end
end
watermarkResetDrawTextY()
end)

RegisterNetEvent('esx_misc:updatePromotionTimer')
AddEventHandler('esx_misc:updatePromotionTimer', function(_Promotion, timerStatus, min, sec)
	-- Input validation
	if not _Promotion then 
			print("^1ERROR: Invalid promotion name^7")
			return 
	end

	-- Check if promotion exists
	if not promotion[_Promotion] then
			print(("^1ERROR: Promotion '%s' not found^7"):format(_Promotion))
			return
	end

	-- Update timer status and values
	if timerStatus then
			promotion[_Promotion].timer = true
			promotion[_Promotion].min = tonumber(min) or 0
			promotion[_Promotion].sec = tonumber(sec) or 0
	else
			promotion[_Promotion].timer = false
			promotion[_Promotion].min = 0
			promotion[_Promotion].sec = 0
	end

	-- Update display
	if watermarkResetDrawTextY then
			watermarkResetDrawTextY()
	end
end)

RegisterNetEvent('esx_misc:updatePromotionTimer_duobleXP_store')
AddEventHandler('esx_misc:updatePromotionTimer_duobleXP_store', function(status, time)
	if status then
promotion["doubleXP_store"].doubleStore = true
promotion["doubleXP_store"].time = tonumber(time)
duobleXP_storeTimer()
else
promotion["doubleXP_store"].doubleStore = false
end
end)

RegisterNetEvent('esx_misc:updatePromotionTimer_ShowrobTimer')
AddEventHandler('esx_misc:updatePromotionTimer_ShowrobTimer', function(status, time)
if status then
	promotion["ShowrobTimer"].robTimer = true
	promotion["ShowrobTimer"].time = tonumber(time)
	ShowrobTimerTimer()
else
	promotion["ShowrobTimer"].robTimer = false
end
end)

function ShowrobTimerTimer()
if promotion["ShowrobTimer"].time <= 0 then
	if promotion["ShowrobTimer"].robTimer then
		TriggerServerEvent("napoly_xplevel:Remove_StoreDoubleXP")
	end
	return
end
promotion["ShowrobTimer"].time = promotion["ShowrobTimer"].time-1

ConvertTimetoDisplay2(promotion["ShowrobTimer"].time)

Wait(1000)
ShowrobTimerTimer()
end

local updateServer = 0
function duobleXP_storeTimer()
if promotion["doubleXP_store"].time <= 0 then
		 if promotion["doubleXP_store"].doubleStore then
	print('sop double')
		 TriggerServerEvent("napoly_xplevel:Remove_StoreDoubleXP")
	 end
	 return
end
	-- print(updateServer)
	if updateServer >= 60 then
	 TriggerServerEvent("napoly_xplevel:reduce_StoreDoubleXP")
 updateServer = 0
end

updateServer = updateServer+1
	promotion["doubleXP_store"].time = promotion["doubleXP_store"].time-1

ConvertTimetoDisplay(promotion["doubleXP_store"].time)
	
Wait(1000)
duobleXP_storeTimer()
end

function ConvertTimetoDisplay(time) --displayTime
local RipTime = time
local remainingseconds = RipTime/ 60
local ripseconds = string.format("%02.f", math.floor(RipTime) % 60 )
local remainingminutes = remainingseconds / 60
local ripminutes = string.format("%02.f", math.floor(remainingseconds) % 60)
local remaininghours = remainingminutes / 24
local riphours = string.format("%02.f", math.floor(remainingminutes) % 24)
local remainingdays = remaininghours / 365
local ripdays = string.format("%02.f", math.floor(remaininghours) % 365)

promotion["doubleXP_store"].displayTime = riphours..':' ..ripminutes.. ':' ..ripseconds
end

function ConvertTimetoDisplay2(time) --displayTime
local RipTime = time
local remainingseconds = RipTime/ 60
local ripseconds = string.format("%02.f", math.floor(RipTime) % 60 )
local remainingminutes = remainingseconds / 60
local ripminutes = string.format("%02.f", math.floor(remainingseconds) % 60)
local remaininghours = remainingminutes / 24
local riphours = string.format("%02.f", math.floor(remainingminutes) % 24)
local remainingdays = remaininghours / 365
local ripdays = string.format("%02.f", math.floor(remaininghours) % 365)

promotion["ShowrobTimer"].displayTime = riphours..':' ..ripminutes.. ':' ..ripseconds
end

RegisterNetEvent('esx_misc:currentPortNum')
AddEventHandler('esx_misc:currentPortNum', function(port)
for k,v in pairs(portsControl) do
	if k ~= port then
		v.status = false
	end
end
portsControl[port].status = true
watermarkResetDrawTextY()
end)

Citizen.CreateThread(function()
while not switchstate do
	Citizen.Wait(500)
end

local blueColor = {r=61,g=174,b=255}
local white = {r=255,g=255,b=255}

watermarkResetDrawTextY()

while true do
	Citizen.Wait(0)
	local sleep = true
	
	if bringontherainbows then
		sleep = false
		rgb = RGBRainbow(1)
	end
	
	if not hidehud then

		sleep = false

		--keys
	--	SetTextColour(206,82,0,alpha)
	--	SetTextFont(fontId)
	--	SetTextScale(0.4, 0.4)
	--	SetTextWrap(0.0, 1.0)
	--	SetTextCentre(false)
	--	SetTextDropshadow(2, 2, 0, 0, 0)
	--	SetTextEdge(1, 0, 0, 0, 205)
	--	SetTextOutline()
	--	SetTextEntry("STRING")
	--	AddTextComponentString('Home')
	--	DrawText(0.888, 0.498)
		
	--	SetTextColour(150,150,150,alpha)
	--	SetTextFont(fontId)
	--	SetTextScale(scale, scale)
	--	SetTextWrap(0.0, 1.0)
	--	SetTextCentre(false)
	--	SetTextDropshadow(2, 2, 0, 0, 0)
	--	SetTextEdge(1, 0, 0, 0, 205)
	--	SetTextOutline()
	--	SetTextEntry("STRING")
	--	AddTextComponentString('<FONT FACE = "A9eelsh">ﻡﻼﻜﻟﺍ ﻯﺪﻣ') --الاختصارات
	--	DrawText(0.933, 0.500)
		
		--key
		
		--Playeronline
					-- رقم اللاعبين باللون الأبيض
					SetTextColour(255, 255, 255, alpha)
					SetTextFont(fontId)
					SetTextScale(0.31, 0.31)
					SetTextWrap(0.0, 1.0)
					SetTextCentre(false)
					SetTextDropshadow(2, 2, 0, 0, 0)
					SetTextEdge(43, 0, 0, 0, 205)
					SetTextOutline()
					SetTextEntry("STRING")
					AddTextComponentString(playersOnline)
					DrawText(0.205, 0.961)  -- تم تعديل X من 0.014 إلى 0.024

					-- نص "عدد اللاعبين" بألوان قوس قزح
					if bringontherainbows then
							SetTextColour(rgb.r, rgb.g, rgb.b, alpha)
					else
							SetTextColour(255, 255, 255, alpha)
					end
					SetTextFont(fontId)
					SetTextScale(0.31, 0.31)
					SetTextWrap(0.0, 1.0)
					SetTextCentre(false)
					SetTextDropshadow(2, 2, 0, 0, 0)
					SetTextEdge(43, 0, 0, 0, 205)
					SetTextOutline()
					SetTextEntry("STRING")
					AddTextComponentString('<FONT FACE = "A9eelsh"> :ﻦﻴﺒﻋﻼﻟﺍ ﺩﺪﻋ')
					DrawText(0.220, 0.961)  -- تم تعديل X ليتناسب مع موقع الرقم الجديد
		--[[
		SetTextColour(255, 255, 255, alpha)
		SetTextFont(fontId)
		SetTextScale(scale, scale)
		SetTextWrap(0.0, 1.0)
		SetTextCentre(false)
		SetTextDropshadow(2, 2, 0, 0, 0)
		SetTextEdge(1, 0, 0, 0, 205)
		SetTextOutline()
		SetTextEntry("STRING")
		AddTextComponentString('<FONT FACE = "A9eelsh">'..id..' :ﺮﻓﺮﻴﺴﻟﺎﺑ ﻚﻤﻗﺭ')
		DrawText(idoffset2, 0.769)
		]]
		
		for k,v in pairs(promotion) do -- Color
			if v.status then

				sleep = false
					
				if v.Color then
					SetTextColour(v.Color.r, v.Color.g, v.Color.b, alpha)
				else
					SetTextColour(blueColor.r, blueColor.g, blueColor.b, alpha)
				end
				SetTextFont(fontId)
				SetTextScale(scale, scale)
				SetTextWrap(0.0, 1.0)
				SetTextCentre(false)
				SetTextDropshadow(2, 2, 0, 0, 0)
				SetTextEdge(1, 0, 0, 0, 205)
				SetTextOutline()
				SetTextEntry("STRING")
				if v.timer then
					--if v.min > 0 or v.sec > 0 then
					if v.min > 0 then
						AddTextComponentString(('%s:%s %s'):format(v.min, v.sec, v.msg1))
					else
						AddTextComponentString(('%s %s'):format(v.sec, v.msg1))
					end
					elseif v.doubleStore then
					AddTextComponentString(('%s %s'):format(v.displayTime, v.msg1))
				elseif v.robTimer then
					AddTextComponentString(('%s %s'):format(v.displayTime, v.msg1))
				else
					AddTextComponentString(('%s'):format(v.msg1))
				end
				DrawText(0.174, v.DrawTextY)
			end
		end
		
					for k,v in pairs(portsControl) do
							if v.status then
									sleep = false
							
									SetTextColour(white.r, white.g, white.b, alpha)
									SetTextFont(fontId)
									SetTextScale(0.20, 0.28)
									SetTextWrap(0.0, 1.0)
									SetTextCentre(false)
									SetTextDropshadow(2, 2, 0, 0, 255)
									SetTextEdge(1, 0, 0, 0, 205)
									SetTextOutline()
									SetTextEntry("STRING")
									AddTextComponentString(v.msg1..' :ﺮﻳﺪﺼﺘﻟﺍ ﻊﻗﻮﻣ')
									DrawText(0.174, 0.935)  -- تم تغيير X من 0.015 إلى 0.174 لتتماشى مع عدد اللاعبين
							end
					end
	end
	if sleep then
		Citizen.Wait(500)
	end
end
end)


Citizen.CreateThread(function()
local sleep = 0

if Config.OnesyncInfinty then 
	sleep = 1000
else
	sleep = 30000
end

while true do

	TriggerServerEvent("esx_misc:onlineplayersserver")
	
	if playersOnline >= 100 then
		idoffset = 0.034
		idoffset2 = 0.103
	elseif playersOnline >= 10 then
		idoffset = 0.027
		idoffset2 = 0.103
		--idoffset3 = 0.335
	else
		idoffset = 0.020
		idoffset2 = 0.103
		--idoffset3 = 0.325
	end
	
	Citizen.Wait(30000)
end
end)

RegisterNetEvent('esx_misc:onlineplayers')
AddEventHandler('esx_misc:onlineplayers', function(count)
-- for _, playerId in ipairs(count) do
-- 	if #count ~= playersOnline then
		playersOnline = #count
-- 	end
-- end
end)

RegisterNetEvent('esx_misc:watermark_promotion')
AddEventHandler('esx_misc:watermark_promotion', function(promotionName, promotionStatus, mode)
-- تحقق من وجود جميع المتغيرات المطلوبة
if not promotion then
	print("^1ERROR: promotion table is nil^0")
	return
end
	
if not promotionName then
	print("^1ERROR: promotionName is nil^0")
	return
end
	
if not promotion[promotionName] then
	print("^1ERROR: promotion[" .. tostring(promotionName) .. "] does not exist^0")
	return
end
promotion[promotionName].status = promotionStatus
watermarkResetDrawTextY()
	
local msg1 = "<FONT FACE='A9eelsh'>~w~"..promotion[promotionName].msg1 --big
local msg2
if mode ~= 2 then
	if promotionStatus then --promotion start
		msg2 = "<FONT FACE='A9eelsh'>~g~"..promotion[promotionName].msg2 --small
		PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	elseif promotionName ~= 'doubleXP_store' then --promotion finish
		msg2 = "<FONT FACE='A9eelsh'>~r~"..promotion[promotionName].msg3 --small
		PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
	end
else
		if promotionStatus then --promotion start
			msg1 = "<FONT FACE='A9eelsh'>~w~"..promotion[promotionName].msg2 --big
			msg2 = "<FONT FACE='A9eelsh'>~r~"..promotion[promotionName].msg1 --small
				PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
		elseif promotionName ~= 'doubleXP_store' then --promotion finish
			msg1 = "<FONT FACE='A9eelsh'>~w~"..promotion[promotionName].msg3 --big
			msg2 = "<FONT FACE='A9eelsh'>~g~"..promotion[promotionName].msg1 --small
			PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
		end
end

local temps = 0
if promotionName == 'doubleXP_store' and promotionStatus == false then return end
scaleform = InitializeScaleform("mp_big_message_freemode",msg1,msg2)

while temps<200 do
	Citizen.Wait(0)
	DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255, 0)
	temps = temps + 1
end
end)


RegisterNetEvent('esx_misc:watermark_port')
AddEventHandler('esx_misc:watermark_port', function(portNum, data)
	--[[
for k,v in pairs(data) do
	portsControl[k].status = v
end]]
watermarkResetDrawTextY()
	
local msg1 = "<FONT FACE='A9eelsh'>~w~"..portsControl[portNum].msg1 --big
local msg2

if portsControl[portNum].status then --portsControl start
	msg2 = "<FONT FACE='A9eelsh'>~g~"..portsControl[portNum].msg2 --small
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
else --portsControl finish
	msg2 = "<FONT FACE='A9eelsh'>~r~"..portsControl[portNum].msg3 --small
	PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end

local temps = 0

scaleform = InitializeScaleform("mp_big_message_freemode",msg1,msg2)

while temps<200 do
	Citizen.Wait(0)
	DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255, 0)
	temps = temps + 1
end
end)

-- draw scaleform multi use
function InitializeScaleform(scaleform,msg,msg2)
local scaleform = RequestScaleformMovie(scaleform)

while not HasScaleformMovieLoaded(scaleform) do
	Citizen.Wait(0)
end

PushScaleformMovieFunction(scaleform, "SHOW_SHARD_WASTED_MP_MESSAGE")
PushScaleformMovieFunctionParameterString(msg)
PushScaleformMovieFunctionParameterString(msg2)
PopScaleformMovieFunctionVoid()

return scaleform
end

function watermarkResetDrawTextY()
local DrawTextYbase = 0.743
local count = 1
local k = 0
local y
for k,v in pairs(portsControl) do
	if v.status then
		if count == 1 then
			v.DrawTextY = DrawTextYbase
			y = DrawTextYbase
		else
			y = y - 0.025
			v.DrawTextY = y
		end
		count = count + 1
	end
end	
for k,v in pairs(promotion) do
	if v.status then
		if count == 1 then
			v.DrawTextY = DrawTextYbase
			y = DrawTextYbase
		else
			y = y - 0.025
			v.DrawTextY = y
		end
		count = count + 1
	end
end
end

AddEventHandler('esx:onPlayerDeath', function(data)
Wait(1000)
promotion['SellingDrugs'].sec = 0
promotion['SellingDrugs'].min = 0
end)

RegisterNetEvent('napoly_drugs:hamada:maxItemsSelling')
AddEventHandler('napoly_drugs:hamada:maxItemsSelling', function()
promotion['SellingDrugs'].sec = 0
promotion['SellingDrugs'].min = 0
end)

function StarteTimerDrugs()
CreateThread(function()
	while true do
		Wait(1000)
		if promotion['SellingDrugs'].sec > 0 then
			promotion['SellingDrugs'].sec = promotion['SellingDrugs'].sec - 1
		elseif promotion['SellingDrugs'].min > 0 and promotion['SellingDrugs'].sec == 0 then
			promotion['SellingDrugs'].min = promotion['SellingDrugs'].min - 1
			promotion['SellingDrugs'].sec = 60
		elseif promotion['SellingDrugs'].min == 0 and promotion['SellingDrugs'].sec == 0 then
			TriggerServerEvent('napoly_drugs:hamada:stopSellingDrugs')
			break
		end
	end
end)
end

local function SafeEventHandler(cb)
return function(...)
		local status, err = pcall(cb, ...)
		if not status then
				-- يمكنك إزالة هذا السطر إذا كنت لا تريد رؤية الأخطاء في الكونسول
				-- print("^3Warning: Event handler error suppressed^7")
		end
end
end

RegisterNetEvent('esx_misc:watermark_promotion')
AddEventHandler('esx_misc:watermark_promotion', SafeEventHandler(function(promotionType, status, timer, min, sec)
	if not promotion then return end
	if not promotionType then return end
	if not promotion[promotionType] then return end
	
	promotion[promotionType].status = status
	if timer then
			promotion[promotionType].timer = timer
			promotion[promotionType].min = min or 0
			promotion[promotionType].sec = sec or 0
	end
	if watermarkResetDrawTextY then
			watermarkResetDrawTextY()
	end
end))

RegisterNetEvent('esx_misc:updatePromotionStatus')
AddEventHandler('esx_misc:updatePromotionStatus', function(job, data, data2, msg1)
	if job ~= 'jobs' then
			if promotion[job] then
					promotion[job].status = data
			end

			if job == 'SellingDrugs' and promotion[job] and promotion[job].status == true then
					promotion[job].min = 15
					promotion[job].sec = 0
					StarteTimerDrugs()
			end
			if job == 'WaitingDrugs' and promotion[job] and promotion[job].status == true then
					promotion[job].msg1 = msg1
			end
			if data2 and promotion[job] then
					promotion[job].data = data2
			end
	else
			for k,v in pairs(data) do
					if promotion[k] then  -- تحقق من وجود المفتاح قبل الوصول إليه
							promotion[k].status = v
							if data2 then
									promotion[k].data = data2
							end
					end
			end
	end
	watermarkResetDrawTextY()
end)





