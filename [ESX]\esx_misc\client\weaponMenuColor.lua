local GUI                     = {}
GUI.Time                      = 0

function OpenWeaponsSkinsMenu()
    local elements = {}
    ESX.UI.Menu.CloseAll()

    --        table.insert(elements, {label = "------------------------"})
    --        table.insert(elements, {label = "|         كفرات         |"})
    --        table.insert(elements, {label = "------------------------"})
        table.insert(elements, {label = "الأساسي", istint = true, value = 0})
        table.insert(elements, {label = "اخضر", istint = true, value = 1})
        table.insert(elements, {label = "ذهبي", istint = true, value = 2})
        table.insert(elements, {label = "وردي", istint = true, value = 3})
        table.insert(elements, {label = "جيشي", istint = true, value = 4})
        table.insert(elements, {label = "شرطة", istint = true, value = 5})
        table.insert(elements, {label = "برتقالي", istint = true, value = 6})
        table.insert(elements, {label = "بلاتيني", istint = true, value = 7})
    ESX.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'esx_extraitems_skins',
        {
            title    = 'قائمة كفرات سلاح مجانا',
            align    = 'top-left',
            elements = elements,

        },
function(data, menu)
                SetPedWeaponTintIndex(PlayerPedId(), GetSelectedPedWeapon(PlayerPedId()), data.current.value)
        end,
        function(data, menu)
            menu.close()
        end
    )
end

-- Key Controls
-- Citizen.CreateThread(function()
--     while true do
--         if not IsPedInAnyVehicle(PlayerPedId(), false) and GetSelectedPedWeapon(PlayerPedId()) ~= GetHashKey("WEAPON_UNARMED") then
--             if not IsPlayerFreeAiming(PlayerId()) then
--                 if IsControlPressed(0, 303) and GetLastInputMethod(2) and not ESX.UI.Menu.IsOpen('default', GetCurrentResourceName(), 'esx_extraitems_skins') and (GetGameTimer() - GUI.Time) > 300 then
--                     OpenWeaponsSkinsMenu()
--                 end
--             end
--         else
--             Citizen.Wait(3000)
--         end
--         Citizen.Wait(1)
--     end
-- end)

RegisterKeyMapping('weaponmenu', 'Weapon Menu', 'keyboard', 'U')
RegisterCommand('weaponmenu', function()
	if not IsPauseMenuActive() then
		if not IsPedInAnyVehicle(PlayerPedId(), false) and GetSelectedPedWeapon(PlayerPedId()) ~= GetHashKey("WEAPON_UNARMED") then
            if not IsPlayerFreeAiming(PlayerId()) then
                if not ESX.UI.Menu.IsOpen('default', GetCurrentResourceName(), 'esx_extraitems_skins') and (GetGameTimer() - GUI.Time) > 300 then
                    OpenWeaponsSkinsMenu()
                end
            end
        end
	end
end)