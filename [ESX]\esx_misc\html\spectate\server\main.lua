RegisterCommand("spec", function(source, args, user)
    local source_ = source
    local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer ~= nil and xPlayer.job.name == 'admin' then
    TriggerClientEvent('esx_spectate:spectate', source_, target)
    else
    TriggerClientEvent('chatMessage', source, "خلل : ", {255, 0, 0}, "ليست لديك صلاحيات")
    end
end)

ESX.RegisterServerCallback('esx_spectate:getPlayerData', function(source, cb, id)
	local xPlayer = ESX.GetPlayerFromId(id)
	if xPlayer ~= nil then
	   cb(xPlayer)
	end	
end)

RegisterServerEvent('esx_spectate:kick')
AddEventHandler('esx_spectate:kick', function(target, msg)
	DropPlayer(target, msg)
end)

ESX.RegisterServerCallback('esx_spectate:getOtherPlayerData', function(source, cb, target)

    local xPlayer = ESX.GetPlayerFromId(target)
    if xPlayer ~= nil then
       local identifier = GetPlayerIdentifiers(target)[1]

       local result = MySQL.Sync.fetchAll("SELECT * FROM users WHERE identifier = @identifier", {
            ['@identifier'] = identifier
            })

       local user      = result[1]
       local firstname     = xPlayer.getName()
	   local money			= xPlayer.getMoney()
           local bank			= xPlayer.getAccount('bank').money

       local data = {
         name        = GetPlayerName(target),
         job         = xPlayer.job,
         inventory   = xPlayer.inventory,
         accounts    = xPlayer.accounts,
         weapons     = xPlayer.loadout,
         firstname   = firstname,
	     money 	  = money,
	     bank 		  = bank
       }

         TriggerEvent('esx_license:getLicenses', target, function(licenses)
           data.licenses = licenses
           cb(data)
	   end)
    end
end)