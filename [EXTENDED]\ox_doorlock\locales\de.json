{"unlocked_door": "<PERSON><PERSON><PERSON> aufgeschloss<PERSON>", "locked_door": "<PERSON><PERSON><PERSON>", "lock_door": "[E] <PERSON>ü<PERSON> ve<PERSON>n", "unlock_door": "[E] <PERSON><PERSON><PERSON> au<PERSON>", "door_lock": "<PERSON><PERSON><PERSON><PERSON>", "passcode": "Zugangscode", "lockpick_broke": "<PERSON><PERSON> ist zerbrochen", "pick_lock": "<PERSON><PERSON><PERSON> au<PERSON>", "add_lock": "Türschloss hinzufügen", "edit_lock": "Türschloss bearbeiten", "remove_lock": "Türschloss entfernen", "cannot_unlock": "<PERSON>s ist nicht möglich die Tür aufzuschließen", "cannot_lock": "<PERSON>s ist nicht möglich die Tür zu versch<PERSON>ßen", "create_modify_lock": "<PERSON><PERSON><PERSON> ein Türschloss oder bearbeite ein vorhandenes", "add_door_textui": "Neue Tür erstellen \nMit [LMB] interagieren \nMit [RMB] abbrechen", "command_closest": "Öffne die UI direkt zur nächstgelegenen Tür"}