ESX = nil
local CallbacksRegistered = false

-- Initialize Opod globally first
Opod = {
    data = {
        PaintingCache   = {},
        BankVault       = {},
        SystemsHacked   = false,
        RobberyOngoing  = false,
        WindowCut       = false,
        GalleryClosed   = false,
        EggDisplayCut   = 'fixed',
        Robbers         = {},
        DoorStatus      = false,
        WinchSpawned    = false,
        LastRobbed      = 0
    }
}

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports[Config.SharedObject]:getSharedObject()
        Citizen.Wait(100)
    end

    -- Update configuration after ESX is loaded
    if Config then
        Opod.data.PaintingCache = Config.PaintingLocations or {}
        Opod.data.BankVault = Config.BankVault or {}
    end

    -- Register callbacks only after ESX is initialized
    if not CallbacksRegistered then
        RegisterCallbacks()
        CallbacksRegistered = true
    end
end)

function RegisterCallbacks()
    ESX.RegisterServerCallback('opod-artHeist:CheckEntityStatus', function(source, cb)
        cb(Opod.data)
    end)

    ESX.RegisterServerCallback('opod-artHeist:CanStartRobbery', function(source, cb)
        local xPlayers = ESX.GetPlayers()
        local copsOnline = 0
        for _, v in ipairs(xPlayers) do
            local xPlayer = ESX.GetPlayerFromId(v)
            if xPlayer.job.name == 'police' then
                copsOnline = copsOnline + 1
            end
        end
        if copsOnline < Config.RequiredPolice then
            cb('not_enough')
            return
        end
        cb('cut')
    end)

    ESX.RegisterServerCallback('opod-artHeist:HasItem', function(source, cb, item, amount)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer.getInventoryItem(item).count > 0 then
            if amount then
                xPlayer.removeInventoryItem(item, amount)
            end
            cb(true)
        else
            cb(false)
        end
    end)

    ESX.RegisterServerCallback('esx_misc:getStatusStolen', function(source, cb)
        local status = {
            canRob = true,
            lastRobbed = Opod.data.LastRobbed or 0,
            cooldown = Config.Cooldown or 500
        }
        
        if (os.time() - status.lastRobbed) < status.cooldown then
            status.canRob = false
        end
        
        cb(status)
    end)
end

-- Initialize bank vault and paintings
Citizen.CreateThread(function()
    while not Opod or not Opod.data or not Opod.data.BankVault do 
        Wait(100) 
    end
    
    if Opod.data.BankVault then
        Opod.data.BankVault.isLocked = true
        Opod.data.BankVault.doorRotating = false
        Opod.data.BankVault.curHeading = Opod.data.BankVault.defaultHeading or 0
    end
    
    if Opod.data.PaintingCache then
        for _, v in ipairs(Opod.data.PaintingCache) do
            v.isStolen = false
        end
    end
end)

RegisterNetEvent("esx_wesam_or_68store:sendtochat")
AddEventHandler("esx_wesam_or_68store:sendtochat", function()
    TriggerClientEvent('chatMessage', -1, "^3عاجل^0: تتم الان سرقة ^1معرض الفنون")
end)