RNE = function(e, h) RegisterNetEvent(e) AddEventHandler(e, h) end

CT = function(h) Citizen.CreateThread(h) end

TE = function(e, h, h2, h3, h4, h5, h6, h7, h8, h9) TriggerEvent(e, h, h2, h3, h4, h5, h6, h7, h8, h9) end

TCE = function(e, s, h, h2, h3, h4, h5, h6, h7, h8, h9) TriggerClientEvent(e, s, h, h2, h3, h4, h5, h6, h7, h8, h9) end

TSE = function(e, h, h2, h3, h4, h5, h6, h7, h8, h9) TriggerServerEvent(e, h, h2, h3, h4, h5, h6, h7, h8, h9) end

AEH = function(e, h) AddEventHandler(e, h) end

Opod = {}