local totalM = 0

Doors = {
    ["F1"] = {{loc = vector3(312.93, -284.45, 54.16), h = 160.91, txtloc = vector3(312.93, -284.45, 54.16), obj = nil, locked = true}, {loc = vector3(310.93, -284.44, 54.16), txtloc = vector3(310.93, -284.44, 54.16), state = nil, locked = true}},
    ["F2"] = {{loc = vector3(148.76, -1045.89, 29.37), h = 158.54, txtloc = vector3(148.76, -1045.89, 29.37), obj = nil, locked = true}, {loc = vector3(146.61, -1046.02, 29.37), txtloc = vector3(146.61, -1046.02, 29.37), state = nil, locked = true}},
    ["F3"] = {{loc = vector3(-1209.66, -335.15, 37.78), h = 213.67, txtloc = vector3(-1209.66, -335.15, 37.78), obj = nil, locked = true}, {loc = vector3(-1211.07, -336.68, 37.78), txtloc = vector3(-1211.07, -336.68, 37.78), state = nil, locked = true}},
    ["F4"] = {{loc = vector3(-2957.26, 483.53, 15.70), h = 267.73, txtloc = vector3(-2957.26, 483.53, 15.70), obj = nil, locked = true}, {loc = vector3(-2956.68, 481.34, 15.70), txtloc = vector3(-2956.68, 481.34, 15.7), state = nil, locked = true}},
    ["F5"] = {{loc = vector3(-351.97, -55.18, 49.04), h = 159.79, txtloc = vector3(-351.97, -55.18, 49.04), obj = nil, locked = true}, {loc = vector3(-354.15, -55.11, 49.04), txtloc = vector3(-354.15, -55.11, 49.04), state = nil, locked = true}},
    ["F6"] = {{loc = vector3(1174.24, 2712.47, 38.09), h = 160.91, txtloc = vector3(1174.24, 2712.47, 38.09), obj = nil, locked = true}, {loc = vector3(1176.40, 2712.75, 38.09), txtloc = vector3(1176.40, 2712.75, 38.09), state = nil, locked = true}},
}

function Robbery (name,title,message,color)
    local DiscordWebHook = "webhooks"

    local embeds = {
        {
            ["title"]=title,
			["description"] = message,
            ["type"]="rich",
            ["color"] =color,
            ["footer"]=  { ["text"]= "السرقات", },
        }
    }

    if message == nil or message == '' then return FALSE end
    PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterServerEvent('utk_fleeca:msg')
AddEventHandler('utk_fleeca:msg', function(name,title,message,color)
	Robbery(name,title,message,color)
end)

function DiscordLog (name, title, message, color, DiscordWebHook)

	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "سجل سرقة البنوك الصغيرة",
            ["icon_url"] = ""},
		}
	}

	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
		elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end


RegisterServerEvent("utK3E38H34k_fh:startcheck") -- utk_fh:startcheck
AddEventHandler("utK3E38H34k_fh:startcheck", function(bank, l)
    local _source = source
    local copcount = 0
    local Players = ESX.GetPlayers()
    if l == "a;olj@dh2%98%20h92" then
    for i = 1, #Players, 1 do
        local xPlayer = ESX.GetPlayerFromId(Players[i])

        if xPlayer.job.name == "police" or xPlayer.job.name == "agent" then
            copcount = copcount + 1
        end
    end
    local xPlayer = ESX.GetPlayerFromId(_source)
    local item = xPlayer.getInventoryItem("id_card_f")["count"]

    if copcount >= UTK.mincops then
        if item >= 1 then
            if not UTK.Banks[bank].onaction == true then
                if (os.time() - UTK.cooldown) > UTK.Banks[bank].lastrobbed then
				    totalM = 0

                    UTK.Banks[bank].onaction = true
                    xPlayer.removeInventoryItem("id_card_f", 1)
                    TriggerClientEvent("utk_fh:outcome", _source, true, bank)
                    TriggerClientEvent("utk_fh:policenotify", -1, bank)

					 bankInfo = ""
					 if bank == 'F1' then
					 bankInfo = "لوس مربع 7021"
					 elseif bank == 'F2' then
					 bankInfo = "لوس الحديقة العامة"
					 elseif bank == 'F3' then
					 bankInfo = "لوس مربع 5146"
					 elseif bank == 'F4' then
					 bankInfo = "الساحل مربع 7066"
					 elseif bank == 'F5' then
					 bankInfo = "لوس مربع 7032"
					 elseif bank == 'F6' then
					 bankInfo = "ساندي مربع 707"
					 end

					 TriggerClientEvent('chatMessage', -1, "^1عاجل^2: الأن تتم عملية سطو مسلح على بنك "..bankInfo.." ^3")

					 local ids = ExtractIdentifiers(source)
						_discordID ="**Discord ID:  ** <@" ..ids.discord:gsub("discord:", "")..">"
						_identifierID ="**identifier :  ** " ..xPlayer.identifier..""
						DiscordLog ('سرقة البنك الصغير', 'بدأ سرقة بنك '..bankInfo, 'من قبل: '..xPlayer.getName()..'\n'.._discordID..'\n'.._identifierID..'', '********', 'webhooks')
                else
                    TriggerClientEvent("utk_fh:outcome", _source, false, "لازم تنتظر الى 30 دقيقة من اخر سرقة  "..math.floor((UTK.cooldown - (os.time() - UTK.Banks[bank].lastrobbed)) / 60)..":"..math.fmod((UTK.cooldown - (os.time() - UTK.Banks[bank].lastrobbed)), 60))
                end
            else
                TriggerClientEvent("utk_fh:outcome", _source, false, "!جاري سرقة البنك الصغير")
            end
        else
            TriggerClientEvent("utk_fh:outcome", _source, false, "لا تملك شريحة البنك")
        end
    else
        TriggerClientEvent("utk_fh:outcome", _source, false, 'لايوجد عدد شرطة كافي يجب وجود '..UTK.mincops)
    end
    end
end)

RegisterServerEvent("utk_fh:lootup")
AddEventHandler("utk_fh:lootup", function(var, var2)
    TriggerClientEvent("utk_fh:lootup_c", -1, var, var2)
end)

RegisterServerEvent("utk_fh:openDoor")
AddEventHandler("utk_fh:openDoor", function(coords, method)
    TriggerClientEvent("utk_fh:openDoor_c", -1, coords, method)
end)

RegisterServerEvent("utk_fh:toggleDoor")
AddEventHandler("utk_fh:toggleDoor", function(key, state)
    Doors[key][1].locked = state
    TriggerClientEvent("utk_fh:toggleDoor", -1, key, state)
end)

RegisterServerEvent("utk_fh:toggleVault")
AddEventHandler("utk_fh:toggleVault", function(key, state)
    Doors[key][2].locked = state
    TriggerClientEvent("utk_fh:toggleVault", -1, key, state)
end)

RegisterServerEvent("utk_fh:updateVaultState")
AddEventHandler("utk_fh:updateVaultState", function(key, state)
    Doors[key][2].state = state
end)

RegisterServerEvent("utKD32Dk_fh:startLoot")
AddEventHandler("utKD32Dk_fh:startLoot", function(data, name, players)
    local _source = source

    for i = 1, #players, 1 do
        TriggerClientEvent("utk_fh:startLoot_c", players[i], data, name)
    end
    TriggerClientEvent("utk_fh:startLoot_c", _source, data, name)
end)

RegisterServerEvent("utk_fh:stopHeist")
AddEventHandler("utk_fh:stopHeist", function(name)
    TriggerClientEvent("utk_fh:stopHeist_c", -1, name)
end)

RegisterServerEvent("utkGGjG23K_fh:rewardCash") -- utk_fh:rewardCash
AddEventHandler("utkGGjG23K_fh:rewardCash", function(z, token)
    local xPlayer = ESX.GetPlayerFromId(source)
    local reward = math.random(UTK.mincash, UTK.maxcash)
	local number = math.random(1,185)
	local GiveXP = false
	local src = source
	XtraD = ''
	XtraD2 = ''
	if z == "kal;jdh289b^hrtrtryjjj" then

    if UTK.black then

       	if number == 1 then
	    xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 2 then
	    xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 3 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 4 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 5 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 6 then
		xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
	    elseif number == 7 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 8 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 9 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 10 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 11 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 12 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 13 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 14 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 15 then
		xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 17 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 18 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 19 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 20 then
		xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 21 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 22 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 23 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 24 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 25 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 26 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 27 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 28 then
	    xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 29 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 30 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 31 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 32 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 33 then
	    xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 34 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 35 then
		xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 36 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 37 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 38 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 39 then
		xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 40 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 41 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 42 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 43 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 44 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 45 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
	    elseif number == 46 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 47 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 48 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 49 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 50 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 51 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 52 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 53 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 54 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 55 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
	    elseif number == 56 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 57 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 58 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 59 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 60 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 61 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 62 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 63 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 64 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 65 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 66 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 67 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 68 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 69 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 70 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 71 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 72 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 73 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 74 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
	    elseif number == 75 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 76 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 77 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 78 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 79 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 80 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 81 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 82 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 83 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 84 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 85 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 86 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 87 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 88 then
		xPlayer.addAccountMoney('black_money', reward)
		GiveXP = true
		elseif number == 89 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 90 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 91 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 92 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 93 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 94 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 95 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 96 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 97 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 98 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
	    elseif number == 99 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 100 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 101 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 102 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 103 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 104 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 105 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 106 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 107 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 108 then
		xPlayer.addAccountMoney('black_money', reward)
		GiveXP = true
	    elseif number == 109 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 110 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 111 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 112 then
		xPlayer.addAccountMoney('black_money', reward)
		GiveXP = true
	    elseif number == 113 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 114 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 115 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 116 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 117 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 118 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 119 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 120 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 121 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 122 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 123 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 124 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 125 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 126 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 127 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 128 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 129 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 130 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 131 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
	    elseif number == 132 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 133 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 134 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 135 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 136 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 137 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 138 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 139 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 140 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 141 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 142 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 143 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 144 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 145 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 146 then
		xPlayer.addAccountMoney('black_money', reward)
		GiveXP = true
	    elseif number == 147 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 148 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 149 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 150 then
		xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 151 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 152 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 153 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 154 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 155 then
		xPlayer.addAccountMoney('black_money', reward)
		GiveXP = true
	    elseif number == 156 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 157 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 158 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 159 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 160 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 161 then
	    xPlayer.addAccountMoney('black_money', reward)
GiveXP = true
		elseif number == 162 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 163 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 164 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 165 then
		xPlayer.addAccountMoney('black_money', reward)
	    elseif number == 166 then
	    xPlayer.addAccountMoney('black_money', reward)
		elseif number == 167 then
	    xPlayer.addAccountMoney('black_money', reward)
        GiveXP = true
		elseif number == 168 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 169 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 170 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 171 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 172 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 173 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number == 174 then
		xPlayer.addAccountMoney('black_money', reward)
		elseif number > 174 and number < 186 then
		xPlayer.addAccountMoney('black_money', reward)
        xPlayer.addInventoryItem("laptop_h", 1)
		TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', source, 'add', 300, 'سرقة بنك الساحل (محظرظ حصلت على جهاز تهكير البنك المركزي)')
		    TriggerClientEvent('pNotify:SendNotification', xPlayer.source, {
                text = '<center><b style="color:#0BAF5F;font-size:26px;"> أنت محظوظ على قد تحصلت على جهاز تهكير البنك المركزي ',
                type = "info",
                timeout = 10000,
                layout = "centerLeft"
            })
			XtraD2 = 'محظرظ حصل على جهاز تهكير البنك المركزي و 300 خبرة إضافية'
        else
        xPlayer.addAccountMoney('black_money', reward)
	    end
		totalM = totalM + reward
		if GiveXP then
		TriggerEvent('napoly_xplevel:updateCurrentPlayerXP', source, 'add', 20, 'سرقة بنك الساحل')
		XtraD = '\nخبرة: 20'
		end

		 local ids = ExtractIdentifiers(source)
						_discordID ="**Discord ID:  ** <@" ..ids.discord:gsub("discord:", "")..">"
						_identifierID ="**identifier :  ** " ..xPlayer.identifier..""
						DiscordLog ('سرقة البنك الصغير', 'جمع جوائز البنك الصغير', 'من قبل: '..xPlayer.getName()..'\n'.._discordID..'\n'.._identifierID..'\nمبلغ: $'..reward..' أموال حمراء\nمجموع الأموال المجموعة بهاذه السرقة: $'..totalM..''..XtraD..''..XtraD2, '********', 'webhooks')

	end
	end
end)

RegisterServerEvent("utk_fh:setCooldown")
AddEventHandler("utk_fh:setCooldown", function(name)
    UTK.Banks[name].lastrobbed = os.time()
    UTK.Banks[name].onaction = false
    TriggerClientEvent("utk_fh:resetDoorState", -1, name)
end)

ESX.RegisterServerCallback("utk_fh:getBanks", function(source, cb)
    cb(UTK.Banks, Doors)
end)

ESX.RegisterServerCallback("utk_fh:checkSecond", function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    local item = xPlayer.getInventoryItem("id_card_f")["count"]

	-------------
	local data = exports.esx_misc:isNoCrimetime() --return table with 3 values {active(boolen),location,label}
	local data2 = exports.esx_misc:isNoCrimetime2() --return table with 3 values {active(boolen),name,label}
	if data.active and (data.location == 'peace_time' or data.location == 'restart_time' or data.location == 'NoCrimetime' or data.location == 'NewScenario' or data.location == 'SmallBanks') then
	 cb("لايمكن السرقة. يوجد "..data.label)
	elseif data2.active and (data2.location == 'NoCrimetime' or data2.location == 'NewScenario' or data2.location == 'SmallBanks') then
	 cb("لايمكن السرقة. يوجد "..data2.label)
	else

    if item >= 1 then
        xPlayer.removeInventoryItem("id_card_f", 1)
        cb(true)
    else
        cb(false)
    end
    end
end)
