<!--   
Code by <PERSON> aka. plunk<PERSON><PERSON><PERSON>, everything related to alarm is happeing because of his code.
Original code: https://github.com/plunkett<PERSON><PERSON>/interact-sound
His Github: https://github.com/plunkett<PERSON>tt
Credits and thanks to him.
-->
<html>
    <head>
        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.1.1/howler.min.js" type="text/javascript"></script>
        <script>
            var AudioPlayer = null;
            AudioPlayer = new Howl({src: ["./alarm.ogg"]});
            window.addEventListener('message', function(event) {
                if (event.data.transactionType == "playSound") {
                  AudioPlayer.volume(0.03);
                  AudioPlayer.play();
                }
                if (event.data.transactionType == "stopSound") {
                  AudioPlayer.stop();
                }
            });
        </script>
    </head> 
</html>
