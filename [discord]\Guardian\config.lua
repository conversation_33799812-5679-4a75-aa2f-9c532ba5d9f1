-----------------------------------------------------------
-- Guardian- A Simple FiveM Script, Made By Jordan.#2139 --
-----------------------------------------------------------

-------------------------------------------------------
--           CONFIG YOUR PERIPHERALS HERE!           --
-------------------------------------------------------
Config = {
    DiscordYeet = 'يرجى فتح الديسكورد', -- Message to client if their discord cannot be found by the script
    WhitelistYeet = 'ادخل الديسكورد ووافق على الشروط ويمكنك الدخول للسيرفر مباشرة بدون مقابلة', -- Message to client if they are not whitelisted
    --WhitelistYeet = 'السيرفر حاليا بحالة أوفلاين يمكنك متابعة حالة السيرفر بالديسكورد', -- Message to client if they are not whitelisted
    RoleIdsYeet = 'اربط الفايف ام بالديسكورد',
    DiscordURL = 'https://discord.gg/XVrajbtZdT',
    EnableWhitelist = false, -- تفعيل التفعيل (:
    CheckingRoleMsg = '⏳ جاري التحقق من التأشيرة',
    WhitelistRoles = { -- Roles that you assigned in Badgers API that should pass the whitelist, ALSO Make sure that **each** role ends with a comma!
        --الإدارة
        "1300888182610460732", -- أدمن بلس
        "1300888186871873576", -- أدمن
        "1300888188159524966", -- مشرف بلس
        "1300888189116088352", -- مشرف
        "1300888191741464647", -- الدعم الفني
        --رول دخول
        --"1299023663093387385", -- تجربة السيرفر
        --رول التفعيل
        "1300888443303362642", -- موافق على الشروط
    },
    }
    --------------------------
    -- ^^^ DO THAT HERE ^^^ -- 
    --------------------------