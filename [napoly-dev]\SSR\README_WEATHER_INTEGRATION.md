# تكامل نظام الطقس مع cd_easytime

## نظرة عامة
تم تطوير نظام الطقس التدريجي في SSR ليعمل بتنسيق كامل مع سكربت `cd_easytime` لضمان عدم حدوث تضارب في إعدادات الطقس.

## كيفية عمل التكامل

### 1. إيقاف المزامنة المؤقت
```lua
-- عند بدء العد التنازلي
TriggerEvent('cd_easytime:PauseSync', true, 30)
```

### 2. تطبيق الطقس التدريجي
```lua
-- استخدام نفس وظائف cd_easytime
function ChangeWeatherDirect(weather, instant, changespeed)
    CheckSnowSync(weather)
    if instant then
        ClearOverrideWeather()
        ClearWeatherTypePersist()
        SetWeatherTypePersist(weather)
        SetWeatherTypeNow(weather)
        SetWeatherTypeNowPersist(weather)
    else
        ClearOverrideWeather()
        SetWeatherTypeOvertimePersist(weather, changespeed or 180.0)
    end
end
```

### 3. إعادة تفعيل المزامنة
```lua
-- عند انتهاء العد التنازلي
TriggerEvent('cd_easytime:PauseSync', false)
TriggerServerEvent('cd_easytime:SyncMe')
```

## التسلسل الزمني للطقس

### الدقيقة 5-4: طقس هادئ
- **الطقس**: `CLEAR`
- **المطر**: 0.0
- **الغيوم**: 0.1
- **الرياح**: 3.0

### الدقيقة 4: رياح قوية
- **الطقس**: `CLOUDS`
- **المطر**: 0.0
- **الغيوم**: 0.3 (قليلة)
- **الرياح**: 20.0 (قوية)

### الدقيقة 3: طقس الهالوين مع رياح قوية
- **الطقس**: `HALLOWEEN`
- **المطر**: 0.0
- **الغيوم**: 0.8 (كثيفة)
- **الرياح**: 22.0 (قوية جداً)

### الدقيقة 2: إضافة مطر تدريجي
- **الطقس**: `HALLOWEEN` (بدون تغيير)
- **المطر**: تدريجي من 0.0 إلى 0.9
- **الغيوم**: 0.8 (نفس السابق)
- **الرياح**: 22.0 (نفس السابق)
- **ملاحظة**: فقط المطر يتم إضافته تدريجياً

### الدقيقة 1: عاصفة رعدية كاملة
- **الطقس**: `THUNDER`
- **المطر**: 1.0
- **الغيوم**: 1.0
- **الرياح**: 25.0
- **تأثيرات إضافية**: رعد وبرق وآثار المطر

## الإعدادات

```lua
Config.CDEasyTime = {
    Enabled = true,                    -- تفعيل التنسيق
    PauseOnCountdown = true,          -- إيقاف المزامنة أثناء العد التنازلي
    RestoreAfterCountdown = true,     -- إعادة تفعيل المزامنة بعد الانتهاء
    SyncDelay = 1000,                 -- تأخير إعادة المزامنة (ميلي ثانية)
}
```

## معالجة الأحداث

### منع التداخل
```lua
RegisterNetEvent('cd_easytime:SyncWeather')
AddEventHandler('cd_easytime:SyncWeather', function(data)
    if isRestartInProgress then
        return -- تجاهل تحديثات cd_easytime أثناء العد التنازلي
    end
end)
```

### تنظيف التأثيرات
```lua
function CleanupAllEffects()
    -- تنظيف جميع التأثيرات
    -- إعادة تفعيل cd_easytime
    if Config.CDEasyTime.Enabled then
        TriggerEvent('cd_easytime:PauseSync', false)
        TriggerServerEvent('cd_easytime:SyncMe')
    end
end
```

## المتطلبات
- سكربت `cd_easytime` يجب أن يكون مثبت ومفعل
- إعدادات `Config.CDEasyTime.Enabled = true`
- صلاحيات تشغيل أحداث cd_easytime

## استكشاف الأخطاء
1. تأكد من تشغيل cd_easytime قبل SSR
2. تحقق من رسائل التتبع في وحدة التحكم
3. تأكد من عدم وجود سكربت طقس آخر يتداخل
4. تحقق من إعدادات cd_easytime في config.lua
