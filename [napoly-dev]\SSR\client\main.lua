local ESX = exports["es_extended"]:getSharedObject()
local isRestartInProgress = false
local countdownTime = 0
local lastTick = 0
local isInFinalMinute = false

local currentWeather = 'CLEAR'
local isWeatherTransitioning = false
local isThunderActive = false
local lastThunderTime = 0

-- مزامنة الطقس مع cd_easytime
RegisterNetEvent('cd_easytime:UpdateWeather')
AddEventHandler('cd_easytime:UpdateWeather', function(weather)
    if currentWeather ~= weather then
        currentWeather = weather

    end
end)

-- مستمع لأحداث تحذير التسونامي من cd_easytime
RegisterNetEvent('cd_easytime:StartTsunamiCountdown')
AddEventHandler('cd_easytime:StartTsunamiCountdown', function(tsunamiStarting)
    if tsunamiStarting then
        -- تشغيل العد التنازلي للتسونامي
        TriggerEvent('ssr:startCountdown', Config.DefaultRestartTime, true)
        -- تشغيل صفارة الإنذار
        TriggerServerEvent('InteractSound_SV:PlayWithinDistance', Config.TsunamiSiren.MaxDistance, Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
    end
end)

-- وظيفة محسنة للتحقق مما إذا كان اللاعب في الخارج
-- متغيرات للذاكرة المؤقتة
local lastLocationCheck = 0
local lastLocationResult = false
local locationCheckInterval = 1000 -- فحص كل ثانية
local currentTime = 0
local currentHour = 0
local currentMinute = 0

-- مزامنة الوقت مع cd_easytime
RegisterNetEvent('cd_easytime:UpdateTime')
AddEventHandler('cd_easytime:UpdateTime', function(hour, minute)
    currentHour = hour
    currentMinute = minute
    NetworkOverrideClockTime(hour, minute, 0)
    
    -- التحقق من الوقت للتأثيرات الخاصة
    local isDawn = (hour >= 5 and hour < 7)
    local isDusk = (hour >= 18 and hour < 20)
    
    if isDawn or isDusk then
        -- تطبيق تأثيرات خاصة للفجر والغسق
        local timecycleMod = isDawn and 'dawn' or 'dusk'
        SetTimecycleModifier(timecycleMod)
        SetTimecycleModifierStrength(0.3)
    else
        ClearTimecycleModifier()
    end
end)

-- مزامنة حالة الطقس
RegisterNetEvent('cd_easytime:SyncWeather')
AddEventHandler('cd_easytime:SyncWeather', function(data)
    if data.weather and data.weather ~= currentWeather then
        if not isWeatherTransitioning then
            isWeatherTransitioning = true
            
            -- تطبيق التحول التدريجي في الطقس
            SetWeatherTypeOvertimePersist(data.weather, 15.0)
            
            Citizen.Wait(15000)
            currentWeather = data.weather
            isWeatherTransitioning = false
            
            -- تحديث تأثيرات إضافية بناءً على الطقس
            if data.weather == 'RAIN' or data.weather == 'THUNDER' then
                SetRainLevel(Config.Weather.RainLevel)
            else
                SetRainLevel(0.0)
            end
        end
    end
end)

local function IsPlayerOutside()
    local currentTime = GetGameTimer()
    
    -- استخدام النتيجة المخزنة مؤقتًا إذا لم يمر وقت كافٍ
    if currentTime - lastLocationCheck < locationCheckInterval then
        return lastLocationResult
    end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    local ray = StartExpensiveSynchronousShapeTestLosProbe(
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.StartOffset,
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.RayHeight,
        Config.LocationCheck.IgnorePlayer,
        Config.LocationCheck.IgnoreVehicles,
        Config.LocationCheck.RayType
    )
    
    local retval, hit, endCoords = GetShapeTestResult(ray)
    lastLocationResult = (hit == 0)
    lastLocationCheck = currentTime
    
    if Config.Debug then
        print("فحص الخارج - hit:", hit, "النتيجة:", lastLocationResult)
    end
    
    return lastLocationResult
end



-- مراقبة العاصفة وربطها مع cd_easytime
Citizen.CreateThread(function()
    while true do
        if isRestartInProgress and isInFinalMinute then
            -- تطبيق تأثيرات الطقس مباشرة
            ClearOverrideWeather()
            ClearWeatherTypePersist()
            SetWeatherTypePersist('THUNDER')
            SetWeatherTypeNow('THUNDER')
            SetWeatherTypeNowPersist('THUNDER')
            
            -- تطبيق تأثيرات إضافية
            SetRainLevel(Config.Weather.RainLevel)
            SetCloudHatOpacity(Config.Weather.CloudOpacity)
        end
        Citizen.Wait(1000)
    end
end)

-- وظيفة بدء تأثيرات الدقيقة الأخيرة
function StartFinalMinuteEffects()
    if isInFinalMinute then return end
    
    isInFinalMinute = true
    --print("بدء تأثيرات الدقيقة الأخيرة")
    
    -- تشغيل صوت التسونامي مرتين
    local sirenCount = 0
    local function playLastMinuteSiren()
        if sirenCount < 2 then
            TriggerServerEvent('InteractSound_SV:PlayOnAll', Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
            sirenCount = sirenCount + 1
            
            -- إيقاف الصوت الحالي وتشغيل الصوت التالي بعد المدة المحددة
            Citizen.SetTimeout(Config.TsunamiSiren.Duration, function()
                TriggerServerEvent('InteractSound_SV:Stop')
                if sirenCount < 2 then
                    -- انتظر 500 مللي ثانية قبل تشغيل الصوت التالي
                    Citizen.SetTimeout(500, playLastMinuteSiren)
                end
            end)
        end
    end
    
    -- بدء تشغيل الصوت
    playLastMinuteSiren()
    
    -- تهيئة العاصفة وربطها مع cd_easytime
    TriggerEvent('cd_easytime:ForceUpdate', {
        weather = 'THUNDER',
        instantweather = true,
        blackout = false
    })
    SetRainLevel(Config.Weather.RainLevel)
    SetCloudHatOpacity(Config.Weather.CloudOpacity)
    

end

-- وظيفة تحديث الطقس التدريجي
function UpdateProgressiveWeather()
    -- تحقق من الوقت الحالي وتطبيق التغييرات المناسبة
    if countdownTime > Config.WeatherPhases.OVERCAST then
        -- 5-4 دقائق: طقس صافي مع غيوم خفيفة تدريجية
        if Config.Debug then
            print("SSR: تطبيق طقس صافي - الوقت المتبقي: " .. countdownTime)
        end
        SetWeatherTypeOverTime('CLEAR', 20.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.3)
        SetWindSpeed(5.0)

    elseif countdownTime > Config.WeatherPhases.HALLOWEEN then
        -- 4-3 دقائق: طقس غائم
        if Config.Debug then
            print("SSR: تطبيق طقس غائم - الوقت المتبقي: " .. countdownTime)
        end
        SetWeatherTypeOverTime('OVERCAST', 20.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.5)
        SetWindSpeed(7.0)

    elseif countdownTime > Config.WeatherPhases.CLOUDY_RAIN then
        -- الدقيقة 3: طقس الهالوين مع رياح قوية
        if Config.Debug then
            print("SSR: تطبيق طقس الهالوين - الوقت المتبقي: " .. countdownTime)
        end
        SetWeatherTypeOverTime('HALLOWEEN', 15.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.3)
        SetWindSpeed(15.0)
        SetWindDirection(math.random() * 360.0)

    elseif countdownTime > Config.WeatherPhases.THUNDER_STORM then
        -- الدقيقة 2: غيوم + مطر + رياح
        if Config.Debug then
            print("SSR: تطبيق مطر ورياح - الوقت المتبقي: " .. countdownTime)
        end
        SetWeatherTypeOverTime('RAIN', 15.0)
        SetRainLevel(0.6)
        SetCloudHatOpacity(0.8)
        SetWindSpeed(10.0)
        SetWindDirection(math.random() * 360.0)

    else
        -- الدقيقة 1: العاصفة الرعدية الكاملة
        if Config.Debug then
            print("SSR: تطبيق عاصفة رعدية - الوقت المتبقي: " .. countdownTime)
        end
        SetWeatherTypeOverTime('THUNDER', 10.0)
        SetRainLevel(1.0)
        SetCloudHatOpacity(1.0)
        SetWindSpeed(20.0)
        SetWindDirection(math.random() * 360.0)

        -- تفعيل الرعد والوميض في الدقيقة الأخيرة
        if not isThunderActive then
            isThunderActive = true
            StartThunderEffects()
            if Config.Debug then
                print("SSR: تم تفعيل تأثيرات الرعد")
            end
        end
    end
end

-- وظيفة تشغيل تأثيرات الرعد والوميض
function StartThunderEffects()
    if Config.Debug then
        print("SSR: بدء تشغيل تأثيرات الرعد")
    end

    Citizen.CreateThread(function()
        while isThunderActive and countdownTime > 0 do
            local currentTime = GetGameTimer()
            local randomInterval = math.random(Config.Thunder.MinInterval, Config.Thunder.MaxInterval)

            -- التحقق من الفترة الزمنية بين الرعود
            if currentTime - lastThunderTime >= randomInterval then
                if Config.Debug then
                    print("SSR: تشغيل رعد - الفترة: " .. randomInterval)
                end

                -- تطبيق الوميض
                SetTimecycleModifier(Config.LightingEffects.CycleName)
                SetTimecycleModifierStrength(Config.Thunder.FlashIntensity)

                -- تشغيل صوت الرعد
                TriggerServerEvent('InteractSound_SV:PlayWithinDistance', Config.Thunder.MaxDistance, Config.Thunder.SoundFile, Config.Thunder.Volume)

                -- انتظار مدة الوميض
                Citizen.Wait(Config.LightingEffects.IntenseDuration)

                -- إزالة الوميض
                ClearTimecycleModifier()

                lastThunderTime = currentTime
            end

            Citizen.Wait(1000)
        end

        if Config.Debug then
            print("SSR: توقف تأثيرات الرعد")
        end
    end)
end

-- تحديث حدث بدء العد التنازلي
RegisterNetEvent('ssr:startCountdown')
AddEventHandler('ssr:startCountdown', function(time, isAuto)
    if isRestartInProgress then
        CleanupAllEffects()
    end
    
    isRestartInProgress = true
    countdownTime = time
    lastTick = GetGameTimer()
    isInFinalMinute = false
    
    -- تهيئة الطقس الأولي بشكل تدريجي
    SetWeatherTypeOverTime('EXTRASUNNY', 10.0)
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    
    SendNUIMessage({
        type = 'showTimer',
        time = math.floor(countdownTime),
        isAuto = isAuto
    })
    
    -- بدء مراقبة الطقس
    Citizen.CreateThread(function()
        while isRestartInProgress and countdownTime > 0 do
            local currentTick = GetGameTimer()
            local deltaTime = currentTick - lastTick
            
            if deltaTime >= 100 then
                countdownTime = countdownTime - (deltaTime / 1000)
                lastTick = currentTick
                
                -- تحديث العداد
                SendNUIMessage({
                    type = 'updateTimer',
                    time = math.max(0, math.floor(countdownTime)),
                    isAuto = isAuto
                })
                
                -- تحديث الطقس التدريجي
                UpdateProgressiveWeather()
            end
            
            Citizen.Wait(0)
        end
        
        if countdownTime <= 0 then
            SendNUIMessage({
                type = 'hideTimer'
            })
            CleanupAllEffects()
        end
    end)
end)

-- وظيفة تنظيف التأثيرات
function CleanupAllEffects()
    -- إيقاف تأثيرات الرعد
    isThunderActive = false
    lastThunderTime = 0

    isInFinalMinute = false
    SetWeatherTypeOverTime('CLEAR', 1.0)
    ClearWeatherTypePersist()
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    SetWindSpeed(5.0)
    -- تأكد من إيقاف أي اهتزاز للكاميرا
    StopGameplayCamShaking(true)
    ClearTimecycleModifier()
end

-- إيقاف العد التنازلي
RegisterNetEvent('ssr:cancelRestart')
AddEventHandler('ssr:cancelRestart', function()
    isRestartInProgress = false
    countdownTime = 0
    CleanupAllEffects()
    
    SendNUIMessage({
        type = 'hideTimer'
    })
end)







-- إضافة في بداية الملف
math.randomseed(GetGameTimer())
































