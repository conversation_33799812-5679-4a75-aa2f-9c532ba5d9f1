local ESX = exports["es_extended"]:getSharedObject()
local isRestartInProgress = false
local countdownTime = 0
local lastTick = 0
local isInFinalMinute = false
local lastThunderTime = 0
local lastStumbleTime = 0
local currentWeather = 'CLEAR'
local isWeatherTransitioning = false

-- مزامنة الطقس مع cd_easytime
RegisterNetEvent('cd_easytime:UpdateWeather')
AddEventHandler('cd_easytime:UpdateWeather', function(weather)
    if currentWeather ~= weather then
        currentWeather = weather
        if weather == 'THUNDER' then
            -- تفعيل تأثيرات العاصفة الرعدية
            PlaySynchronizedThunder(true)
        end
    end
end)

-- مستمع لأحداث تحذير التسونامي من cd_easytime
RegisterNetEvent('cd_easytime:StartTsunamiCountdown')
AddEventHandler('cd_easytime:StartTsunamiCountdown', function(tsunamiStarting)
    if tsunamiStarting then
        -- تشغيل العد التنازلي للتسونامي
        TriggerEvent('ssr:startCountdown', Config.DefaultRestartTime, true)
        -- تشغيل صفارة الإنذار
        TriggerServerEvent('InteractSound_SV:PlayWithinDistance', Config.TsunamiSiren.MaxDistance, Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
    end
end)

-- وظيفة محسنة للتحقق مما إذا كان اللاعب في الخارج
-- متغيرات للذاكرة المؤقتة
local lastLocationCheck = 0
local lastLocationResult = false
local locationCheckInterval = 1000 -- فحص كل ثانية
local currentTime = 0
local currentHour = 0
local currentMinute = 0

-- مزامنة الوقت مع cd_easytime
RegisterNetEvent('cd_easytime:UpdateTime')
AddEventHandler('cd_easytime:UpdateTime', function(hour, minute)
    currentHour = hour
    currentMinute = minute
    NetworkOverrideClockTime(hour, minute, 0)
    
    -- التحقق من الوقت للتأثيرات الخاصة
    local isDawn = (hour >= 5 and hour < 7)
    local isDusk = (hour >= 18 and hour < 20)
    
    if isDawn or isDusk then
        -- تطبيق تأثيرات خاصة للفجر والغسق
        local timecycleMod = isDawn and 'dawn' or 'dusk'
        SetTimecycleModifier(timecycleMod)
        SetTimecycleModifierStrength(0.3)
    else
        ClearTimecycleModifier()
    end
end)

-- مزامنة حالة الطقس
RegisterNetEvent('cd_easytime:SyncWeather')
AddEventHandler('cd_easytime:SyncWeather', function(data)
    if data.weather and data.weather ~= currentWeather then
        if not isWeatherTransitioning then
            isWeatherTransitioning = true
            
            -- تطبيق التحول التدريجي في الطقس
            SetWeatherTypeOvertimePersist(data.weather, 15.0)
            
            Citizen.Wait(15000)
            currentWeather = data.weather
            isWeatherTransitioning = false
            
            -- تحديث تأثيرات إضافية بناءً على الطقس
            if data.weather == 'RAIN' or data.weather == 'THUNDER' then
                SetRainLevel(Config.Weather.RainLevel)
            else
                SetRainLevel(0.0)
            end
        end
    end
end)

local function IsPlayerOutside()
    local currentTime = GetGameTimer()
    
    -- استخدام النتيجة المخزنة مؤقتًا إذا لم يمر وقت كافٍ
    if currentTime - lastLocationCheck < locationCheckInterval then
        return lastLocationResult
    end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    local ray = StartExpensiveSynchronousShapeTestLosProbe(
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.StartOffset,
        playerCoords.x, playerCoords.y, playerCoords.z + Config.LocationCheck.RayHeight,
        Config.LocationCheck.IgnorePlayer,
        Config.LocationCheck.IgnoreVehicles,
        Config.LocationCheck.RayType
    )
    
    local retval, hit, endCoords = GetShapeTestResult(ray)
    lastLocationResult = (hit == 0)
    lastLocationCheck = currentTime
    
    if Config.Debug then
        print("فحص الخارج - hit:", hit, "النتيجة:", lastLocationResult)
    end
    
    return lastLocationResult
end

function PlaySynchronizedThunder(isIntense)
    -- التحقق من وجود اللاعب
    local playerPed = PlayerPedId()
    if not DoesEntityExist(playerPed) then return end

    -- التحقق من حالة الطقس الحالية
    if currentWeather ~= 'THUNDER' and not isIntense and not Config.Debug then return end

    -- تهيئة الإعدادات والمتغيرات
    local settings = isIntense and Config.Thunder.Intense or Config.Thunder.Normal
    local stumbleSettings = isIntense and Config.Stumble.Intense or Config.Stumble.Normal
    local currentTime = GetGameTimer()

    -- التحقق من التأخير مع تحسين الرسائل التشخيصية
    if currentTime - lastThunderTime < settings.MinDelay then
        if Config.Debug then
            print(string.format("تجاهل الرعد - الوقت المتبقي: %d مللي ثانية", settings.MinDelay - (currentTime - lastThunderTime)))
        end
        return
    end
    lastThunderTime = currentTime

    -- التحقق من موقع اللاعب مع تحسين الأداء
    local isOutside = IsPlayerOutside()
    if Config.Debug then
        print(string.format("حالة اللاعب - في الخارج: %s", isOutside and "نعم" or "لا"))
    end

    -- تطبيق تأثيرات الرعد مع تحسين التعامل مع الأخطاء (الصوت معطل)
    local success, thunderError = pcall(function()
        SetTimecycleModifier(Config.LightingEffects.CycleName)
        SetTimecycleModifierStrength(settings.FlashIntensity)
        -- تم إلغاء صوت الرعد المخصص
        -- TriggerServerEvent('InteractSound_SV:PlayWithinDistance', settings.MaxDistance, settings.SoundFile, settings.Volume)

        -- إضافة تأثيرات إضافية للعواصف الرعدية
        if isIntense then
            SetWeatherTypeTransition(GetHashKey(currentWeather), GetHashKey('THUNDER'), 0.5)
            SetWindSpeed(settings.WindSpeed or 8.0)
            SetWindDirection(math.random() * 360.0)
        end
    end)

    if not success and Config.Debug then
        print("خطأ في تطبيق تأثيرات الرعد:", thunderError)
    end
    
    -- تم إلغاء منطق التعثر واهتزاز الكاميرا
    -- if isOutside and not IsPedInAnyVehicle(playerPed, false) and IsPedOnFoot(playerPed) and
    --    currentTime - lastStumbleTime >= Config.Stumble.MinInterval then

    --     if Config.Debug then
    --         print("بدء تطبيق تأثيرات التعثر")
    --     end

    --     -- تطبيق التعثر مع تحسين التعامل مع الأخطاء والواقعية
    --     local success, stumbleError = pcall(function()
    --         -- حساب وقت التعثر بناءً على شدة العاصفة
    --         local baseRagdollTime = isIntense and
    --             math.random(stumbleSettings.RagdollTimeMin, stumbleSettings.RagdollTimeMax) or
    --             math.random(stumbleSettings.RagdollTimeMin, math.floor(stumbleSettings.RagdollTimeMax * 0.7))

    --         -- تطبيق التعثر مع مراعاة حالة اللاعب
    --         SetPedToRagdoll(playerPed, baseRagdollTime, baseRagdollTime, 0, true, true, false)

    --         -- حساب قوة الدفع بناءً على شدة العاصفة
    --         local forward = GetEntityForwardVector(playerPed)
    --         local pushForce = stumbleSettings.PushForce * (isIntense and 1.2 or 0.8)
    --         local upwardForce = isIntense and 5.0 or 3.0

    --         -- تطبيق القوة بشكل أكثر واقعية
    --         ApplyForceToEntity(
    --             playerPed,
    --             1,
    --             forward.x * pushForce,
    --             forward.y * pushForce,
    --             upwardForce * stumbleSettings.Intensity,
    --             0.0, 0.0, 0.0,
    --             1,
    --             false, true, true, true, true
    --         )

    --         -- تطبيق اهتزاز الكاميرا بشكل متناسب مع شدة العاصفة
    --         ShakeGameplayCam('MEDIUM_EXPLOSION_SHAKE', isIntense and stumbleSettings.ShakeIntensity or stumbleSettings.ShakeIntensity * 0.7)
    --         lastStumbleTime = currentTime

    --         if Config.Debug then
    --             print(string.format("تم تطبيق التعثر - القوة: %.2f، المدة: %d", pushForce, baseRagdollTime))
    --         end
    --     end)

    --     if not success and Config.Debug then
    --         print("خطأ في تطبيق التعثر:", stumbleError)
    --     end
    -- end
    
    -- تحسين تأثير الوميض
    local flashDuration = isIntense and Config.LightingEffects.IntenseDuration or Config.LightingEffects.NormalDuration
    local startTime = GetGameTimer()
    
    Citizen.CreateThread(function()
        while GetGameTimer() - startTime < flashDuration do
            Citizen.Wait(0)
        end
        ClearTimecycleModifier()
        if Config.Debug then
            print("اكتمل تأثير الوميض")
        end
    end)
end

-- مراقبة العاصفة وربطها مع cd_easytime
Citizen.CreateThread(function()
    while true do
        if isRestartInProgress and isInFinalMinute then
            -- تطبيق تأثيرات الطقس مباشرة
            ClearOverrideWeather()
            ClearWeatherTypePersist()
            SetWeatherTypePersist('THUNDER')
            SetWeatherTypeNow('THUNDER')
            SetWeatherTypeNowPersist('THUNDER')
            
            -- تطبيق تأثيرات إضافية
            SetRainLevel(Config.Weather.RainLevel)
            SetCloudHatOpacity(Config.Weather.CloudOpacity)
        end
        Citizen.Wait(1000)
    end
end)

-- وظيفة بدء تأثيرات الدقيقة الأخيرة
function StartFinalMinuteEffects()
    if isInFinalMinute then return end
    
    isInFinalMinute = true
    --print("بدء تأثيرات الدقيقة الأخيرة")
    
    -- تشغيل صوت التسونامي مرتين
    local sirenCount = 0
    local function playLastMinuteSiren()
        if sirenCount < 2 then
            TriggerServerEvent('InteractSound_SV:PlayOnAll', Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
            sirenCount = sirenCount + 1
            
            -- إيقاف الصوت الحالي وتشغيل الصوت التالي بعد المدة المحددة
            Citizen.SetTimeout(Config.TsunamiSiren.Duration, function()
                TriggerServerEvent('InteractSound_SV:Stop')
                if sirenCount < 2 then
                    -- انتظر 500 مللي ثانية قبل تشغيل الصوت التالي
                    Citizen.SetTimeout(500, playLastMinuteSiren)
                end
            end)
        end
    end
    
    -- بدء تشغيل الصوت
    playLastMinuteSiren()
    
    -- تهيئة العاصفة وربطها مع cd_easytime
    TriggerEvent('cd_easytime:ForceUpdate', {
        weather = 'THUNDER',
        instantweather = true,
        blackout = false
    })
    SetRainLevel(Config.Weather.RainLevel)
    SetCloudHatOpacity(Config.Weather.CloudOpacity)
    
    -- بدء تسلسل الرعد
    Citizen.CreateThread(function()
        local nextThunderTime = 0
        
        while isInFinalMinute and isRestartInProgress and countdownTime > 0 do
            local currentTime = GetGameTimer()
            
            -- تحديث مستوى المطر والغيوم
            SetRainLevel(Config.Weather.RainLevel)
            SetCloudHatOpacity(Config.Weather.CloudOpacity)
            
            -- تشغيل الرعد بشكل دوري
            if currentTime >= nextThunderTime then
                --print("تشغيل الرعد")
                local isIntenseThunder = countdownTime <= 30
                PlaySynchronizedThunder(isIntenseThunder)
                
                local settings = isIntenseThunder and Config.Thunder.Intense or Config.Thunder.Normal
                nextThunderTime = currentTime + math.random(settings.MinDelay, settings.MaxDelay)
            end
            
            Citizen.Wait(0)
        end
    end)
end

-- وظيفة تحديث الطقس التدريجي
function UpdateProgressiveWeather()
    -- تحقق من الوقت الحالي وتطبيق التغييرات المناسبة
    if countdownTime <= Config.WeatherPhases.CALM and countdownTime > Config.WeatherPhases.OVERCAST then
        -- 5-4 دقائق: طقس صافي مع غيوم خفيفة تدريجية
        SetWeatherTypeOverTime('CLEAR', 20.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.3)
        
    elseif countdownTime <= Config.WeatherPhases.OVERCAST and countdownTime > Config.WeatherPhases.CLOUDY then
        -- 4-3 دقائق: طقس غائم
        SetWeatherTypeOverTime('OVERCAST', 20.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.5)
        
    elseif countdownTime <= Config.WeatherPhases.CLOUDY and countdownTime > Config.WeatherPhases.LIGHT_RAIN then
        -- 3-2 دقائق: غيوم كثيفة
        SetWeatherTypeOverTime('CLOUDY', 20.0)
        SetRainLevel(0.0)
        SetCloudHatOpacity(0.7)
        
    elseif countdownTime <= Config.WeatherPhases.LIGHT_RAIN and countdownTime > Config.WeatherPhases.STORM then
        -- 2-1 دقيقة: مطر خفيف مع غيوم داكنة
        SetWeatherTypeOverTime('RAIN', 20.0)
        SetRainLevel(0.3)
        SetCloudHatOpacity(0.85)
    end
end

-- تحديث حدث بدء العد التنازلي
RegisterNetEvent('ssr:startCountdown')
AddEventHandler('ssr:startCountdown', function(time, isAuto)
    if isRestartInProgress then
        CleanupAllEffects()
    end
    
    isRestartInProgress = true
    countdownTime = time
    lastTick = GetGameTimer()
    isInFinalMinute = false
    
    -- تهيئة الطقس الأولي بشكل تدريجي
    SetWeatherTypeOverTime('EXTRASUNNY', 10.0)
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    
    SendNUIMessage({
        type = 'showTimer',
        time = math.floor(countdownTime),
        isAuto = isAuto
    })
    
    -- بدء مراقبة الطقس
    Citizen.CreateThread(function()
        while isRestartInProgress and countdownTime > 0 do
            local currentTick = GetGameTimer()
            local deltaTime = currentTick - lastTick
            
            if deltaTime >= 100 then
                countdownTime = countdownTime - (deltaTime / 1000)
                lastTick = currentTick
                
                -- تحديث العداد
                SendNUIMessage({
                    type = 'updateTimer',
                    time = math.max(0, math.floor(countdownTime)),
                    isAuto = isAuto
                })
                
                -- تحديث الطقس التدريجي
                if countdownTime <= Config.WeatherPhases.CALM then
                    UpdateProgressiveWeather()
                end
            end
            
            Citizen.Wait(0)
        end
        
        if countdownTime <= 0 then
            SendNUIMessage({
                type = 'hideTimer'
            })
            CleanupAllEffects()
        end
    end)
end)

-- وظيفة تنظيف التأثيرات
function CleanupAllEffects()
    isInFinalMinute = false
    SetWeatherTypeOverTime('CLEAR', 1.0)
    ClearWeatherTypePersist()
    SetRainLevel(0.0)
    SetCloudHatOpacity(0.0)
    -- تأكد من إيقاف أي اهتزاز للكاميرا
    StopGameplayCamShaking(true)
    ClearTimecycleModifier()
end

-- إيقاف العد التنازلي
RegisterNetEvent('ssr:cancelRestart')
AddEventHandler('ssr:cancelRestart', function()
    isRestartInProgress = false
    countdownTime = 0
    CleanupAllEffects()
    
    SendNUIMessage({
        type = 'hideTimer'
    })
end)

-- إضافة حدث للتحكم في الرعد
RegisterNetEvent('ssr:playThunder')
AddEventHandler('ssr:playThunder', function(isIntense)
    --print("تم استلام حدث الرعد") -- للتأكد من استلام الحدث
    PlaySynchronizedThunder(isIntense)
end)

-- إزالة أو تعديل الثريد القديم الذي كان يسبب التعثر العشوائي
Citizen.CreateThread(function()
    while true do
        if isRestartInProgress and isInFinalMinute then
            -- نحذف التعثر العشوائي ونتركه فقط للرعد
            -- سيتم التعامل مع التعثر في PlaySynchronizedThunder
        end
        Citizen.Wait(1000)
    end
end)

-- مراقبة العاصفة
Citizen.CreateThread(function()
    while true do
        if isRestartInProgress and isInFinalMinute then
            -- تم نقل منطق التعثر إلى PlaySynchronizedThunder
        end
        Citizen.Wait(1000)
    end
end)

-- إعادة تعيين مرحلة التعثر عند الموت أو الإنعاش (معطل)
-- AddEventHandler('esx:onPlayerDeath', function()
--     stumblePhase = 0
-- end)

-- AddEventHandler('esx:onPlayerSpawn', function()
--     stumblePhase = 0
-- end)

-- إضافة في بداية الملف
math.randomseed(GetGameTimer())
































