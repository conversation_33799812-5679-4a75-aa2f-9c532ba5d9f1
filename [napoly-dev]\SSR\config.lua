Config = {}

-- إعدادات عامة للنظام
Config.Debug = false -- تفعيل/تعطيل رسائل التتبع

-- إعدادات صوت التسونامي
Config.TsunamiSiren = {
    SoundFile = "tsunami_siren",  -- اسم ملف الصوت
    Volume = 0.8,                -- مستوى الصوت (0.0 - 1.0)
    Duration = 30000,            -- مدة تشغيل الصوت (30 ثانية)
    MaxDistance = 500.0          -- المسافة القصوى لسماع الصوت
}

-- إعدادات الرعد (معطلة)
Config.Thunder = {
    -- إعدادات الرعد العادي (معطلة)
    Normal = {
        SoundFile = "",             -- اسم ملف الصوت (معطل)
        Volume = 0.0,               -- مستوى الصوت (معطل)
        Shake = 0.0,                -- قوة الاهتزاز (معطلة)
        MaxDistance = 0.0,          -- المسافة القصوى لسماع الصوت (معطلة)
        FlashIntensity = 0.9,       -- شدة الوميض (محتفظ بها للإضاءة فقط)
        MinDelay = 5000,            -- الحد الأدنى للتأخير بين الرعود
        MaxDelay = 10000            -- الحد الأقصى للتأخير بين الرعود
    },

    -- إعدادات الرعد القوي (معطلة)
    Intense = {
        SoundFile = "",
        Volume = 0.0,
        Shake = 0.0,
        MaxDistance = 0.0,
        FlashIntensity = 1.0,
        MinDelay = 3000,
        MaxDelay = 6000
    }
}

-- إعدادات تأثيرات التعثر (معطلة)
Config.Stumble = {
    MinInterval = 999999999,        -- الحد الأدنى للوقت بين التعثرات (معطل)

    Normal = {
        RagdollTimeMin = 0,         -- الحد الأدنى لمدة التعثر (معطل)
        RagdollTimeMax = 0,         -- الحد الأقصى لمدة التعثر (معطل)
        PushForce = 0.0,            -- قوة الدفع العادية (معطلة)
        Intensity = 0.0,            -- شدة التعثر العادية (معطلة)
        ShakeIntensity = 0.0        -- قوة اهتزاز الكاميرا العادية (معطلة)
    },

    Intense = {
        RagdollTimeMin = 0,
        RagdollTimeMax = 0,
        PushForce = 0.0,
        Intensity = 0.0,
        ShakeIntensity = 0.0
    }
}

-- إعدادات المراحل الزمنية (بالثواني)
Config.WeatherPhases = {
    CALM = 300,        -- 5 دقائق
    OVERCAST = 240,    -- 4 دقائق
    CLOUDY = 180,      -- 3 دقائق
    LIGHT_RAIN = 120,  -- 2 دقيقة
    STORM = 60         -- 1 دقيقة (العاصفة الرعدية)
}

-- إعدادات الطقس
Config.Weather = {
    RainLevel = 0.8,           -- مستوى المطر خلال العاصفة
    CloudOpacity = 1.0,        -- كثافة الغيوم
    TransitionTime = 15.0,     -- وقت الانتقال بين حالات الطقس (بالثواني)
    
    -- إعدادات متزامنة مع cd_easytime
    SyncEnabled = true,        -- تفعيل المزامنة مع cd_easytime
    DefaultWeather = 'CLEAR',  -- الطقس الافتراضي
    
    -- إعدادات الرياح
    WindSpeed = {
        Normal = 5.0,         -- سرعة الرياح العادية
        Storm = 8.0,          -- سرعة الرياح أثناء العاصفة
        Thunder = 12.0        -- سرعة الرياح أثناء العواصف الرعدية
    },
    
    -- إعدادات التأثيرات الخاصة
    SpecialEffects = {
        Dawn = {
            StartHour = 5,
            EndHour = 7,
            Intensity = 0.3
        },
        Dusk = {
            StartHour = 18,
            EndHour = 20,
            Intensity = 0.3
        }
    }
}

-- إعدادات فحص الخارج/الداخل
Config.LocationCheck = {
    StartOffset = 0.1,
    RayHeight = 20.0,
    IgnorePlayer = 1,
    IgnoreVehicles = 1,
    RayType = 1
}

-- إعدادات تأثيرات الضوء
Config.LightingEffects = {
    CycleName = "lightning",    -- اسم دورة الإضاءة
    NormalDuration = 1500,      -- مدة الوميض العادي
    IntenseDuration = 2000      -- مدة الوميض القوي
}

-- إعدادات Discord Webhook
Config.DiscordWebhook = {
    url = "https://discord.com/api/webhooks/1372200058342346882/a8TWp4L_9odyOEisQon4QSqfCkIi775f1p9CUKJ4RMk_Asqll_mcmFpmRyTWDldX0vky",      -- رابط Webhook الخاص بك
    botName = "Server Restarter",  -- اسم البوت
    avatar = "https://c0.klipartz.com/pngpicture/739/431/gratis-png-tormenta-de-tornado-vendaval-ciclon-tropical-tornado-thumbnail.png",                   -- رابط الصورة الرمزية (اختياري)
    colors = {
        default = 16711680,        -- أحمر
        yellow = 15335168,         -- أصفر
        green = 65280,             -- أخضر
        red = 16711680             -- أحمر
    }
}  


Config.DefaultRestartTime = 300  -- 5 دقائق (بالثواني)

-- إعدادات الإشعارات
Config.Notifications = {
    -- تنسيق إشعارات إعادة التشغيل
    Restart = {
        template = '<div style="position: fixed; top: 20%; left: 50%; transform: translateX(-50%); background-color: rgb(255, 172, 51); color: white; padding: 12px 24px; border-radius: 4px; font-family: Arial, sans-serif; display: flex; align-items: center; gap: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"><i class="fas fa-exclamation-triangle" style="color: white;"></i><div><div style="font-weight: bold; margin-bottom: 4px;">Server Announcement by txAdmin:</div>{0}</div></div>',
        color = {255, 172, 51}
    },
    -- تنسيق إشعارات الإلغاء
    Cancel = {
        template = '<div style="position: fixed; top: 20%; left: 50%; transform: translateX(-50%); background-color: rgb(40, 167, 69); color: white; padding: 12px 24px; border-radius: 4px; font-family: Arial, sans-serif; display: flex; align-items: center; gap: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"><i class="fas fa-check-circle" style="color: white;"></i><div><div style="font-weight: bold; margin-bottom: 4px;">Server Announcement:</div>{0}</div></div>',
        color = {40, 167, 69}
    },
    -- رسائل الإشعارات
    Messages = {
        restart = "سيتم إعادة تشغيل السيرفر خلال %d دقائق.",
        cancel = "تم إلغاء عملية إعادة تشغيل السيرفر."
    }
}


-- Timer Settings | إعدادات المؤقت
Config.DefaultRestartTime = 300 -- وقت العد التنازلي الافتراضي بالثواني (5 دقائق)

-- Player Fall Settings | إعدادات سقوط اللاعب (معطلة)
Config.PlayerFall = {
    cooldown = 999999999,         -- وقت الانتظار بين السقطات (معطل)
    firstStumbleChance = 0.0,     -- احتمالية التعثر الأول (معطلة)
    secondStumbleChance = 0.0,    -- احتمالية التعثر الثاني (معطلة)
    finalFallChance = 0.0,        -- احتمالية السقوط النهائي (معطلة)

    -- مدة السقوط (معطلة)
    firstStumbleDuration = 0,     -- مدة التعثر الأول (معطلة)
    secondStumbleDuration = 0,    -- مدة التعثر الثاني (معطلة)
    finalFallDuration = 0         -- مدة السقوط النهائي (معطلة)
}

-- UI Settings | إعدادات الواجهة
Config.UI = {
    position = {
        right = "20px", -- المسافة من اليمين
        top = "50%"     -- المسافة من الأعلى
    },
    warningTime = 30 -- الوقت بالثواني عندما يبدأ المؤقت بالوميض
}

Config.Commands = {
    startRestart = 'srestart',
    cancelRestart = 'crestart'
}

Config.AdminGroups = {
    ['superadmin'] = true,
    ['admin'] = true
}

return Config


