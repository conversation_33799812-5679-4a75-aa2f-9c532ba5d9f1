local ESX = exports["es_extended"]:getSharedObject()
local isRestartInProgress = false
local restartTime = 0



-- Function to send Discord webhook
function SendToDiscord(title, message, color, source)
    local sourceInfo = "وحدة التحكم"
    local playerName = "غير معروف"
    local playerGroup = "غير معروف"
    local currentTime = os.date("%Y-%m-%d %H:%M:%S")
    local arabicDay = {
        ["Sunday"] = "الأحد",
        ["Monday"] = "الإثنين",
        ["Tuesday"] = "الثلاثاء",
        ["Wednesday"] = "الأربعاء",
        ["Thursday"] = "الخميس",
        ["Friday"] = "الجمعة",
        ["Saturday"] = "السبت"
    }
    local dayName = arabicDay[os.date("%A")] or "غير معروف"
    
    if source > 0 then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            sourceInfo = xPlayer.getName()
            playerName = xPlayer.getName()
            playerGroup = xPlayer.getGroup()
        end
    end

    local embed = {
        {
            ["title"] = "🔄 " .. title,
            ["description"] = "```" .. message .. "```",
            ["color"] = color,
            ["fields"] = {
                {
                    ["name"] = "👤 المسؤول المنفذ",
                    ["value"] = "```" .. sourceInfo .. "```",
                    ["inline"] = true
                },
                {
                    ["name"] = "🎭 الرتبة الإدارية",
                    ["value"] = "```" .. playerGroup .. "```",
                    ["inline"] = true
                },
                {
                    ["name"] = "📅 التاريخ والوقت",
                    ["value"] = "```يوم " .. dayName .. "\n" .. currentTime .. "```",
                    ["inline"] = false
                }
            },
            ["thumbnail"] = {
                ["url"] = "https://i.imgur.com/4M7IWwP.png"
            },
            ["footer"] = {
                ["text"] = "نظام إعادة تشغيل السيرفر | " .. os.date("%Y-%m-%d"),
                ["icon_url"] = "https://i.imgur.com/4M7IWwP.png"
            },
            ["timestamp"] = os.date("!%Y-%m-%dT%H:%M:%SZ")
        }
    }

    PerformHttpRequest(Config.DiscordWebhook.url, function(err, text, headers) end, 'POST', json.encode({
        username = Config.DiscordWebhook.botName,
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Function to start server restart
-- Function to start server restart
function StartServerRestart(seconds, source, isAuto)
    if isRestartInProgress then return end
    
    isRestartInProgress = true
    restartTime = seconds
    
    -- إخطار cd_easytime ببدء التسونامي
    TriggerEvent('cd_easytime:StartTsunamiCountdown', true)
    
    -- بدء العد التنازلي
    TriggerClientEvent('ssr:startCountdown', -1, seconds, isAuto)
    
    -- تشغيل صوت التسونامي ثلاث مرات
    local sirenCount = 0
    local function playSiren()
        if sirenCount < 3 then
            TriggerClientEvent('InteractSound_CL:PlayOnAll', -1, Config.TsunamiSiren.SoundFile, Config.TsunamiSiren.Volume)
            sirenCount = sirenCount + 1
            
            -- إيقاف الصوت الحالي وتشغيل الصوت التالي بعد المدة المحددة
            Citizen.SetTimeout(Config.TsunamiSiren.Duration, function()
                TriggerClientEvent('InteractSound_CL:Stop', -1)
                if sirenCount < 3 then
                    -- انتظر ثانية واحدة قبل تشغيل الصوت التالي
                    Citizen.SetTimeout(1000, playSiren)
                end
            end)
        end
    end
    
    -- بدء تشغيل الصوت
    playSiren()
    
    -- إرسال إشعار في الشات باللغة العربية
    TriggerClientEvent('chat:addMessage', -1, {
        template = Config.Notifications.Restart.template,
        args = {string.format(Config.Notifications.Messages.restart, math.floor(seconds/60))},
        color = Config.Notifications.Restart.color
    })
    
    -- إرسال إشعار إضافي للتأكيد
    TriggerClientEvent('esx:showNotification', -1, string.format(Config.Notifications.Messages.restart, math.floor(seconds/60)))
    
    -- إرسال لوق للديسكورد إذا كان مفعل
    if Config.DiscordWebhook.url ~= "WEBHOOK_URL_HERE" then
        local message = string.format("تم جدولة إعادة تشغيل السيرفر خلال %d دقائق", math.floor(seconds/60))
        SendToDiscord("إعادة تشغيل السيرفر", message, Config.DiscordWebhook.colors.default, source)
    end
    


    -- إنشاء مؤقت للطرد
    Citizen.CreateThread(function()
        Citizen.Wait(seconds * 1000) -- انتظار حتى نهاية العد التنازلي
        
        if isRestartInProgress then
            -- طرد جميع اللاعبين
            local players = GetPlayers()
            for _, playerId in ipairs(players) do
                DropPlayer(playerId, "تم إعادة تشغيل السيرفر")
            end
        end
    end)
end

-- Function to cancel server restart
function CancelServerRestart(source)
    if not isRestartInProgress then return end
    
    isRestartInProgress = false
    
    -- Broadcast cancel to all clients with Arabic message
    TriggerClientEvent('ssr:cancelRestart', -1)
    
    -- إرسال إشعار إلغاء في الشات باللغة العربية
    TriggerClientEvent('chat:addMessage', -1, {
        template = Config.Notifications.Cancel.template,
        args = {Config.Notifications.Messages.cancel},
        color = Config.Notifications.Cancel.color
    })
    
    -- إرسال إشعار إضافي للتأكيد
    TriggerClientEvent('esx:showNotification', -1, Config.Notifications.Messages.cancel)
    
    -- Log to Discord
    local sourceInfo = "Console"
    if source > 0 then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            sourceInfo = string.format("Admin: %s (%s)", xPlayer.getName(), xPlayer.getIdentifier())
        end
    end
    
    SendToDiscord(
        "إلغاء إعادة تشغيل السيرفر",
        "تم إلغاء عملية إعادة تشغيل السيرفر",
        Config.DiscordWebhook.colors.yellow,
        source
    )
end

-- Register server commands
RegisterCommand(Config.Commands.startRestart, function(source, args)
    --print("[SSR] Start restart command received from source: " .. source)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    -- التحقق من الصلاحيات
    if source == 0 then -- Console
        --print("[SSR] Command executed from console")
        local minutes = tonumber(args[1]) or (Config.DefaultRestartTime / 60)
        local seconds = math.floor(minutes * 60)
        StartServerRestart(seconds, source, false)
    elseif xPlayer then
        if Config.AdminGroups[xPlayer.getGroup()] then
            --print("[SSR] Permission granted for player: " .. GetPlayerName(source))
            local minutes = tonumber(args[1]) or (Config.DefaultRestartTime / 60)
            local seconds = math.floor(minutes * 60)
            StartServerRestart(seconds, source, false)
        else
            --print("[SSR] Permission denied for player: " .. GetPlayerName(source))
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = true,
                args = {"System", "You don't have permission to use this command!"}
            })
        end
    else
        --print("[SSR] Invalid player source: " .. source)
    end
end)

RegisterCommand(Config.Commands.cancelRestart, function(source)
    --print("[SSR] Cancel restart command received from source: " .. source)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if source == 0 then -- Console
        --print("[SSR] Command executed from console")
        CancelServerRestart(source)
    elseif xPlayer then
        if Config.AdminGroups[xPlayer.getGroup()] then
            --print("[SSR] Permission granted for player: " .. GetPlayerName(source))
            CancelServerRestart(source)
        else
            --print("[SSR] Permission denied for player: " .. GetPlayerName(source))
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = true,
                args = {"System", "You don't have permission to use this command!"}
            })
        end
    else
        --print("[SSR] Invalid player source: " .. source)
    end
end)

-- Listen for txAdmin scheduled restart
AddEventHandler('txAdmin:events:scheduledRestart', function(eventData)
    if eventData.secondsRemaining ~= nil then
        StartServerRestart(eventData.secondsRemaining, 0, true) -- إضافة true لتحديد أنه ريستارت تلقائي
    end
end)

-- تسجيل حدث تشغيل الصوت
RegisterServerEvent('InteractSound_SV:PlayWithinDistance')
AddEventHandler('InteractSound_SV:PlayWithinDistance', function(maxDistance, soundFile, volume)
    local source = source
    local coords = GetEntityCoords(GetPlayerPed(source))
    
    TriggerClientEvent('InteractSound_CL:PlayWithinDistance', -1, source, maxDistance, soundFile, volume, coords)
end)

