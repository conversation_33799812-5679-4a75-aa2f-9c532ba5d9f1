<?xml version="1.0" encoding="utf-8"?>

<CVehicleModelInfo__InitDataList>
	<residentTxd>vehshare</residentTxd>
	<residentAnims />
	<InitDatas>
	<Item>
		<modelName>gm2005</modelName> 
		<txdName>gm2005</txdName> 
		<handlingId>gm2005</handlingId> 
		<gameName>gm2005</gameName> 
		<vehicleMakeName>gm2005</vehicleMakeName> 
		<expressionDictName>null</expressionDictName> 
		<expressionName>null</expressionName> 
		<animConvRoofDictName>null</animConvRoofDictName> 
		<animConvRoofName>null</animConvRoofName> 
		<animConvRoofWindowsAffected /> 
		<ptfxAssetName>null</ptfxAssetName> 
		<audioNameHash>DUBSTA</audioNameHash>
		<layout>LAYOUT_STANDARD</layout>
		<coverBoundOffsets>gm2005_COVER_OFFSET_INFO</coverBoundOffsets>
		<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
		<scenarioLayout />
		<cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
		<aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
		<bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
		<povCameraName>DEFAULT_POV_CAMERA</povCameraName>
		<FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000" />
		<FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000" />
		<FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.065000" z="0.030000" />
		<FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.065000" z="0.030000" />
		<FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
		<FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
		<FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
		<FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
		<FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.550000" />
		<FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
		<FirstPersonMobilePhoneSeatIKOffset>
	<Item>
	<Offset x="0.136000" y="0.156000" z="0.435000" />
	<SeatIndex value="2" />
	</Item>
	<Item>
	<Offset x="0.136000" y="0.156000" z="0.435000" />
	<SeatIndex value="3" />
	</Item>
		</FirstPersonMobilePhoneSeatIKOffset> 
		<PovCameraOffset x="0.000000" y="-0.130000" z="0.640000" />
		<PovCameraVerticalAdjustmentForRollCage value="0.000000" />
		<PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
		<vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
		<shouldUseCinematicViewMode value="true" />
		<shouldCameraTransitionOnClimbUpDown value="false" />
		<shouldCameraIgnoreExiting value="false" />
		<AllowPretendOccupants value="true" />
		<AllowJoyriding value="true" />
		<AllowSundayDriving value="true" />
		<AllowBodyColorMapping value="true" />
		<wheelScale value="0.296800" />
		<wheelScaleRear value="0.296800" />
		<dirtLevelMin value="0.000000" />
		<dirtLevelMax value="0.550000" />
		<envEffScaleMin value="0.000000" />
		<envEffScaleMax value="1.000000" />
		<envEffScaleMin2 value="0.000000" />
		<envEffScaleMax2 value="1.000000" />
		<damageMapScale value="0.900000" />
		<damageOffsetScale value="0.700000" />
		<diffuseTint value="0x00FFFFFF" />
		<steerWheelMult value="1.000000" />
		<HDTextureDist value="5.000000" />
		<lodDistances content="float_array">
		15.000000
		25.000000
		65.000000
		130.000000
		500.000000
		500.000000
		</lodDistances>
		<minSeatHeight value="0.868" />
		<identicalModelSpawnDistance value="20" />
		<maxNumOfSameColor value="10" />
		<defaultBodyHealth value="1000.000000" />
		<pretendOccupantsScale value="1.000000" />
		<visibleSpawnDistScale value="1.000000" />
		<trackerPathWidth value="2.000000" />
		<weaponForceMult value="1.000000" />
		<frequency value="100" />
		<swankness>SWANKNESS_3</swankness>
		<maxNum value="999" />
		<flags>FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
		<type>VEHICLE_TYPE_CAR</type>
		<plateType>VPT_BACK_PLATES</plateType>
		<dashboardType>VDT_GENTAXI</dashboardType>
		<vehicleClass>VC_SPORT</vehicleClass>
		<wheelType>VWT_SPORT</wheelType>
		<trailers />
		<additionalTrailers />
		<drivers />
		<extraIncludes>
		<Item>EXTRA_1 EXTRA_2 EXTRA_3 EXTRA_4 EXTRA_5</Item>
		</extraIncludes>
		<doorsWithCollisionWhenClosed />
		<driveableDoors />
		<bumpersNeedToCollideWithMap value="false" />
		<needsRopeTexture value="false" />
		<requiredExtras>EXTRA_1</requiredExtras>
		<rewards />
		<cinematicPartCamera>
		<Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
		<Item>WHEEL_FRONT_LEFT_CAMERA</Item>
		<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
		<Item>WHEEL_REAR_LEFT_CAMERA</Item>
		</cinematicPartCamera>
		<NmBraceOverrideSet />
		<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
		<buoyancySphereSizeScale value="1.000000" />
		<pOverrideRagdollThreshold type="NULL" />
		<firstPersonDrivebyData>
		<Item>STD_BUFFALO_FRONT_LEFT</Item>
		<Item>STD_BUFFALO_FRONT_RIGHT</Item>
		</firstPersonDrivebyData>
	</Item>
	</InitDatas>
	<txdRelationships>
	<Item>
		<parent>vehicles_poltax_interior</parent>
		<child>gm2005</child>
	</Item>
	</txdRelationships>
</CVehicleModelInfo__InitDataList>
