<?xml version="1.0" encoding="UTF-8"?>
<!-- justRELAX1 / Most-Closer-to-reality  (01/08/2019) -->
<CVehicleModelInfoVarGlobal>    
	<Sirens>
    <Item>
      <id value="121013123378970"/> 
      <name>hlal1</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="100.00000000"/>
      <lightFalloffExponent value="100.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="650"/>
      <leftHeadLight>
        <sequencer value=""/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value=""/>
      </rightHeadLight>
      <leftTailLight>
      <sequencer value=""/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value=""/>
      </rightTailLight>
      <leftHeadLightMultiples value="0"/>
      <rightHeadLightMultiples value="0"/>
      <leftTailLightMultiples value="0"/>
      <rightTailLightMultiples value="0"/>
      <useRealLights value="true"/>
      <sirens>
        <Item> <!--Siren 1-->
          <rotation>
            <delta value="0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726958163"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726958163"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 2-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="340874323"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="340874323"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 3-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726380171"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726380171"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 4-->
          <rotation>
            <delta value="-0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="341443211"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="341443211"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 5-->
          <rotation>
            <delta value="-2.35619449"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="341443211"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-2.35619449"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="341443211"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 6-->
          <rotation>
            <delta value="2.35619449"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726380171"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="2.35619449"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726380171"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 7-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="340874323"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="340874323"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 8-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726958163"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2726958163"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		<!-- siren9 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren10 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren11 -->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren12 -->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren13 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren14 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren15 -->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren16 -->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--Siren 17-->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--Siren 18-->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
        <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--Siren 19-->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="44344482"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.70000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <!--Siren 20-->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819246728"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.70000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		<!-- siren21 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2852170240"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren22 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>
  </Sirens>
</CVehicleModelInfoVarGlobal>