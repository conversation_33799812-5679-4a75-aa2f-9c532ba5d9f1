function AddTextEntry(key, value)
	Citizen.InvokeNative(Get<PERSON>ash<PERSON><PERSON>("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()

	AddTextEntry('bmwx6', 'بي ام دبليو خاص - X6')
	AddTextEntry('expmax20', 'فورد اكس ادشن - 2020')
	AddTextEntry('g65amg', 'مرسيدس جي كلاس - 2013')
	AddTextEntry('g5004x4', 'مرسيدس جي كلاس اوف رود - 2019 ')
	AddTextEntry('g632019', 'مرسيدس جي كلاس - 2019')
	AddTextEntry('gclass', 'مرسيدس جي كلاس - اوف رود')
	AddTextEntry('hdd', 'تايوتا لاند كروزر - GX')
	AddTextEntry('lex57015', 'جيب لكزس - 2015')
	AddTextEntry('lxs', 'جيب لكزس  - Black Edition')
	AddTextEntry('lxs_2019', 'جيب لكزس - 2019')
	AddTextEntry('q820', 'اودي - Q8 2020')
	AddTextEntry('rb3_2006', 'لاند كروزر ربع - 2006')
	AddTextEntry('rb3_2017', 'لاند كروزر ربع - 2017')
	AddTextEntry('rrst', 'رنج روفر فوج 2020')
	AddTextEntry('subn', 'شيفرولية سوبربان - 2010')
	AddTextEntry('taho89', 'شيفرولية سوبربان - 1989')
	AddTextEntry('tahoe21', 'شيفرولية تاهو - 2021')
	AddTextEntry('type262', 'فولكس فاجن فان - 1962')
	AddTextEntry('vxr_2020', 'لاند كروزر - VXR 2020')
	AddTextEntry('w463a', 'مرسيدس جي كلاس برابوس - 2019 ')
	
end)