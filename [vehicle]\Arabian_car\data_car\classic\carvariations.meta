﻿<?xml version="1.0" encoding="utf-8"?>
<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>560SEC87</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
            0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Pearl Gray Metallic -->
            9
            9
            4
            4
			4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Arctic White -->
            111
            111
            0
            4
			4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Anthracite Gray Metallic -->
            2
            2
            4
            32
			32
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Pearl Metallic / Silver -->
            0
            9
            65
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green Metallic -->
            49
            49
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sea Foam Green Metallic -->
            170
            170
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Diamond Blue Metallic / Blue-Gray -->
            230
            10
            4
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bison Brown Metallic / Brown -->
            187
            95
            3
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Desert Taupe Metallic -->
            8
            8
            4
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Mocca -->
            101
            101
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Champagne Metallic / Bronze -->
            99
            187
            4
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Desert Red -->
            31
            31
            1
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Signal Red / Silver -->
            27
            6
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cabernet Red Metallic -->
            34
            34
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Ivory / Brown -->
            107
            187
            0
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Pueblo Beige -->
            99
            99
            0
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Smoke Silver Metallic -->
            6
            6
            4
            111
            111
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Astral Silver Metallic / Dark Gray -->
            4
            3
            4
            63
            63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ascot Gray / Dark Gray -->
            7
            3
            1
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Agate Green -->
            228
            228
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Blue Green Metallic / Dark Gray -->
            195
            3
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Deep Blue -->
            172
            172
            3
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Midnight Blue -->
            164
            164
            0
            5
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Nautical Blue Metallic / Blue -->
            62
            63
            65
            5
            5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>560sec87_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>750il</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            8 
            8 
            134 
            156
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>908_750il_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="0" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>Benz300SEL</modelName>
      <colors>
        <Item>
          <indices content="char_array"> 
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            0
            4
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            6
            6
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            10
            10
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            21
            21
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            23
            23
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            25
            25
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            33
            33
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maple Red Metallic -->
            167
            167
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>boss302</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            99 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            116 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            73 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            28 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            49 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            53 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            88 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            14 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            4 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            8 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36 
            1
            3
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>765_boss302_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro68</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            0
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            0
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            0
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            0
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            0
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            0
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro68b</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            111
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            210
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            171
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            229
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            233
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            28
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            61
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            234
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            107
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            68
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            235
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            170
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            168
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            34
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            169
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            49
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            225
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            45
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="505" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro68c</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            111
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            210
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            171
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            229
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            233
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            28
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            61
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            234
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            107
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            68
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            235
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            170
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            168
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            34
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            169
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            49
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            225
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            45
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="505" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro69</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            0
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            0
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            0
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            0
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            0
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            0
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            0
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro68b</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            111
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            210
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            171
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            229
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            233
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            28
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            61
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            234
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            107
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            68
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            235
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            170
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            168
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            34
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            169
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            49
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            225
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            45
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="505" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>camaro68c</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Tuxedo Black -->
            0
            111
            0
            0
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            111
            0
            0
            111
			28
			28
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grecian Green Metallic -->
            210
            0
            4
            210
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafrost Green Metallic -->
            171
            0
            4
            171
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Grotto Blue Metallic -->
            229
            111
            4
            229
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Rallye Green Metallic -->
            233
            111
            53
            233
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Matador Red -->
            28
            0
            0
            28
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Fathom Blue Metallic -->
            61
            111
            4
            61
			181
			181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tripoli Turquoise Metallic -->
            234
            111
            74
            234
			234
			234
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Palomino Ivory -->
            107
            0
            0
            107
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Island Teal Metallic -->
            68
            111
            0
            68
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Teal Blue Metallic -->
            235
            111
            4
            235
			131
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sequoia Green Metallic -->
            170
            111
            6
            170
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ash Gold Metallic -->
            168
            0
            106
            168
			168
			168
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Maroon Metallic -->
            34
            111
            8
            34
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Butternut Yellow -->
            169
            0
            0
            169
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- British Green Metallic -->
            49
            111
            4
            49
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- LeMans Blue Metallic -->
            225
            111
            68
            225
			0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Corvette Bronze Metallic -->
            45
            111
            4
            45
			0
			0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="505" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice89</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            27 
            0 
            28 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            64 
            0 
            73 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            111 
            0 
            111 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            0 
            1		
            2 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            89 
            1		
            2 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            3 
            1		
            5 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            38 
            1		
            89 
            0
            93
            65		
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            70 
            1		
            67 
            0
            93
            65		
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprs</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
            0
			0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Pearl Gray Metallic -->
            9
            9
            4
            4
			4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Arctic White -->
            111
            111
            0
            4
			4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Anthracite Gray Metallic -->
            2
            2
            4
            32
			32
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Pearl Metallic / Silver -->
            0
            9
            65
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green Metallic -->
            49
            49
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sea Foam Green Metallic -->
            170
            170
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Diamond Blue Metallic / Blue-Gray -->
            230
            10
            4
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bison Brown Metallic / Brown -->
            187
            95
            3
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Desert Taupe Metallic -->
            8
            8
            4
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Mocca -->
            101
            101
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Champagne Metallic / Bronze -->
            99
            187
            4
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Desert Red -->
            31
            31
            1
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Signal Red / Silver -->
            27
            6
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cabernet Red Metallic -->
            34
            34
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Ivory / Brown -->
            107
            187
            0
            104
            104
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Pueblo Beige -->
            99
            99
            0
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Smoke Silver Metallic -->
            6
            6
            4
            111
            111
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Astral Silver Metallic / Dark Gray -->
            4
            3
            4
            63
            63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ascot Gray / Dark Gray -->
            7
            3
            1
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Agate Green -->
            228
            228
            0
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Blue Green Metallic / Dark Gray -->
            195
            3
            4
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Deep Blue -->
            172
            172
            3
            106
            106
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Midnight Blue -->
            164
            164
            0
            5
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Nautical Blue Metallic / Blue -->
            62
            63
            65
            5
            5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>chall70</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            30
            30
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            5 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            73 
            73 
            4 
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            34 
            34 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            36
            36
            4
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            8
            8
            4 
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            11 
            11 
            4
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            28
            28
            4 	
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            20
            20
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            51
            51
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            92
            92
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            107 
            107 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            145 
            145 
            87 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            157 
            157 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            19
            19 
            4 
            156
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>chall70_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>chall70drag</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            30
            30
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            5 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            73 
            73 
            4 
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            34 
            34 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            36
            36
            4
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            8
            8
            4 
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            11 
            11 
            4
            156 
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            28
            28
            4 	
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            20
            20
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            51
            51
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            92
            92
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            107 
            107 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            145 
            145 
            87 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            157 
            157 
            4 
            156
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            19
            19 
            4 
            156
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>chall70_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>White Plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>charger69</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0 
            0 
            0
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            4 
            0 
            4 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            12 
            12 
            134 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            26 
            24 
            26 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            29 
            112 
            29 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36 
            112 
            36 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            48 
            24 
            48 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            53 
            112 
            53 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
           62 
            0 
            62 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            63 
            0 
            63 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            90 
            112 
            90 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            138 
            12 
            138 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>696_charger69_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities />
      </plateProbabilities>
      <lightSettings value="696" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>corvette63</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Surf Blue -->
            68
            68
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            120
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Riverside Red -->
            33
            32
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ember Red -->
            34
            34
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Monaco Blue Poly -->
            62
            65
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cordovan Brown Poly -->
            96
            96
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Ermine White -->
            112
            112
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Saddle Tan Poly -->
            100
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>1063_corvette63_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>spyker</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            8 
            38 
            8 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            147 
            107 
            4 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            64 
            112 
            74 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            52 
            5 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            38 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            61 
            112 
            73 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            34 
            112 
            73 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            111 
            38 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            33 
            107 
            4 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27 
            112 
            5 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            38 
            5 
            138 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            89 
            106 
            89 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>378_spyker_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="20" />
          </Item>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="30" />
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="927" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>moss</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            8 
            38 
            8 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            147 
            107 
            4 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            64 
            112 
            74 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            52 
            5 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            38 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            61 
            112 
            73 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            34 
            112 
            73 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            111 
            38 
            112 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            33 
            107 
            4 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27 
            112 
            5 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            38 
            5 
            138 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            89 
            106 
            89 
            4 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>377_moss_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="20" />
          </Item>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="30" />
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="926" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>charger</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0 
            120 
            0
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            0 
            112 
            0
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            6 
            6 
            36
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            62 
            62 
            0
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>379_charger_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities />
      </plateProbabilities>
      <lightSettings value="928" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>cx75</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            82 
            70 
            0 
            0 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            2 
            5 
            0 
            8 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            131 
            5 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36 
            138 
            2 
            0 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>380_cx75_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="929" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>vulcan</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            51 
            91 
            91 
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>381_vulcan_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="930" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>dbs</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            2 
            111 
            107 
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>382_dbs_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="931" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>firebird</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            141 
            64 
            63
            0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>383_firebird_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities />
      </plateProbabilities>
      <lightSettings value="932" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>firebird77</modelName>
      <colors>
        <!--
	  PAINT:1 	PAINT:2 	PAINT:PEARL 	PAINT:4 	PAINT:6 	PAINT:7
	  PRIMARY	SECONDARY	PEARL			RIM			DASH (Trim)	LIVERY (Dashboard)
	  0			98			3				11			37			37
	  0			98			3				0			37			37
	  112		37			112				132			0			112
	  112		37			112				0			0			112
	  31		31			31				31			156			0
	  31		31			31				0			156			0
	  31		4			99				31			156			31
	  34		11			32				0			156			112
	  138		11			138				2			156			0
	  90		97			90				2			156			0
	  52		102			52				49			156			0
	  62		62			62				62			156			112
	  -->
        <Item>
          <indices content="char_array">
            0
            98
            3
            11 
			156
			37
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            0
            98
            3
            11 
			37
			37
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112
            37
            112
            132 
			0
			112
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112
            37
            112
            0 
			0
			112
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            31
            31
            31 
			156
			0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            31
            31
            0 
			156
			0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            4
            99
            31 
			156
			31
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            41
            11
            32
            0 
			156
			112
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            138
            11
            138
            2 
			156
			0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            90
            97
            90
            2 
			156
			0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            52
            102
            52
            49 
			156
			112
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            62
            62
            62
            62 
			156
			112
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>726_firebird77_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="726" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice91</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maple Red Metallic -->
            167
            167
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice91b</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maple Red Metallic -->
            167
            167
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice91p</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maple Red Metallic -->
            167
            167
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice93</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice93b</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice93p</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic -->
            5
            5
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Silver Metallic / Medium Gray -->
            5
            9
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic -->
            65
            65
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Sapphire Blue Metallic / Medium Gray -->
            65
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice95</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic -->
            74
            74
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic / Medium Gray -->
            74
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic -->
            61
            61
            170
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic / Medium Gray -->
            61
            9
            170
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Granite Metallic -->
            99
            99
            93
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice95b</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic -->
            74
            74
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic / Medium Gray -->
            74
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic -->
            61
            61
            170
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic / Medium Gray -->
            61
            9
            170
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Granite Metallic -->
            99
            99
            93
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>caprice95p</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black / Medium Gray -->
            0
            9
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White -->
            111
            111
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright White / Medium Gray -->
            111
            9
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic -->
            74
            74
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Adriatic Blue Metallic / Medium Gray -->
            74
            9
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic -->
            61
            61
            170
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Adriatic Blue Metallic / Medium Gray -->
            61
            9
            170
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Driftwood Metallic -->
            93
            93
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic -->
            35
            35
            2
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Garnet Red Metallic / Medium Gray -->
            35
            9
            2
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Granite Metallic -->
            99
            99
            93
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Purple Pearl Metallic -->
            72
            72
            6
			0
			107
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>impala96</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Green-Gray Metallic -->
            11
            11
            234
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Cherry Metallic -->
            227
            227
            34
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>caprice91_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>gsxb</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            134
            134
            0
            156
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>2929_vehicles_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="0" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>impala672</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            28 
            7 
            26 
            106 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            56 
            7 
            26 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            52 
            7 
            6 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            92 
            7 
            5 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            62 
            7 
            111 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            74 
            74 
            111 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            74 
            7 
            73 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            69 
            7 
            65 
            106 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            103 
            8 
            0 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            97 
            7 
            98 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            107 
            7 
            4 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            107 
            8 
            5 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112 
            7 
            0 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            68 
            74 
            51 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            92 
            7 
            106 
            156 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            32 
            32 
            0 
            106 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>mb300sl</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            3
            3
            134
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            7
            7
            134
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            3
            3
            134
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27
            27
            28
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            34
            34
            28
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            66
            66
            61
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            73
            73
            70
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            76
            76
            70
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            88
            88
            88
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            90
            90
            88
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            102
            108
            88
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>2212_sl_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>mercw126</modelName>
      <colors>
        <Item>
          <indices content="char_array"> 
            0
            0
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            0
            4
            0
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            6
            6
            111
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            10
            10
            111
			0
			107
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            21
            21
            0
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            23
            23
            0
			0
			31
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            25
            25
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array"> 
            33
            33
            4
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic -->
            164
            164
            7
			0
			63
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Black Sapphire Metallic / Medium Gray -->
            164
            9
            7
			0
			65
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maple Red Metallic -->
            167
            167
            3
			0
			5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gunmetal Metallic -->
            2
            2
            3
			0
			5
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>942_mercw126_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>standard white</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>mustang68</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Raven Black -->
            0
            27
            0
            156
            31
            31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Royal Maroon -->
            167
            111
            0
            156
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Acapulco Blue Metallic -->
            67
            111
            68
            156 
            127
            127
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Gulfstream Aqua Metallic -->
            234
            111
            68
            156 
            210
            210
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Lime Gold Metallic -->
            203
            111
            107
            156 
            203
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Wimbledon White -->
            111
            0
            0
            156 
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Diamond Blue -->
            193
            0
            0
            156 
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Seafoam Green Metallic -->
            186
            0
            0
            156 
            203
            203
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Brittany Blue Metallic -->
            74
            111
            5
            156 
            74
            74
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Highland Green Metallic -->
            49
            111
            5
            156 
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Candy Apple Red -->
            27
            111
            0
            156 
            31
            31
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tahoe Turquoise Metallic -->
            194
            111
            6
            156
            161
            161
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Meadowlark Yellow -->
            169
            0
            0
            156 
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Presidential Blue -->
            231
            111
            74
            156
			162
			162
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Sunlit Gold Metallic -->
            168
            0
            107
            156 
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Pebble Beige -->
            173
            0
            0
            156 
            106
            106
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>mustang68_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>silver67</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Tan / Brown -->
            106
            103
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Shell Grey / Tudor Grey -->
            4
            11
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Cream / British Racing Green -->
            107
            49
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Champagne Metallic -->
            99
            99
            4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver / Black -->
            6
            0
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver -->
            6
            6
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver / Maroon -->
            6
            34
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maroon / Dark Brown -->
            34
            96
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maroon -->
            34
            34
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Maroon / Black -->
            34
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Blue Metallic -->
            181
            181
            5
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Blue  / Tan -->
            62
            106
            68
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- White -->
            111
            111
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Blue Metallic / Grey -->
            181
            4
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>trans69</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            31 
            31 
            31 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            6 
            6 
            111 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            63 
            63 
            5 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            4 
            4 
            111 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            72 
            72 
            65 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            111 
            111 
            0 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            30 
            30 
            36 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            0 
            0 
            1 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            2 
            111 
            4 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            7 
            0 
            5 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5 
            0 
            111 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36 
            0 
            41 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            13 
            0 
            0 
            156
						0
						0 
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>0_default_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="0" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>z2879</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            <!-- Black -->
            0
            0
            0
            0
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Silver Metallic -->
            4
            4
            111
            4
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Blue Metallic -->
            74
            74
            4
            74
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Blue Metallic -->
            181
            4
            68
            181
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Blue Metallic -->
            62
            74
            127
            62
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Light Green -->
            186
            49
            0
            186
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Green Metallic -->
            183
            49
            5
            183
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Bright Yellow -->
            89
            90
            0
            89
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Medium Beige -->
            107
            90
            0
            107
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Camel Metallic -->
            97
            90
            184
            97
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Dark Brown Metallic -->
            187
            167
            37
            187
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Red -->
            29
            0
            0
            29
          </indices>
        </Item>
        <Item>
          <indices content="char_array">
            <!-- Carmine Metallic -->
            167
            167
            37
            167
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>z2879_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Blue Plate</Name>
            <Value value="50" />
          </Item>
          <Item>
            <Name>Yellow Plate</Name>
            <Value value="50" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2" />
      <sirenSettings value="0" />
    </Item>
  </variationData>
</CVehicleModelInfoVariation>