function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHash<PERSON><PERSON>("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()

	AddTextEntry('560sec87', ' مرسيدس كوبية - SEC 560')
	AddTextEntry('750il', ' بي ام دبليو - BMW 750 iL ')
	AddTextEntry('Benz300SEL', 'مرسيدس - SEL 300')
	AddTextEntry('boss302', 'موستنق - Boss 302')
	AddTextEntry('camaro68', 'كمارو - Ss68')
	AddTextEntry('camaro69', 'كمارو - Ss 69')
	AddTextEntry('caprice91', 'كابرس صابونة - 1991')
	AddTextEntry('caprice93', 'كابرس صابونة - 1993')
	AddTextEntry('caprs', 'كابرس - 1989')
	AddTextEntry('chall70', 'تشالنجر - RT 1970')
	AddTextEntry('corvette63', 'كورفت - SbltWindow 63')
	AddTextEntry('firebird', 'بونتياق - FireBird')
	AddTextEntry('firebird77', 'بونتياق ترانزان - 1977')
	AddTextEntry('gsxb', 'بيوك جي اس اكس - Buick GSX')
	AddTextEntry('impala96', 'شوفرليت امبالا - 1996')
	AddTextEntry('impala672', 'شوفرليت امبالا - 1967')
	AddTextEntry('mb300sl', 'مرسيدس - Sl300')
	AddTextEntry('mercw126', ' مرسيدس بنز - SEL 560')
	AddTextEntry('mustang68', 'موستنق - fastback 1968')
	AddTextEntry('silver67', 'روز رايز - 1967')
	AddTextEntry('trans69', 'بونتياق ترانزام - 1969')
	AddTextEntry('z2879', 'شوفرليت كمارو - z28')
	AddTextEntry('z2879', 'شوفرليت كمارو - z28')

	
end)