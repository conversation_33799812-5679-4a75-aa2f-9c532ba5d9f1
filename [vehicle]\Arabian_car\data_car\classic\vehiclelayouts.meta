﻿<?xml version="1.0" encoding="utf-8"?>
<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds2@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds2@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps2@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps2@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps3@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps3@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps4@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps4@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DOMINATOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.100000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="0.000000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DOMINATOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.100000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="0.000000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>FELTZER3_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="0.050000" />
      <ExtraBackwardOffset value="-0.300000" />
      <ExtraZOffset value="-0.200000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>OSIRIS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.050000" />
      <ExtraZOffset value="0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SWIFT2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>NOSE</Name>
          <Position x="0.000000" y="4.525000" z="-0.100000" />
          <Length value="1.100000" />
          <Width value="1.700000" />
          <Height value="1.200000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>BODY</Name>
          <Position x="0.000000" y="2.375000" z="0.300000" />
          <Length value="3.200000" />
          <Width value="1.800000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VIRGO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>WINDSOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="0.050000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.097500" y="-1.082400" z="0.547100" />
          <Heading value="-2.712000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.340000" y="-1.175000" z="0.548000" />
          <Heading value="1.538000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_UNARMED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ds@0h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_UNARMED_RPS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ps@0h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_ONE_HANDED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ds@1h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_ONE_HANDED_RPS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ps@1h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LUXOR2_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="17.500000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.500000" />
      <DriveByAnimInfos>
        <Item ref="LUXOR2_DB_ANIM_INFO_UNARMED_RPS" />
        <Item ref="LUXOR2_DB_ANIM_INFO_ONE_HANDED_RPS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <DriveByFlags>UseThreeAnimIntroOutro LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LUXOR2_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-17.500000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.500000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="LUXOR2_DB_ANIM_INFO_UNARMED_RDS" />
        <Item ref="LUXOR2_DB_ANIM_INFO_ONE_HANDED_RDS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <DriveByFlags>UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_HELI_SWIFT2_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-180.000000" />
      <MaxAimSweepHeadingAngleDegs value="0.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-180.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="0.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_ONE_HANDED_RDS" />
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_TWO_HANDED_RDS" />
      </DriveByAnimInfos>
      <DriveByCamera>FROGGER_REAR_LEFT_PASSENGER_AIM_CAMERA</DriveByCamera>
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_HELI_SWIFT2_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="0.000000" />
      <MaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="0.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="180.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_ONE_HANDED_RPS" />
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_TWO_HANDED_RPS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_HELI_SWIFT2_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_STANDARD_REAR_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat IsRearSeatWithActivities</SeatFlags>
      <HairScale value="-0.275000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_HELI_SWIFT2_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink>SEAT_STANDARD_REAR_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat IsRearSeatWithActivities</SeatFlags>
      <HairScale value="-0.275000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsRearSeatWithActivities</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsRearSeatWithActivities</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SWIFT2_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_SWIFT2_REAR_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack FallsOutWhenDead</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SWIFT2_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_SWIFT2_REAR_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack FallsOutWhenDead</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_LUXOR2_REAR_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_JET_FRONT_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims DisableAbnormalExits</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_LUXOR2_REAR_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_JET_FRONT_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand CannotBeJacked UseStandardInVehicleAnims DisableAbnormalExits</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SWIFT2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_SWIFT2_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SWIFT2_REAR_RIGHT</Name>
      <DoorBoneName>door_pside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_r</DoorHandleBoneName>
      <WindowId>REAR_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_SWIFT2_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_REAR_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.550000" y="-0.546000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.416400" y="-0.576000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.466000" y="-0.221000" z="0.035000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.466000" y="-0.296000" z="0.035000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753300" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="-0.100000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.740000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="-0.100000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_INFERNUS_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753300" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_INFERNUS_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.740000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_SWIFT2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_SWIFT2_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT2_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_SWIFT2_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT2_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SWIFT2_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SWIFT2_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseFinerAlignTolerance Use2DBodyBlend</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_FELTZER3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_OSIRIS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnInsideCloseDoor NoArmIkOnOutsideCloseDoor UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance AutomaticCloseDoor</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_LUXOR2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_JET_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_JET_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_JET_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_JET_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_LEFT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_LEFT_1" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_1" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_2" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_2" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_3" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_3" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims AllowEarlyDoorAndSeatUnreservation DisableJackingAndBusting ClimbUpAfterOpenDoor UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.110000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.145000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.110000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.145000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>PLANE_LUXOR_BACK_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-90.000000" y="90.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="4.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="4.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>PLANE_LUXOR_BACK_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-90.000000" y="90.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-9.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-9.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FELTZER3_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-100.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000" />
            <AngleToBlendInOffset x="30.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.175000" />
            <AngleToBlendInOffset x="10.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FELTZER3_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-100.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000" />
            <AngleToBlendInOffset x="30.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.175000" />
            <AngleToBlendInOffset x="10.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_OSIRIS_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="100.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="0.000000" y="55.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="45.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="65.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.000000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_OSIRIS_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="100.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="65.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.075000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="0.000000" y="55.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="45.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.000000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>