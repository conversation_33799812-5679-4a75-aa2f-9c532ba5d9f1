<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
	<Item>
      <modelName>cat745c</modelName>
      <txdName>cat745c</txdName>
      <handlingId>cat745c</handlingId>
      <gameName>cat745c</gameName>
      <vehicleMakeName>CAT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PHANTOM</audioNameHash>
      <layout>LAYOUT_UNKNOWN</layout>
      <coverBoundOffsets>DUMP_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.018000" y="-0.045000" z="-0.043000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.161000" y="0.301000" z="0.531000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="-0.050000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.959000" />
      <wheelScaleRear value="0.959609" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.500000" />
      <envEffScaleMax value="0.700000" />
      <envEffScaleMin2 value="0.500000" />
      <envEffScaleMax2 value="0.700000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
         500.000000
        1000.000000
        2000.000000
        3000.000000
        4000.000000
        5000.000000
      </lodDistances>
      <minSeatHeight value="1.142" />
      <identicalModelSpawnDistance value="50" />
      <maxNumOfSameColor value="2" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2" />
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_PEDS_CAN_STAND_ON_TOP FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_BULKY FLAG_DONT_TIMESLICE_WHEELS FLAG_CANNOT_BE_PICKUP_BY_CARGOBOB</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_INDUSTRIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
		<Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>DUMP_WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>DUMP_WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_DUMP_FRONT_LEFT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>