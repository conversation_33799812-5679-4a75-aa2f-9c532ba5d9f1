<?xml version="1.0" encoding="UTF-8"?>

<CHandlingDataMgr>
<HandlingData>
  <Item type="CHandlingData">
      <handlingName>polmav</handlingName>
      <fMass value="3500.000000" />
      <fInitialDragCoeff value="10.000000" />
      <fPercentSubmerged value="75.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.200000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="1" />
      <fInitialDriveForce value="0.300000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="1.300000" />
      <fClutchChangeRateScaleDownShift value="1.300000" />
      <fInitialDriveMaxFlatVel value="650.000000" />
      <fBrakeForce value="0.400000" />
      <fBrakeBiasFront value="0.600000" />
      <fHandBrakeForce value="0.700000" />
      <fSteeringLock value="35.000000" />
      <fTractionCurveMax value="1.500000" />
      <fTractionCurveMin value="1.200000" />
      <fTractionCurveLateral value="12.000000" />
      <fTractionSpringDeltaMax value="0.100000" />
      <fLowSpeedTractionLossMult value="0.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.500000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="3.500000" />
      <fSuspensionCompDamp value="2.500000" />
      <fSuspensionReboundDamp value="3.000000" />
      <fSuspensionUpperLimit value="0.600000" />
      <fSuspensionLowerLimit value="-0.050000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.150000" />
      <fAntiRollBarForce value="0.000000" />
      <fAntiRollBarBiasFront value="0.000000" />
      <fRollCentreHeightFront value="0.000000" />
      <fRollCentreHeightRear value="0.000000" />
      <fCollisionDamageMult value="1.500000" />
      <fWeaponDamageMult value="0.500000" />
      <fDeformationDamageMult value="3.000000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="100.000000" />
      <fOilVolume value="8.000000" />
      <fSeatOffsetDistX value="-0.150000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="52000" />
      <strModelFlags>2044000</strModelFlags>
      <strHandlingFlags>401000</strHandlingFlags>
      <strDamageFlags>20</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
        <Item type="CFlyingHandlingData">
          <fThrust value="0.530000" />
          <fThrustFallOff value="0.035000" />
          <fThrustVectoring value="0.400000" />
          <fYawMult value="-1.450000" />
          <fYawStabilise value="0.002000" />
          <fSideSlipMult value="0.004000" />
          <fRollMult value="1.850000" />
          <fRollStabilise value="0.010000" />
          <fPitchMult value="1.670000" />
          <fPitchStabilise value="0.001000" />
          <fFormLiftMult value="1.000000" />
          <fAttackLiftMult value="2.950000" />
          <fAttackDiveMult value="2.950000" />
          <fGearDownDragV value="0.100000" />
          <fGearDownLiftMult value="1.000000" />
          <fWindMult value="0.000500" />
          <fMoveRes value="0.035000" />
          <vecTurnRes x="0.800000" y="0.700000" z="0.800000" />
          <vecSpeedRes x="0.500000" y="0.500000" z="0.700000" />
          <fGearDoorFrontOpen value="90.000000" />
          <fGearDoorRearOpen value="90.000000" />
          <fGearDoorRearOpen2 value="90.000000" />
          <fGearDoorRearMOpen value="90.000000" />
          <fTurublenceMagnitudeMax value="0.000000" />
          <fTurublenceForceMulti value="0.000000" />
          <fTurublenceRollTorqueMulti value="0.000000" />
          <fTurublencePitchTorqueMulti value="0.000000" />
          <fBodyDamageControlEffectMult value="0.500000" />
          <fInputSensitivityForDifficulty value="0.300000" />
          <fOnGroundYawBoostSpeedPeak value="1.000000" />
          <fOnGroundYawBoostSpeedCap value="1.000000" />
          <fEngineOffGlideMulti value="1.000000" />
	  <fSubmergeLevelToPullHeliUnderwater value="0.01" />
          <handlingType>HANDLING_TYPE_FLYING</handlingType>
        </Item>
        <Item type="CVehicleWeaponHandlingData">
          <uWeaponHash>
            <Item>VEHICLE_WEAPON_SEARCHLIGHT</Item>
            <Item />
            <Item />
          </uWeaponHash>
          <WeaponSeats content="int_array">
            0 
            0 
            0 
          </WeaponSeats>
          <fTurretSpeed content="float_array">
            1.000000	
            0.000000	
          </fTurretSpeed>
          <fTurretPitchMin content="float_array">
            0.000000	
            0.000000	
          </fTurretPitchMin>
          <fTurretPitchMax content="float_array">
            0.000000	
            0.000000	
          </fTurretPitchMax>
          <fTurretCamPitchMin content="float_array">
            0.000000	
            0.000000	
          </fTurretCamPitchMin>
          <fTurretCamPitchMax content="float_array">
            0.000000	
            0.000000	
          </fTurretCamPitchMax>
          <fBulletVelocityForGravity content="float_array">
            0.000000	
            0.000000	
          </fBulletVelocityForGravity>
          <fTurretPitchForwardMin content="float_array">
            0.000000	
            0.000000	
          </fTurretPitchForwardMin>
          <fUvAnimationMult value="0.000000" />
          <fMiscGadgetVar value="0.000000" />
          <fWheelImpactOffset value="0.000000" />
        </Item>
        <Item type="NULL" />
      </SubHandlingData>
    </Item>
  </HandlingData>
</CHandlingDataMgr>
