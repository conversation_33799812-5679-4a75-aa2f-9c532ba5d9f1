<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />

  <InitDatas>
    <Item>
      <modelName>polmav</modelName>
      <txdName>polmav</txdName>
      <handlingId>polmav</handlingId>
      <gameName>POLMAV</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_HELI</layout>
      <coverBoundOffsets>POLMAV_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_MAVERICK_CAMERA</cameraName>
      <aimCameraName>MAVERICK_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>HELI_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.063000" z="-0.033000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.121000" y="0.350000" z="0.508000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.296000" z="0.410000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="-0.519000" y="-0.133000" z="0.558000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.547000" y="-0.359000" z="0.468000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="0.005000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.170000" />
      <wheelScaleRear value="0.075000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        100.000000
        200.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="1.057" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.350000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_DRIVER_NO_DRIVE_BY FLAG_NO_RESPRAY FLAG_ALLOWS_RAPPEL FLAG_DONT_SPAWN_IN_CARGEN FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_DONT_SPAWN_AS_AMBIENT FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_MAVERICK</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_Y_SWAT_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_SNIPERRIFLE</Item>
        <Item>REWARD_AMMO_SNIPERRIFLE</Item>
        <Item>REWARD_STAT_WEAPON</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>HELI_FRONT_RIGHT</Item>
        <Item>HELI_LEFT_SIDE_PASSENGER</Item>
        <Item>HELI_RIGHT_SIDE_PASSENGER</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>

  <txdRelationships>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>kj2000</child>
    </Item>
  </txdRelationships>

</CVehicleModelInfo__InitDataList>
