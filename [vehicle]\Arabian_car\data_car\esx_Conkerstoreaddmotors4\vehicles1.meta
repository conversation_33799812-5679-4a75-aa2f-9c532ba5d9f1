<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
	<residentTxd>vehshare</residentTxd>
	<residentAnims />
	<InitDatas>
	<Item>
			<modelName>flhxs_streetglide_special18</modelName>
			<txdName>flhxs_streetglide_special18</txdName>
			<handlingId>FLHXS_STREETGLIDE_SPECIAL18</handlingId>
			<gameName>FLHXS_STREETGLIDE_SPECIAL18</gameName>
			<vehicleMakeName>HARLEYDAVIDSON</vehicleMakeName>
			<expressionDictName>null</expressionDictName>
			<expressionName>null</expressionName>
			<animConvRoofDictName>null</animConvRoofDictName>
			<animConvRoofName>null</animConvRoofName>
			<animConvRoofWindowsAffected/>
			<ptfxAssetName>null</ptfxAssetName>
			<audioNameHash>BAGGER</audioNameHash>
			<layout>LAYOUT_BIKE_FREEWAY</layout>
			<coverBoundOffsets>BIKE_COVER_OFFSET_INFO</coverBoundOffsets>
			<POVTuningInfo>BAGGER_POV_TUNING</POVTuningInfo>
			<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
			<scenarioLayout/>
			<cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
			<aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
			<bonnetCameraName>BIKE_BAGGER_POV_CAMERA</bonnetCameraName>
			<povCameraName>BIKE_BAGGER_POV_CAMERA</povCameraName>
			<FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
			<FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="=0.030000"/>
			<FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonVisorSwitchIKOffset x="-0.025000" y="0.035000" z="-0.0300000"/>
			<FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
			<FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
			<PovCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
			<PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
			<PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
			<PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
			<vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
			<shouldUseCinematicViewMode value="true"/>
			<shouldCameraTransitionOnClimbUpDown value="false"/>
			<shouldCameraIgnoreExiting value="false"/>
			<AllowPretendOccupants value="false"/>
			<AllowJoyriding value="true"/>
			<AllowSundayDriving value="true"/>
			<AllowBodyColorMapping value="true"/>
			<wheelScale value="0.257300"/>
			<wheelScaleRear value="0.257300"/>
			<dirtLevelMin value="0.000000"/>
			<dirtLevelMax value="0.850000"/>
			<envEffScaleMin value="0.000000"/>
			<envEffScaleMax value="1.000000"/>
			<envEffScaleMin2 value="0.000000"/>
			<envEffScaleMax2 value="1.000000"/>
			<damageMapScale value="0.600000"/>
			<damageOffsetScale value="1.000000"/>
			<diffuseTint value="0x00FFFFFF"/>
			<steerWheelMult value="0.350000"/>
			<HDTextureDist value="5.000000"/>
			<lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
			<minSeatHeight value="10"/>
			<identicalModelSpawnDistance value="80"/>
			<maxNumOfSameColor value="10"/>
			<defaultBodyHealth value="1000.000000"/>
			<pretendOccupantsScale value="1.000000"/>
			<visibleSpawnDistScale value="1.000000"/>
			<trackerPathWidth value="2.000000"/>
			<weaponForceMult value="1.000000"/>
			<frequency value="30"/>
			<swankness>SWANKNESS_1</swankness>
			<maxNum value="3"/>
			<flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_BIKE_CLAMP_PICKUP_LEAN_RATE</flags>
			<type>VEHICLE_TYPE_BIKE</type>
			<plateType>VPT_BACK_PLATES</plateType>
			<dashboardType>VDT_ZTYPE</dashboardType>
			<vehicleClass>VC_MOTORCYCLE</vehicleClass>
			<wheelType>VWT_BIKE</wheelType>
			<trailers/>
			<additionalTrailers/>
			<drivers/>
			<extraIncludes/>
			<doorsWithCollisionWhenClosed/>
			<driveableDoors/>
			<bumpersNeedToCollideWithMap value="false"/>
			<needsRopeTexture value="false"/>
			<requiredExtras>EXTRA_1 EXTRA_2 EXTRA_3</requiredExtras>
			<rewards/>
			<cinematicPartCamera>
				<Item>WHEEL_REAR_LEFT_CAMERA</Item>
				<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
			</cinematicPartCamera>
			<NmBraceOverrideSet>Bike</NmBraceOverrideSet>
			<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
			<buoyancySphereSizeScale value="1.000000"/>
			<pOverrideRagdollThreshold type="NULL"/>
			<firstPersonDrivebyData>
				<Item>BIKE_BAGGER_FRONT</Item>
				<Item>BIKE_BAGGER_REAR</Item>
			</firstPersonDrivebyData>
		</Item>
	</InitDatas>
	<txdRelationships>
   
	</txdRelationships>
</CVehicleModelInfo__InitDataList>