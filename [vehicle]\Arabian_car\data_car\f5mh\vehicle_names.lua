function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHash<PERSON><PERSON>("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()

	AddTextEntry('745le' ,"Bmw - 245 Li")
	AddTextEntry('bbentayga' ," بنتلي بنتياق - bentayga 2017")
	AddTextEntry('ben17' ," بنتلي سبورت - ben 2017 ")
	AddTextEntry('bfs14' ," بنتلي قوست - Ghost 2014 ")
	AddTextEntry('binkshf' ,"  بنتلي سبورت كشف - ben 2019 ")
	AddTextEntry('bmm' ,"  بنتلي قوست - Ghost 2013 ")
	AddTextEntry('cullinan' ,"  روز كولينان - Roz cullinan 2018 ")
	AddTextEntry('dawnonyx' ," روز داون اونكس - Dawn Onyx 2019 ")
	AddTextEntry('genesisg90' ," جينيسيس موتورز - GENESIS G90 ")
	AddTextEntry('ghostewb1' ," روز قوست - Roz Ghost 2020 ")
	AddTextEntry('s500w222' ," مرسيدس سي - Mercedes C500 ")
	AddTextEntry('wraithb' ," روز راذ - Roz Wraith ")
	AddTextEntry('wraithb' ," روز راذ - Roz Wraith ")

end)