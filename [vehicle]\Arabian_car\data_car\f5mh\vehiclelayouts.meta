﻿<?xml version="1.0" encoding="utf-8"?>
<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>HUNTLEY_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="-0.150000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="-0.180000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>MASSACRO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.040000" />
      <ExtraForwardOffset value="-0.100000" />
      <ExtraBackwardOffset value="-0.060000" />
      <ExtraZOffset value="-0.040000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ZENTORNO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.110000" />
      <ExtraForwardOffset value="-0.100000" />
      <ExtraBackwardOffset value="-0.050000" />
      <ExtraZOffset value="0.175000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>THRUST_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.120000" />
      <ExtraForwardOffset value="-0.100000" />
      <ExtraBackwardOffset value="-0.150000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DOMINATOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.100000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="0.000000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>rhd_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="-0.050000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>720spider_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="0.050000" />
      <ExtraBackwardOffset value="-0.300000" />
      <ExtraZOffset value="-0.200000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos />
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos />
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_HUNTLEY_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.500000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="ZTYPE_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_rhd_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_rhd_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink>SEAT_STANDARD_REAR_LEFT</RearSeatLink>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000" />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_rhd_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_rhd_FRONT_LEFT</ShuffleLink>
      <RearSeatLink>SEAT_STANDARD_REAR_RIGHT</RearSeatLink>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000" />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_HUNTLEY_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_HUNTLEY_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_rhd_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@std@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_rhd_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_rhd_FRONT_LEFT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_rhd_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_Dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_rhd_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.551000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForWaterEntry value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_rhd_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>Standard</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_rhd_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.551700" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForWaterEntry value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_rhd_FRONT_RIGHT" />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_720spider_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753300" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="-0.100000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_720spider_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.740000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="-0.100000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_HUNTLEY</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HUNTLEY_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseFinerAlignTolerance UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.00000" />
      <BodyLeanXApproachSpeed value="5.00000" />
      <BodyLeanXSmallDelta value="0.30000" />
      <LookBackApproachSpeedScale value="1.00000" />
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_rhd</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_rhd_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_rhd_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_rhd_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_rhd_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_rhd_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_rhd_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.00000" />
      <BodyLeanXApproachSpeed value="5.00000" />
      <BodyLeanXSmallDelta value="0.30000" />
      <LookBackApproachSpeedScale value="1.00000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_720spider</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_720spider_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_720spider_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk DisableJackingAndBusting UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BIKE_THRUST_FRONT</Name>
      <HeadingLimits x="-150.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="100.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="-0.075000" />
            <AngleToBlendInOffset x="100.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="35.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="25.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.110000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.145000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.280000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_720spider_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-100.000000" y="100.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000" />
            <AngleToBlendInOffset x="30.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.175000" />
            <AngleToBlendInOffset x="10.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_720spider_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-100.000000" y="100.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="75.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000" />
            <AngleToBlendInOffset x="30.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.175000" />
            <AngleToBlendInOffset x="10.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>