﻿<?xml version="1.0" encoding="utf-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vershare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>745LE</modelName>
      <txdName>745LE</txdName>
      <handlingId>745LE</handlingId>
      <gameName>745LE</gameName>
      <vehicleMakeName>745LE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ORACLE</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>ORACLE2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.125000" z="-0.035000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.125000" z="-0.050000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.030000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.030000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.010000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.213000" z="0.511000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.086000" z="0.435000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.086000" z="0.435000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.020000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.294000" />
      <wheelScaleRear value="0.294000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.867" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ORACLE2_FRONT_LEFT</Item>
        <Item>STD_ORACLE2_FRONT_RIGHT</Item>
        <Item>STD_ORACLE2_REAR_LEFT</Item>
        <Item>STD_ORACLE2_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>bbentayga</modelName>
      <txdName>bbentayga</txdName>
      <handlingId>bbentayga</handlingId>
      <gameName>bbentayga</gameName>
      <vehicleMakeName>Bentley Bentayga</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>huntley</audioNameHash>
      <layout>LAYOUT_STD_HUNTLEY</layout>
      <coverBoundOffsets>HUNTLEY_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.028000" y="-0.128000" z="-0.035000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.045000" y="-0.128000" z="-0.035000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.035000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.143000" y="0.241000" z="0.408000" />
      <FirstPersonMobilePhoneOffset x="0.143000" y="0.241000" z="0.408000" />
      <PovCameraOffset x="0.000000" y="-0.235000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322500" />
      <wheelScaleRear value="0.322500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.600000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
      450.000000    
      460.000000    
      470.000000    
      480.000000    
      500.000000    
      500.000000    
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <dashboardType>VDT_FEROCI</dashboardType>
      <plateType>VPT_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ASTEROPE_FRONT_LEFT</Item>
        <Item>STD_ASTEROPE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ben17</modelName>
      <txdName>ben17</txdName>
      <handlingId>ben17</handlingId>
      <gameName>ben17</gameName>
      <vehicleMakeName>BENTLEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>va_cogcabrio</animConvRoofDictName>
      <animConvRoofName>roof</animConvRoofName>
      <animConvRoofWindowsAffected>
        <Item>VEH_EXT_WINDOW_LF</Item>
        <Item>VEH_EXT_WINDOW_RF</Item>
        <Item>VEH_EXT_WINDOW_LR</Item>
        <Item>VEH_EXT_WINDOW_RR</Item>
      </animConvRoofWindowsAffected>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>COGCABRIO</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COGCABRIO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.080000" z="-0.035000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.080000" z="-0.035000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.123000" y="0.218000" z="0.541000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.081000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.286500" />
      <wheelScaleRear value="0.286500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.791" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="0.800000" />
      <frequency value="50" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPORTS FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>bfs14</modelName>
      <txdName>bfs14</txdName>
      <handlingId>bfs14</handlingId>
      <gameName>bfs14</gameName>
      <vehicleMakeName>Bentley Flying Spur 2014</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FUGITIVE</audioNameHash>
      <layout>LAYOUT_STD_LOWROOF</layout>
      <coverBoundOffsets>BFS14_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.090000" z="-0.055000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.090000" z="-0.055000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.050000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.128000" y="0.231000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.425000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.425000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.275000" z="0.600500" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.010000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="-0.010000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.294000" />
      <wheelScaleRear value="0.294000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
      450.000000    
      460.000000    
      470.000000    
      480.000000    
      500.000000    
      500.000000 
      </lodDistances>
      <minSeatHeight value="0.819" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BFS14_FRONT_LEFT</Item>
        <Item>STD_BFS14_FRONT_RIGHT</Item>
        <Item>STD_BFS14_REAR_LEFT</Item>
        <Item>STD_BFS14_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>binkshf</modelName>
      <txdName>binkshf</txdName>
      <handlingId>binkshf</handlingId>
      <gameName>binkshf</gameName>
      <vehicleMakeName>BENTLEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>va_cogcabrio</animConvRoofDictName>
      <animConvRoofName>roof</animConvRoofName>
      <animConvRoofWindowsAffected>
        <Item>VEH_EXT_WINDOW_LF</Item>
        <Item>VEH_EXT_WINDOW_RF</Item>
        <Item>VEH_EXT_WINDOW_LR</Item>
        <Item>VEH_EXT_WINDOW_RR</Item>
      </animConvRoofWindowsAffected>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>COGCABRIO</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COGCABRIO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.080000" z="-0.035000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.080000" z="-0.035000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.123000" y="0.218000" z="0.541000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.081000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.286500" />
      <wheelScaleRear value="0.286500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.791" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="0.800000" />
      <frequency value="50" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPORTS FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>bmm</modelName>
      <txdName>bmm</txdName>
      <handlingId>bmm</handlingId>
      <gameName>bmm</gameName>
      <vehicleMakeName>BENTLEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>COGNOSCENTI2</audioNameHash>
      <layout>LAYOUT_STD_HABANERO</layout>
      <coverBoundOffsets>SUPERD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.080000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.055000" y="-0.080000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="-0.095000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.095000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.055000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.050000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.253000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.135000" z="0.485000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.176000" y="0.124000" z="0.460000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.176000" y="0.124000" z="0.460000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.700000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.050000" z="-0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.247500" />
      <wheelScaleRear value="0.247500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.925" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="80" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="3" />
      <flags>FLAG_EXTRAS_REQUIRE FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_INCREASED_RAMMING_FORCE FLAG_HAS_CAPPED_EXPLOSION_DAMAGE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_SCHAFTER5_FRONT_LEFT</Item>
        <Item>STD_SCHAFTER5_FRONT_RIGHT</Item>
        <Item>STD_COGNOSCENTI_REAR_LEFT</Item>
        <Item>STD_COGNOSCENTI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>cullinan</modelName>
      <txdName>cullinan</txdName>
      <handlingId>cullinan</handlingId>
      <gameName>cullinan</gameName>
      <vehicleMakeName>ROLLSROYCE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SUPERD</audioNameHash>
      <layout>LAYOUT_STD_HIGHWINDOW</layout>
      <coverBoundOffsets>RADI_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.045000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.075000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.075000" z="-0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.045000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.243000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.213000" z="0.495000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.075000" z="0.050000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.287100" />
      <wheelScaleRear value="0.287100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        35.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.896" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO_FRONT_LEFT</Item>
        <Item>STD_BUFFALO_FRONT_RIGHT</Item>
        <Item>STD_DILETTANTE_REAR_LEFT</Item>
        <Item>STD_DILETTANTE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>dawnonyx</modelName>
      <txdName>dawnonyx</txdName>
      <handlingId>dawnonyx</handlingId>
      <gameName>dawnonyx</gameName>
      <vehicleMakeName>RR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>superd</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>WINDSOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.208000" z="0.546000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.158000" y="0.135000" z="0.460000" />
      <PovCameraOffset x="0.000000" y="-0.160000" z="0.560000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.309700" />
      <wheelScaleRear value="0.309700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="0.600000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        350.000000
        350.000000
        350.000000
        350.000000
        350.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.925" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="80" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="3" />
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_HAS_LIVERY FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_BULLET_RESISTANT_GLASS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_TAILGATER_FRONT_LEFT</Item>
        <Item>STD_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>genesisg90</modelName>
      <txdName>genesisg90</txdName>
      <handlingId>genesisg90</handlingId>
      <gameName>genesisg90</gameName>
      <vehicleMakeName>GENESISG90</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>superd</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>FELON_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_RAPIDGT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.043000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.055000" z="0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.055000" z="0.030000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.048000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.176000" y="0.113000" z="0.415000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.140000" z="0.545000" />
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.570000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="-0.035000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.100000" z="-0.035000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.290000" />
      <wheelScaleRear value="0.290000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.200000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.81" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FELON_FRONT_LEFT</Item>
        <Item>STD_FELON_FRONT_RIGHT</Item>
        <Item>STD_FELON_REAR_LEFT</Item>
        <Item>STD_FELON_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ghostewb1</modelName>
      <txdName>ghostewb1</txdName>
      <handlingId>ghostewb1</handlingId>
      <gameName>ghostewb1</gameName>
      <vehicleMakeName>Rolls Royce</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SUPERD</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>GHOSTEWB_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.030000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.030000" z="0.020000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.285000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.145000" z="0.435000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.455000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.455000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.620000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="-0.015000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="-0.015000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.282600" />
      <wheelScaleRear value="0.282600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="0.800000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
      450.000000    
      460.000000    
      470.000000    
      480.000000    
      500.000000    
      500.000000 
      </lodDistances>
      <minSeatHeight value="0.862" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_SULTAN</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <maxSteeringWheelAngle value="109.0" />
      <firstPersonDrivebyData>
        <Item>STD_TAILGATER_FRONT_LEFT</Item>
        <Item>STD_FRONT_RIGHT</Item>
        <Item>STD_TAILGATER_REAR_LEFT</Item>
        <Item>STD_TAILGATER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>s500w222</modelName>
      <txdName>s500w222</txdName>
      <handlingId>s500w222</handlingId>
      <gameName>s500w222</gameName>
      <vehicleMakeName>MERC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SCHAFTER2</audioNameHash>
      <layout>LAYOUT_STD_WARRENER</layout>
      <coverBoundOffsets>WARRENER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.045000" z="0.048000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.063000" y="-0.080000" z="-0.005000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.015000" y="-0.050000" z="-0.030000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.153000" y="0.180000" z="0.415000" />
      <FirstPersonMobilePhoneOffset x="0.156000" y="0.255000" z="0.510000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.153000" y="0.088000" z="0.418000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.153000" y="0.088000" z="0.418000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.185000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.215000" />
      <wheelScaleRear value="0.215000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        350.000000
        350.000000
        350.000000
        350.000000
        350.000000
        350.000000
      </lodDistances>
      <minSeatHeight value="0.846" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="4" />
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_BULLET_RESISTANT_GLASS </flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO2_FRONT_LEFT</Item>
        <Item>STD_BUFFALO2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>wraithb</modelName>
      <txdName>wraithb</txdName>
      <handlingId>wraithb</handlingId>
      <gameName>wraithb</gameName>
      <vehicleMakeName>ROLLSROYCE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>RADI</audioNameHash>
      <layout>LAYOUT_STD_HIGHWINDOW</layout>
      <coverBoundOffsets>RADI_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.045000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.075000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.075000" z="-0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.045000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.243000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.213000" z="0.495000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.075000" z="0.050000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.287100" />
      <wheelScaleRear value="0.287100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        35.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.896" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO_FRONT_LEFT</Item>
        <Item>STD_BUFFALO_FRONT_RIGHT</Item>
        <Item>STD_DILETTANTE_REAR_LEFT</Item>
        <Item>STD_DILETTANTE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_bob_brown_interior</parent>
      <child>745le</child>
    </Item>
    <Item>
      <parent>vehicles_feroci_interior</parent>
      <child>bbentayga</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>ben17</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>bfs14</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>binkshf</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_w2_interior</parent>
      <child>bmm</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>cullinan</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>dawnonyx</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>genesisg90</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>ghostewb1</child>
    </Item>
    <Item>
      <parent>vehicles_monroe_interior</parent>
      <child>s500w222</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>wraithb</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>