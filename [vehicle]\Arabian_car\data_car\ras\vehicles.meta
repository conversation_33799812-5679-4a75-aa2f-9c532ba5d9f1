<?xml version="1.0" encoding="utf-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vershare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>actros</modelName>
      <txdName>actros</txdName>
      <handlingId>ACTROS</handlingId>
      <gameName>ACTROS</gameName>
      <vehicleMakeName>JOBUILT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PHANTOM</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.030000" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.370000" z="0.433000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.420000" />
      <PovCameraOffset x="0.000000" y="-0.075000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="-0.035000" y="0.000000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.035000" y="0.000000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.353200" />
      <wheelScaleRear value="0.318500" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="60" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_USE_STRICTER_EXIT_COLLISION_TESTS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>semihauler</Item>
        <Item>lowboy</Item>
        <Item>lowboyjeep</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
        <Item>eagerbeaver</Item>
        <Item>botdumptr</Item>
        <Item>boxlongtr</Item>
        <Item>docktrailer2</Item>
        <Item>drybulktr</Item>
        <Item>dumptr</Item>
        <Item>fueltr</Item>
        <Item>gastr</Item>
        <Item>trailerlogs2</Item>
        <Item>trailerswb</Item>
        <Item>trailerswb2</Item>
        <Item>trflat2</Item>
        <Item>trailer01</Item>
        <Item>trailer02</Item>
        <Item>trailer03</Item>
        <Item>trailer04</Item>
        <Item>trailer05</Item>
      </trailers>
      <additionalTrailers>
        <Item>armytanker</Item>
        <Item>armytrailer</Item>
        <Item>tr4</Item>
        <Item>tvtrailer</Item>
      </additionalTrailers>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="-0.600000" />
      <buoyancySphereSizeScale value="0.800000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>m3e30</modelName>
      <txdName>m3e30</txdName>
      <handlingId>m3e30</handlingId>
      <gameName>m3e30</gameName>
      <vehicleMakeName>BMW</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SENTINEL3</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>STINGER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_HIGH</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0" y="-0.055000" z="0" />
      <FirstPersonDriveByUnarmedIKOffset x="0" y="-0.1" z="0" />
      <FirstPersonProjectileDriveByIKOffset x="0" y="-0.065000" z="-0.085000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.05" y="-0.065000" z="-0.085000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerIKOffset x="0" y="-0.055000" z="0" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0" y="-0.1" z="0" />
      <FirstPersonMobilePhoneOffset x="0.128000" y="0.19" z="0.498000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.148000" z="0.418000" />
      <PovCameraOffset x="0" y="-0.21" z="0.6" />
      <PovCameraVerticalAdjustmentForRollCage value="0" />
      <PovPassengerCameraOffset x="0" y="0" z="-0.025000" />
      <PovRearPassengerCameraOffset x="0" y="0" z="-0.025000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.23000" />
      <wheelScaleRear value="0.35000" />
      <dirtLevelMin value="0" />
      <dirtLevelMax value="0.3" />
      <envEffScaleMin value="0" />
      <envEffScaleMax value="1" />
      <envEffScaleMin2 value="0" />
      <envEffScaleMax2 value="1" />
      <damageMapScale value="0.6" />
      <damageOffsetScale value="1" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1" />
      <HDTextureDist value="5" />
      <lodDistances content="float_array">
        15
        30
        50
        100
        500
        500
      </lodDistances>
      <minSeatHeight value="0.797" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000" />
      <pretendOccupantsScale value="1" />
      <visibleSpawnDistScale value="1" />
      <trackerPathWidth value="2" />
      <weaponForceMult value="1" />
      <frequency value="1" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="2" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_EXTRAS_ALL FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0" y="0" z="0" />
      <buoyancySphereSizeScale value="1" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_STINGER_FRONT_LEFT</Item>
        <Item>LOW_STINGER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mack53</modelName>
      <txdName>mack53</txdName>
      <handlingId>mack53</handlingId>
      <gameName>mack53</gameName>
      <vehicleMakeName>mack</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>hauler2</audioNameHash>
      <layout>LAYOUT_RIOT_VAN</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.03" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.37" z="0.433000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.42" />
      <PovCameraOffset x="0" y="-0.0" z="0.6" />
      <PovCameraVerticalAdjustmentForRollCage value="0" />
      <PovPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <PovRearPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.36" />
      <wheelScaleRear value="0.36" />
      <dirtLevelMin value="0.3" />
      <dirtLevelMax value="1" />
      <envEffScaleMin value="0" />
      <envEffScaleMax value="1" />
      <envEffScaleMin2 value="0" />
      <envEffScaleMax2 value="1" />
      <damageMapScale value="0.5" />
      <damageOffsetScale value="0.9" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1" />
      <HDTextureDist value="5" />
      <lodDistances content="float_array">
        25	
        50	
        100	
        200	
        750	
        750
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="100" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000" />
      <pretendOccupantsScale value="1" />
      <visibleSpawnDistScale value="1" />
      <trackerPathWidth value="2" />
      <weaponForceMult value="1" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_HAS_TWO_BONNET_BONES FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_USE_STRICTER_EXIT_COLLISION_TESTS FLAG_DONT_HOLD_LOW_GEARS_WHEN_ENGINE_UNDER_LOAD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>semihauler</Item>
        <Item>lowboy</Item>
        <Item>lowboyjeep</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
        <Item>eagerbeaver</Item>
        <Item>botdumptr</Item>
        <Item>boxlongtr</Item>
        <Item>docktrailer2</Item>
        <Item>drybulktr</Item>
        <Item>dumptr</Item>
        <Item>fueltr</Item>
        <Item>gastr</Item>
        <Item>trailerlogs2</Item>
        <Item>trailerswb</Item>
        <Item>trailerswb2</Item>
        <Item>trflat2</Item>
        <Item>trailer01</Item>
        <Item>trailer02</Item>
        <Item>trailer03</Item>
        <Item>trailer04</Item>
        <Item>trailer05</Item>
      </trailers>
      <additionalTrailers>
        <Item>tr4</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
      </additionalTrailers>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0" y="0" z="-0.6" />
      <buoyancySphereSizeScale value="0.8" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mack53d</modelName>
      <txdName>mack53</txdName>
      <handlingId>mack53d</handlingId>
      <gameName>mack53d</gameName>
      <vehicleMakeName>mack</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>hauler2</audioNameHash>
      <layout>LAYOUT_RIOT_VAN</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.03" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.37" z="0.433000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.42" />
      <PovCameraOffset x="0" y="-0.0" z="0.6" />
      <PovCameraVerticalAdjustmentForRollCage value="0" />
      <PovPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <PovRearPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.36" />
      <wheelScaleRear value="0.36" />
      <dirtLevelMin value="0.3" />
      <dirtLevelMax value="1" />
      <envEffScaleMin value="0" />
      <envEffScaleMax value="1" />
      <envEffScaleMin2 value="0" />
      <envEffScaleMax2 value="1" />
      <damageMapScale value="0.5" />
      <damageOffsetScale value="0.9" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1" />
      <HDTextureDist value="5" />
      <lodDistances content="float_array">
        25	
        50	
        100	
        200	
        750	
        750
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="100" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000" />
      <pretendOccupantsScale value="1" />
      <visibleSpawnDistScale value="1" />
      <trackerPathWidth value="2" />
      <weaponForceMult value="1" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_HAS_TWO_BONNET_BONES FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_USE_STRICTER_EXIT_COLLISION_TESTS FLAG_DONT_HOLD_LOW_GEARS_WHEN_ENGINE_UNDER_LOAD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras>
        <Item>EXTRA_3 EXTRA_4</Item>
      </requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0" y="0" z="-0.6" />
      <buoyancySphereSizeScale value="0.8" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mack53t</modelName>
      <txdName>mack53</txdName>
      <handlingId>mack53t</handlingId>
      <gameName>mack53t</gameName>
      <vehicleMakeName>mack</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>hauler2</audioNameHash>
      <layout>LAYOUT_RIOT_VAN</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.03" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.37" z="0.433000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.42" />
      <PovCameraOffset x="0" y="-0.0" z="0.6" />
      <PovCameraVerticalAdjustmentForRollCage value="0" />
      <PovPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <PovRearPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.36" />
      <wheelScaleRear value="0.36" />
      <dirtLevelMin value="0.3" />
      <dirtLevelMax value="1" />
      <envEffScaleMin value="0" />
      <envEffScaleMax value="1" />
      <envEffScaleMin2 value="0" />
      <envEffScaleMax2 value="1" />
      <damageMapScale value="0.5" />
      <damageOffsetScale value="0.9" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1" />
      <HDTextureDist value="5" />
      <lodDistances content="float_array">
        25	
        50	
        100	
        200	
        750	
        750
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="100" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000" />
      <pretendOccupantsScale value="1" />
      <visibleSpawnDistScale value="1" />
      <trackerPathWidth value="2" />
      <weaponForceMult value="1" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_HAS_TWO_BONNET_BONES FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_USE_STRICTER_EXIT_COLLISION_TESTS FLAG_DONT_HOLD_LOW_GEARS_WHEN_ENGINE_UNDER_LOAD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>semihauler</Item>
        <Item>lowboy</Item>
        <Item>lowboyjeep</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
        <Item>eagerbeaver</Item>
        <Item>botdumptr</Item>
        <Item>boxlongtr</Item>
        <Item>docktrailer2</Item>
        <Item>drybulktr</Item>
        <Item>dumptr</Item>
        <Item>fueltr</Item>
        <Item>gastr</Item>
        <Item>trailerlogs2</Item>
        <Item>trailerswb</Item>
        <Item>trailerswb2</Item>
        <Item>trflat2</Item>
        <Item>trailer01</Item>
        <Item>trailer02</Item>
        <Item>trailer03</Item>
        <Item>trailer04</Item>
        <Item>trailer05</Item>
      </trailers>
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras>
        <Item>EXTRA_3 EXTRA_4</Item>
      </requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0" y="0" z="-0.6" />
      <buoyancySphereSizeScale value="0.8" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ras3</modelName>
      <txdName>ras3</txdName>
      <handlingId>ras3</handlingId>
      <gameName>ras3</gameName>
      <vehicleMakeName>peterbilt</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>phantom</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.03" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0" y="0" z="0" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0" y="0" z="0" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.37" z="0.433000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.42" />
      <PovCameraOffset x="0" y="-0.005000" z="0.58" />
      <PovCameraVerticalAdjustmentForRollCage value="0" />
      <PovPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <PovRearPassengerCameraOffset x="-0.035000" y="0" z="0.07" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.35" />
      <wheelScaleRear value="0.3455" />
      <dirtLevelMin value="0.3" />
      <dirtLevelMax value="1" />
      <envEffScaleMin value="0" />
      <envEffScaleMax value="1" />
      <envEffScaleMin2 value="0" />
      <envEffScaleMax2 value="1" />
      <damageMapScale value="0.6" />
      <damageOffsetScale value="1" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1" />
      <HDTextureDist value="5" />
      <lodDistances content="float_array">
        25	
        50	
        100	
        200	
        750	
        750
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000" />
      <pretendOccupantsScale value="1" />
      <visibleSpawnDistScale value="1" />
      <trackerPathWidth value="2" />
      <weaponForceMult value="1" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_USE_STRICTER_EXIT_COLLISION_TESTS FLAG_DONT_HOLD_LOW_GEARS_WHEN_ENGINE_UNDER_LOAD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>semihauler</Item>
        <Item>lowboy</Item>
        <Item>lowboyjeep</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
        <Item>eagerbeaver</Item>
        <Item>botdumptr</Item>
        <Item>boxlongtr</Item>
        <Item>docktrailer2</Item>
        <Item>drybulktr</Item>
        <Item>dumptr</Item>
        <Item>fueltr</Item>
        <Item>gastr</Item>
        <Item>trailerlogs2</Item>
        <Item>trailerswb</Item>
        <Item>trailerswb2</Item>
        <Item>trflat2</Item>
        <Item>trailer01</Item>
        <Item>trailer02</Item>
        <Item>trailer03</Item>
        <Item>trailer04</Item>
        <Item>trailer05</Item>
      </trailers>
      <additionalTrailers>
        <Item>tr4</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
      </additionalTrailers>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0" y="0" z="-0.6" />
      <buoyancySphereSizeScale value="0.8" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vnl780</modelName>
      <txdName>vnl780</txdName>
      <handlingId>vnl780</handlingId>
      <gameName>vnl780</gameName>
      <vehicleMakeName>VOLVO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>phantom</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="-0.145000" z="0.015000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.370000" z="0.490000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.420000" />
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.59000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="-0.035000" y="0.000000" z="0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.353200" />
      <wheelScaleRear value="0.318500" />
      <dirtLevelMin value="0.250000" />
      <dirtLevelMax value="0.330000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        40.000000	
        85.000000	
        600.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="1.182" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="60" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_USE_STRICTER_EXIT_COLLISION_TESTS FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>semihauler</Item>
        <Item>lowboy</Item>
        <Item>lowboyjeep</Item>
        <Item>docktrailer</Item>
        <Item>trailers</Item>
        <Item>trailers2</Item>
        <Item>trailers3</Item>
        <Item>tanker</Item>
        <Item>trailerlogs</Item>
        <Item>tr2</Item>
        <Item>trflat</Item>
        <Item>eagerbeaver</Item>
        <Item>botdumptr</Item>
        <Item>boxlongtr</Item>
        <Item>docktrailer2</Item>
        <Item>drybulktr</Item>
        <Item>dumptr</Item>
        <Item>fueltr</Item>
        <Item>gastr</Item>
        <Item>trailerlogs2</Item>
        <Item>trailerswb</Item>
        <Item>trailerswb2</Item>
        <Item>trflat2</Item>
        <Item>trailer01</Item>
        <Item>trailer02</Item>
        <Item>trailer03</Item>
        <Item>trailer04</Item>
        <Item>trailer05</Item>
      </trailers>
      <additionalTrailers>
        <Item>armytanker</Item>
        <Item>armytrailer</Item>
        <Item>tr4</Item>
        <Item>tvtrailer</Item>
      </additionalTrailers>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes>EXTRA_1</extraIncludes>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="-0.600000" />
      <buoyancySphereSizeScale value="0.800000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_PACKER_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare_truck</parent>
      <child>actros</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_interior</parent>
      <child>m3e30</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>mack53</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>pete351</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>vnl780</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>