function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHashKey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()

	AddTextEntry('avalon', 'تويوتا افالون - Toyota Avalon')
	AddTextEntry('Camry11', 'تويوتا كامري - Toyota Camry')
	AddTextEntry('caprice13', 'كابرس - Caprice LS 2013')
	AddTextEntry('caprice17', 'كابرس - Caprice LS 2017')
	AddTextEntry('gcmaccent15', 'هونداي اكسينت - Hyundai Accent 2012')
	AddTextEntry('gcmpassat12', 'فولكس واجن - Volkswagen 2012')
	AddTextEntry('lex350', 'لكزس جي اس - Lexus GS 350')
	AddTextEntry('lex500', 'لكزس جي اس - Lexus GS 500')
	AddTextEntry('optima', 'كيا اوبتيما - Kia Optima 2015')
	AddTextEntry('soso18', 'هونداي سوناتا - Sonata Hyundai 2018')
	AddTextEntry('towncar91', 'تاون لنكون - Town Lingcoln 1995')
	AddTextEntry('towncar2010', 'تاون لنكون - Town Lingcoln 2010')

end)