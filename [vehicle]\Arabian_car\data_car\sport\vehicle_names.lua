function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHash<PERSON><PERSON>("ADD_TEXT_ENTRY"), key, value)
end


Citizen.CreateThread(function()


	AddTextEntry('13fmb302' ,"موستنق بوس - 2013")
	AddTextEntry('16challenger' ,"دوج تشالنجر - 2016")
	AddTextEntry('16charger2' ,"دوج تشارجر - SRT")
	AddTextEntry('16ss' ," كمارو SS 2017")
	AddTextEntry('19dbs' ," استون مارتن ")
	AddTextEntry('amggt63s' ," مرسيدس amg 63 ")
	AddTextEntry('560sec87' ,"  مرسيدس كوبية - SEC 560 ")
	AddTextEntry('c7' ,"  كورفيت C7R ")
	AddTextEntry('cls63s' ,"  مرسيدس - s63 ")
	AddTextEntry('ctsv16' ," كديلاك - 16 ")
	AddTextEntry('demon' ,"  دوج تشالنجر - DEMON ")
	AddTextEntry('dtd_c63s' ,"   مرسيدس خاص - s63  ")
	AddTextEntry('gcm992gt3' ,"  بورش - GT3 ")
	AddTextEntry('m3f80' ," بي ام - M3 2015 ")
	AddTextEntry('mach1' ," موستنق ماتش - 2018 ")
	AddTextEntry('mbc63' ,"مرسيدس - C63 AMG")
	AddTextEntry('mustang19' ,"موستنق خاص - GT")
	AddTextEntry('r820' ,"أودي R8")

	AddTextEntry('0x01F576A9', "Headlight cap")
	AddTextEntry('0x084DECDE', "Carbon 6.2 HEMI Hood")
	AddTextEntry('0x0ED99625', "Wing 3")
	AddTextEntry('0x1A5690F7', "Demon Hood /w engine")
	AddTextEntry('0x1BFB3068', "Demon Spoiler")
	AddTextEntry('0x3BB0D788', "Wing 5")
	AddTextEntry('0x3D63F794', "Liberty Walk Widebody")
	AddTextEntry('0x4D7C7742', "6.2 HEMI Hood")
	AddTextEntry('0x7B94F5ED', "'12 Front Bumper")
	AddTextEntry('0x8D5B1979', "R/T Front Bumper")
	AddTextEntry('0x24A9A595', "Hellcat Hood")
	AddTextEntry('0x26B4922C', "Sunroof")
	AddTextEntry('0x39C16D89', "Sideskirts")
	AddTextEntry('0x46B105D3', "Liberty Walk Spoiler")
	AddTextEntry('0x56D6A61E', "Painted SRT Spoiler")
	AddTextEntry('0x91E12285', "Dark grill R/T Front Bumper")
	AddTextEntry('0x99B12964', "Demon Badges")
	AddTextEntry('0x297B331D', "Wing 4")
	AddTextEntry('0x651C42A9', "Dark SRT Spoiler")
	AddTextEntry('0x905A16B2', "Hellcat Badges")
	AddTextEntry('0x33565F1E', "Wing 2")
	AddTextEntry('0x6474041D', "Challenger")
	AddTextEntry('0x43528371', "Demon Widebody")
	AddTextEntry('0xA1DEC280', "Hellcat Front Bumper")
	AddTextEntry('0xB023D8B7', "Wing 1")
	AddTextEntry('0xB48C67DB', "Dark grill SRT Front Bumper")
	AddTextEntry('0xBBE157E3', "Wing 6")
	AddTextEntry('0xC2947B26', "392 HEMI Badges")
	AddTextEntry('0xC6568B6F', "Demon Carbon Front Bumper")
	AddTextEntry('0xCD1F7A5F', "Wing 7")
	AddTextEntry('0xD0EE7E20', "Painted Shaker Hood")
	AddTextEntry('0xD26D7DA1', "Roof Wing")
	AddTextEntry('0xD3952EE3' ,"Wide Exhausts")
	AddTextEntry('0xD604084B' ,"Carbon Hood")
	AddTextEntry('0xE0A1A16B' ,"Wing 8")
	AddTextEntry('0xEA494D01' ,"SRT8 Spoiler")
	AddTextEntry('0xF2EB45FE' ,"Wing 9")
	AddTextEntry('0xF8D1EA12' ,"Dark stock Spoiler")
	AddTextEntry('0xF286C150' ,"Shaker Hood")
	AddTextEntry('0xFB8875D2' ,"Demon Front Bumper")
	AddTextEntry('0xFC3882B4' ,"Dodge")



end)