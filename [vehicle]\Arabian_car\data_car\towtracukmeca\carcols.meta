<?xml version="1.0" encoding="UTF-8"?>
    
<CVehicleModelInfoVarGlobal>    
   <Sirens>
		<Item>
		  <id value="500"/> <!--Plow-->
		  <name>plow</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.50000000"/>
          <lightOuterConeAngle value="70.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- white led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- amber led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- white siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- amber siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- white siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- amber siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>
		<Item>
		  <id value="501"/> <!--pol tow-->
		  <name>plow</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.00000000"/>
          <lightOuterConeAngle value="50.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- blue led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- red led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- blue siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- red siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- red siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- blue siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- red liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- blue liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>
	</Sirens>
	
</CVehicleModelInfoVarGlobal>