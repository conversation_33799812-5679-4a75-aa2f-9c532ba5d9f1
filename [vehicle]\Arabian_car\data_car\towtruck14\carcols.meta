<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVarGlobal>    

 <Kits>
	<!--KITS Trucks 1001 -1099   -->
	<Item> 
      <kitName>1001_flatbed4_modkit</kitName>
      <id value="1001" />	 	  
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods />
	   	<linkMods />
      <statMods />
      <slotNames />
      <liveryNames>
        <Item>FLATBED3_LV1</Item>
        <Item>FLATBED3_LV2</Item>
        <Item>FLATBED3_LV3</Item>
        <Item>FLATBED3_LV4</Item>
        <Item>FLATBED3_LV5</Item>
        <Item>FLATBED3_LV6</Item>
      </liveryNames>
  </Item> 
 
 </Kits> 
 <!--END OF KITS-->
 <!--END OF KITS-->
 <!--END OF KITS-->
  
 <Lights>
	<Item> <!--1012 1013-->
      <id value="1012"/>
      <indicator>
        <intensity value="0.37500000"/>
        <falloffMax value="2.50000000"/>
        <falloffExponent value="8.00000000"/>
        <innerConeAngle value="20.00000000"/>
        <outerConeAngle value="50.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFF6400"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </indicator>
      <rearIndicatorCorona>
        <size value="0.00000000"/>
        <size_far value="8.00000000"/>
        <intensity value="4.00000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFF6400"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="60"/>
        <distBetweenCoronas_far value="65"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="1.40000000"/>
        <size_far value="2.50000000"/>
        <intensity value="5.00000000"/>
        <intensity_far value="8.00000000"/>
        <color value="0xFFFF6400"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="40"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.25000000"/>
        <falloffMax value="4.00000000"/>
        <falloffExponent value="16.00000000"/>
        <innerConeAngle value="45.00000000"/>
        <outerConeAngle value="90.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFF0A01"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </tailLight>
      <tailLightCorona>
        <size value="0.00000000"/>
        <size_far value="8.00000000"/>
        <intensity value="3.00000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFF2D05"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="60"/>
        <distBetweenCoronas_far value="65"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.30000000"/>
        <size_far value="1.00000000"/>
        <intensity value="3.30000000"/>
        <intensity_far value="0.00000000"/>
        <color value="0xFFFF2D05"/>
        <numCoronas value="6"/>
        <distBetweenCoronas value="10"/>
        <distBetweenCoronas_far value="20"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.00000000"/>
        <falloffMax value="35.00000000"/>
        <falloffExponent value="16.00000000"/>
        <innerConeAngle value="0.00000000"/>
        <outerConeAngle value="60.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFFFBEE"/>
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false"/>
      </headLight>
      <headLightCorona>
        <size value="0.10000000"/>
        <size_far value="9.00000000"/>
        <intensity value="3.60000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFFFBEE"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="40"/>
        <distBetweenCoronas_far value="55"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </headLightCorona>
      <reversingLight>
        <intensity value="0.35000000"/>
        <falloffMax value="20.00000000"/>
        <falloffExponent value="32.00000000"/>
        <innerConeAngle value="20.00000000"/>
        <outerConeAngle value="90.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFFFBEE"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </reversingLight>
      <reversingLightCorona>
        <size value="0.80000000"/>
        <size_far value="2.00000000"/>
        <intensity value="1.50000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFFFBEE"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </reversingLightCorona>
      <name>hvywreckers</name>
    </Item>
   
 </Lights>
 <!--END OF LIGHTS-->   
 <!--END OF LIGHTS-->   
 <!--END OF LIGHTS-->   
  
 <Sirens>
  <!--1100 -1199   -->
	<Item> <!--1000--> 
      <id value="1000"/>
      <name>OrangeLEDs17</name>
      <timeMultiplier value="0.80000000"/>
      <lightFalloffMax value="10.00000000"/>
      <lightFalloffExponent value="0.50000000"/>
      <lightInnerConeAngle value="2.290610"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="220"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="1431655765"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="2863311530"/>
      </rightTailLight>
      <leftHeadLightMultiples value="2"/>
      <rightHeadLightMultiples value="2"/>
      <leftTailLightMultiples value="2"/>
      <rightTailLightMultiples value="2"/>
      <useRealLights value="true"/>
      <sirens>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079600"/>
            <start value="4.71238900"/>
            <speed value="1.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863486250"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655723"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311541"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1432005205"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="4.71238900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="3"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431666005"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="4.71238900"/>
            <speed value="1.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="4204112788"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="28.68000000"/>
            <sequencer value="4205467528"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.50000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5500"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>	
	<Item> <!--1010-->
		  <id value="1010"/>
		  <name>plow</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.50000000"/>
          <lightOuterConeAngle value="70.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- white led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- amber led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- white siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- amber siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- white siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- amber siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>		
	<Item> <!--1011-->
		  <id value="1011"/>
		  <name>plow</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.00000000"/>
          <lightOuterConeAngle value="50.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- blue led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- red led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- blue siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- red siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- red siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- blue siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- red liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- blue liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>	
	<Item> <!--1014-->
		  <id value="1014"/>
		  <name>plow</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.00000000"/>
          <lightOuterConeAngle value="50.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- blue led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- red led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- blue siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- red siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- red siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- blue siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- red liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- blue liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>	
	<Item> <!--1015-->
		  <id value="1015"/> <!--Plow-->
		  <name>towtruck15</name>
          <timeMultiplier value="0.50000000"/>
          <lightFalloffMax value="50.00000000"/>
          <lightFalloffExponent value="50.00000000"/>
          <lightInnerConeAngle value="0.50000000"/>
          <lightOuterConeAngle value="70.00000000"/>
          <lightOffset value="0.00000000"/>
		  <textureName>VehicleLight_sirenlight</textureName>
		  <sequencerBpm value="800"/>
		  <leftHeadLight>
			<sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
			<sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
			<sequencer value="2863311530"/>
		  </leftTailLight>
		  <rightTailLight>
			<sequencer value="1431655765"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="2"/>
		  <rightHeadLightMultiples value="2"/>
		  <leftTailLightMultiples value="2"/>
		  <rightTailLightMultiples value="2"/>
		  <useRealLights value="true"/>
		  <sirens>
	  <!-- siren1 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="4.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren2 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren3 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.01000000"/>
            <start value="3.00000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren4 -->  <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren5 --> <!-- white rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.000000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren6 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.50000000"/>
            <speed value="70.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren7 --> <!-- hopper -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="1.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="0"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
	  <!-- siren8 -->  <!-- amber rotator -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.50000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.40000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF5000"/>
          <intensity value="6.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="false"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
			</Item>
			<Item>
          <!-- siren9 -->  <!-- white led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="70.0000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren10 --> <!-- amber led front -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren11 --> <!-- white siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.0000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- Siren12 --> <!-- amber siderunner right side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/> 
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item>
          <!-- siren13 --> <!-- white siderunner left side -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863639290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren14--> <!-- amber siderunner left side -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="10.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="90.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="7.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren15-->  <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="20.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren16--> <!-- amber liberty2 -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="30.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="100.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="90.00000000"/>
            <size value="0.00000000"/>
            <pull value="2.50000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="9.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--siren17--> <!-- white rear loads -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="40.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="4.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="50.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren18--> <!-- rear white lods -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="50.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="100.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="60.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren19--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="60.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3435973836"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item> <!--siren20--> <!-- rear amber -->
          <rotation>
            <delta value="-0.39269900"/>
            <start value="70.00000000"/>
            <speed value="120.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="858993459"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.00000000"/>
            <pull value="45.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="8.00000000"/>
          <lightGroup value="2"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		  </sirens>
		</Item>
	
	<Item> <!--1021-->
      <id value="1021"/>
      <name>All Blue LEDs</name>
      <timeMultiplier value="0.80000000"/>
      <lightFalloffMax value="10.00000000"/>
      <lightFalloffExponent value="0.50000000"/>
      <lightInnerConeAngle value="2.290610"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="220"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="1431655765"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="2863311530"/>
      </rightTailLight>
      <leftHeadLightMultiples value="2"/>
      <rightHeadLightMultiples value="2"/>
      <leftTailLightMultiples value="2"/>
      <rightTailLightMultiples value="2"/>
      <useRealLights value="true"/>
      <sirens>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079600"/>
            <start value="4.71238900"/>
            <speed value="1.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863486250"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655723"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311541"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1432005205"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="4.71238900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="3"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431666005"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.09400900"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="4.71238900"/>
            <speed value="1.00000000"/>
            <sequencer value="1431655765"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863311530"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="4204112788"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="28.68000000"/>
            <sequencer value="4205467528"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="80.00000000"/>
            <size value="0.50000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="2"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>
    <Item> <!-- 1012 towtruck12 -->
      <id value="1012"/>
      <name>towtruck12</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="250.00000000"/>
      <lightFalloffExponent value="250.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="300"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="2863311530"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="1431655765"/>
      </rightTailLight>
      <leftHeadLightMultiples value="1"/>
      <rightHeadLightMultiples value="1"/>
      <leftTailLightMultiples value="1"/>
      <rightTailLightMultiples value="1"/>
      <useRealLights value="true"/>
      <sirens>
        <Item> <!-- 1 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="2728745489"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 2 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2729093666"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 3 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2728745540"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 4 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2729093768"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 5 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="358242833"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 6 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="357935650"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 7 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="358242884"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 8 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="357935752"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 9 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5725557845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 10 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2854789845"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 11 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2854789845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 12 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5725557845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
	<!-- End 'Freedom Lightbar' -->
	
        <Item> <!-- 13 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 14 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 15 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 16 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 17 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.83259571"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 18 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.83259571"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 19 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="-0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 20 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.50000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
      </sirens>
    </Item>
	<Item> <!-- 1013 towtruck13 -->
      <id value="1013"/>
      <name>towtruck13</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="250.00000000"/>
      <lightFalloffExponent value="250.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="300"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="2863311530"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="1431655765"/>
      </rightTailLight>
      <leftHeadLightMultiples value="1"/>
      <rightHeadLightMultiples value="1"/>
      <leftTailLightMultiples value="1"/>
      <rightTailLightMultiples value="1"/>
      <useRealLights value="true"/>
      <sirens>
        <Item> <!-- 1 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="2728745489"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 2 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2729093666"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 3 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2728745540"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 4 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2729093768"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 5 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="358242833"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 6 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="357935650"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 7 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="358242884"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 8 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="357935752"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 9 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5725557845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 10 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2854789845"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
        <Item> <!-- 11 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2854789845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 12 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5725557845"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		
	<!-- End 'Freedom Lightbar' -->
	
        <Item> <!-- 13 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 14 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 15 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 16 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 17 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.83259571"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 18 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-1.83259571"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0300"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 19 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="-0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="5726702120"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!-- 20 -->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.78539816"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2863157573"/>
            <multiples value="2"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.50000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF6A00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
      </sirens>
    </Item>
		
</Sirens>
 <!--END OF SIRENS-->
 <!--END OF SIRENS-->
 <!--END OF SIRENS-->

 <Wheels>
    
 </Wheels>
 <!--END OF WHEELS-->
 <!--END OF WHEELS-->
 <!--END OF WHEELS-->

</CVehicleModelInfoVarGlobal>