function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHash<PERSON>ey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()
	AddTextEntry('10ram', 'ونيت دودج رام 3500 2010')
	AddTextEntry('ddsn15', 'نيسان ددسن 2015')
	AddTextEntry('ford20120', 'فورد - f250 2020')
	AddTextEntry('gmc9080', 'جمس سيرا - GMC 1990')
	AddTextEntry('gmcat42', 'غمارتين 1500 2020 AT4 جمس سيرا')
	AddTextEntry('gmcr20120', 'وانيت  دينالي غمارتين - ابو رقبة')
	AddTextEntry('landv62', 'تيوتا لاند كروزر شاص')
	AddTextEntry('silv1560', 'شيفروليه سلفرادو - 2015')
	AddTextEntry('silv2080', 'وانيت شيفرولية سيلفرادو')
	AddTextEntry('silv5860', 'شيفروليه سلفرادو - 1958')
	AddTextEntry('silv6580', 'شيفروليه سلفرادو - 1965')
	AddTextEntry('silvr20120', 'وانيت سلفرادو غمارتين - ابورقبة')
	AddTextEntry('silvr20120', 'وانيت سلفرادو غمارتين - ابورقبة')
	AddTextEntry('hilux1', 'هايلوكس عمل - 2010')
	--الجــمـارك
	AddTextEntry('20ramambo', 'بوكس كبير - الهلال الاحمر')
	AddTextEntry('7rs02', 'دورية فورد كراون  -  الجــمـارك')
	AddTextEntry('7rs03', 'دورية فورد اكسبلورر - الجــمـارك')
	AddTextEntry('7rs04', 'دورية فورد تورس - أمن الجــمـارك')
	AddTextEntry('7rs05', 'دورية دودج شارجر - الجــمـارك')
	--الجــمـارك
	AddTextEntry('7rs01', 'دورية تاهو -  الجــمـارك')
	AddTextEntry('7rs02', 'دورية فورد كراون  -  الجــمـارك')
	AddTextEntry('7rs03', 'دورية فورد اكسبلورر - الجــمـارك')
	AddTextEntry('7rs04', 'دورية فورد تورس - أمن الجــمـارك')
	AddTextEntry('7rs05', 'دورية دودج شارجر - الجــمـارك')
	--taxi
	AddTextEntry('taxi', 'تاكسي - فورد فكتوريا')
	AddTextEntry('taxi2', 'تاكسي - مرسيدس')

	--classics
	AddTextEntry('stafford', 'رولز رايز كلاسيك')
	AddTextEntry('beetle74', 'فولكس واجن بيتل 1974')
	AddTextEntry('trans69', 'بونتيك ترانس ام 1969')

	--compacts
	AddTextEntry('blista2', 'هوندا سيفيك تايب-أر')
	AddTextEntry('fk8', 'هوندا سيفيك تايب أر 2020')


	AddTextEntry('hilux1', 'هايلوكس عمل - 2010')

	--coupes
	AddTextEntry('f620', 'لكزس')
	AddTextEntry('sentinel', 'أودي أس-5')
	AddTextEntry('felon', 'هيونداي جينس جي-380 2016')
	AddTextEntry('ben17', 'بنتلي سوبر سبورت 2017')
	AddTextEntry('m82020', 'بي ام دبليو ام-8 2020')
	AddTextEntry('mxpan', '2020 MX-5 مازدا')
	AddTextEntry('brz13', '2016 BRZ سوبارو')
	AddTextEntry('nomad56', 'شيفروليه بيل اير واجن 1956')
	AddTextEntry('belair56', 'شيفروليه بيل اير 1956')
	AddTextEntry('wraith', 'رولز رايز ريث 2016')

	--lemo
	AddTextEntry('patriot2', 'ليموزين همر')

	--muscle
	AddTextEntry('vigero', 'دودج جالنجر')
	AddTextEntry('sabregt', 'شيفروليه كمارو اس اس 350 1969')
	AddTextEntry('outlaw', 'بونتيك فاير بيرد اصدار سباق دراق 1978')

	--offroad
	AddTextEntry('patrolswb1', 'نيسان باترول سوبر سفاري 1997 - اصدار1')
	AddTextEntry('patrolswb2', 'نيسان باترول سوبر سفاري 1997 - اصدار2')
	AddTextEntry('patrolswb3', 'نيسان باترول سوبر سفاري 1997 - اصدار3')
	AddTextEntry('bifta', 'بولاريس 1000')
	AddTextEntry('patrolsafari', 'نيسان باترول 2018')
	AddTextEntry('cherokee1', 'جيب شيروكي 1984 ')
	AddTextEntry('jeep2012', 'جيب رانجلر 2012')
	AddTextEntry('fjcruiser', 'جيب تيوتا اف جي 2014')
	AddTextEntry('baja', 'باها توربو 1000')
	AddTextEntry('can', '2020 X3 كان ام مافريك توربو')
	AddTextEntry('bc78bronco', 'فورد برونكو مرفع 1978')
	AddTextEntry('bcsportsmanm', '2021 XP بولارس سبورتسمان')
	AddTextEntry('bcbansheed', '2011 ياماها بانشي صحراوي')
	AddTextEntry('bcgeneral', 'بولارس جينيرال 1000')
	AddTextEntry('bcscrambler', 'بولارس سكرامبلر 1000')

	--pickup120
	AddTextEntry('18f350d', 'وانيت فورد اف-350 2019')
	AddTextEntry('denalihd', 'وانيت دينالي 2019')
	AddTextEntry('00f350dually', 'وانيت فورد سوبر ديوتي اف 350 2000')
	AddTextEntry('10ram', 'ونيت دودج رام 3500 2010')
	AddTextEntry('superduty', 'وانت فورد سوبر ديوتي اف 350 2008')
	AddTextEntry('3500flatbed', 'سطحة دودج رام 3500 2010')
	AddTextEntry('193500hd', 'وانيت دودج رام 3500 2019')
	AddTextEntry('brokenet', 'وانيت فورد اف-450 2010')
	AddTextEntry('topkickhd', 'وانيت جمس توب كيك 2009')
	AddTextEntry('80silverado', 'وانيت سيلفرادو قمارتين 1986 - مرفع')
	AddTextEntry('megaram', 'وانيت دودج ميجا رام 3500 - مرفع')
	AddTextEntry('00excursion', 'فورد اكسكيرشن اصدار سيما 2000 - مرفع')
	AddTextEntry('19ram', 'وانيت دودج رام 1500 - لورايدر')
	AddTextEntry('20silv3500', 'وانيت سلفرادو قمارتين 3500 2020 - سحب مقطورة ابورقبة')
	AddTextEntry('20silv3500work', 'وانيت سلفرادو قمارتين 3500 2020 - سحب مقطورة عادية')
	AddTextEntry('20denalihd', 'وانيت  دينالي قمارتين 3500 2020 - سحب مقطورة عادي')
	AddTextEntry('20denalihdg', 'وانيت  دينالي قمارتين 3500 2020 - سحب مقطورة ابو رقبة')
	AddTextEntry('18ram', 'دودج رام قمارتين ديزل مرفع 2018')
	AddTextEntry('bc20hd', '2020 فورد دينالي قمارتين دبل منزل')
	AddTextEntry('transformer', '2020 F-250 فورد قمارتين ديزل')
	AddTextEntry('g63amg6x6', '2015 AMG 6x6 مرسيدس')
	AddTextEntry('ramlh20', 'دودج رام لونج هورن قمارتين 2500 2020')
	AddTextEntry('mmax', 'شيفروليه سلفرادو قمارتين 2500 2007 - اصدار الوحش')
	AddTextEntry('bc5506d', '2006 F-550 فورد')
	AddTextEntry('bcal450', '2019 F-450 فورد')
	AddTextEntry('bc052500', 'جمس سيرا 2500 2005 - مرفع')
	AddTextEntry('bclandscape', 'دودج رام 5500 2020 - اصدار مزارع')
	AddTextEntry('reds450b', 'فورد F-450 2020')
	AddTextEntry('reds450g', 'فورد F-450 2020 - ابورقبة')
	AddTextEntry('bc01sierrahd', 'جمس سيرا 2500 2001 - مرفع')
	AddTextEntry('bc06bagged', 'شيفروليه سلفرادو 2500 2006')
	AddTextEntry('18f350ds', '2018 ابورقبة F-350 فورد')
	AddTextEntry('18f350dsb', '2018 F-350 فورد')
	AddTextEntry('wdf350', 'فورد اف 350 2019 - مرفع')
	AddTextEntry('bcbaggedram', 'دودج رام 3500 2018 - هايدروليك')
	AddTextEntry('bcf650', 'فورد اف650 2006')


	--pickup40
	AddTextEntry('nissanditsun', 'وانيت دتسن 1985')
	AddTextEntry('h3', 'هـمـر H2 وانيت')
	AddTextEntry('h3', 'هـمـر H2 وانيت')
	AddTextEntry('type266', 'باص فولكس واجن وانيت 1972')


	--pickup60
	AddTextEntry('toyotahilux', 'وانيت هيلوكس 2015')
	AddTextEntry('nissantitan17', 'نيسان تيتان 2017')
	AddTextEntry('landv62', 'تيوتا لاند كروزر شاص')
	AddTextEntry('gmcs', 'وانيت جمس سييرا 2017')
	AddTextEntry('20f250', 'وانيت فورد اف-250 2020')
	AddTextEntry('czr2', '2020 ZR2 شيفروليه كولورادو')
	AddTextEntry('amarok', '2020 فولكس واجن اماروك')



	--pickup80
	AddTextEntry('silverado', 'وانيت شيفرولية سيلفرادو')
	AddTextEntry('x6r', 'بي ام دبليو X6 Raptor')
	AddTextEntry('f150', 'وانيت فورد رابتر أف-150')
	AddTextEntry('1500dj', 'وانيت شيفروليه سيلفرادو مرفع')
	AddTextEntry('sadler', 'وانيت فورد أف-350')
	AddTextEntry('raptor2017', 'وانيت فورد رابتر 2017')
	AddTextEntry('2020silv', 'وانيت سلفرادو قمارتين 1500 2020 - مرفع')
	AddTextEntry('20trailboss', 'وانيت سلفرادو قمارتين 1500 2020')
	AddTextEntry('ram1500', 'دودج رام قمارتين 1500 2020')
	AddTextEntry('gmcat4', 'قمارتين 1500 2020 AT4 جمس سيرا')
	AddTextEntry('silverado2', 'شيفروليه سلفرادو قمارتين 1500 2018')
	AddTextEntry('denali18', 'جمس سيرا دينالي 1500 2018')
	AddTextEntry('19tundra', 'تيوتا تندرا قمارتين 2020')
	AddTextEntry('bad250', '2020 فورد سوبر ديوتي قمارتين مرفع')
	AddTextEntry('bc2500hd', 'دودج رام قمارتين مرفع 2500 2020')
	AddTextEntry('bcss', '2021 سوبر سنيك F-150 فورد')
	AddTextEntry('mmaxdw', '2007 شيفروليه سلفرادو الوحش')
	AddTextEntry('richobs', '1982 F-100 فورد مرفع')
	AddTextEntry('foxct', 'تسلا وانيت سايبر')
	AddTextEntry('silve', 'شيفروليه سلفرادو قمارتين 1500 مرفع 2018')
	AddTextEntry('c10', '1965 C10 شيفروليه')
	AddTextEntry('silv86', 'شيفروليه سلفرادو قمارة 1500 1986')
	AddTextEntry('driftram', 'دودج رام 1500 2010 - اصدار درفت')
	AddTextEntry('silv2500hd', 'شيفروليه سلفرادو 2500 2020')
	AddTextEntry('twister', 'دودج رام 2500 1998 - اصدار تويستر')
	AddTextEntry('92dodgeram', 'دودج رام 250 1992 - مرفع')


	--sedans
	AddTextEntry('camry55', 'تويوتا كـامـري 2016')
	AddTextEntry('750li', 'بي ام دبليو 750')
	AddTextEntry('benzc32', 'مرسيدس سي32')
	AddTextEntry('emperor', 'كـاديلاك فليتـود 1993')
	AddTextEntry('benzc32', 'مرسيدس سي32')
	AddTextEntry('stanier', 'مرسيدس 560 SEL')
	AddTextEntry('nisaltima', 'نيسان التيما 3.5 SE')
	AddTextEntry('caprice13', 'شيفرولية كابريس 2013')
	AddTextEntry('tailgater', 'أودي أي-8')
	AddTextEntry('bmwe38', 'بي ام دبليو 750 1997')
	AddTextEntry('bmwe65', 'بي ام دبليو 750 2004')
	AddTextEntry('glendale', 'مرسيدس 300 6.3 1972 SEL')
	AddTextEntry('benzs600', 'مرسيدس أس-600')
	AddTextEntry('camry18', 'تيوتا كامري 2018')
	AddTextEntry('rrphantom', 'رولز رايز فانتوم 2018')
	AddTextEntry('rculi', 'رولز رايز كولينان 2019')
	AddTextEntry('96impala', 'شيفروليه امبالا 1996')
	AddTextEntry('750li2', 'بي ام دبليو 750-اي-ال')
	AddTextEntry('taurus', 'فورد تورس')
	AddTextEntry('gs350', 'ليكزس جي-اس-350')
	AddTextEntry('pullman', 'مايباخ')
	AddTextEntry('panamera17turbo', 'بورش باناميرا توربو 2020')
	AddTextEntry('ct5v', '2021 CT5-V كاديلاك')
	AddTextEntry('2019M5', '2020 M5 بي ام دبليو')
	AddTextEntry('sonata20', '2021 هيونداي سوناتا')
	AddTextEntry('tmodel', 'تسلا موديل 3 2021')
	AddTextEntry('2018s650p', 'مايباخ 2018 - مصفحة S650 مرسيدس')
	AddTextEntry('rmode63s', '2019 برابس E63S مرسيدس')
	AddTextEntry('e63b', '2014 برابس E63 مرسيدس')

	--sports
	AddTextEntry('game718', 'بورش 718 كايمن كاريرا أس')
	AddTextEntry('c7r', 'كورفيت C7R')
	AddTextEntry('rs6', 'أودي RS6')
	AddTextEntry('schwarzer', 'بي ام دبليو M6 2006')
	AddTextEntry('S63W222', 'مرسيدس أس-63 2014')
	AddTextEntry('benzsl63', 'مرسيدس AMG SL 63')
	AddTextEntry('rm3e36', 'بي ام دبيلو M3 e36')
	AddTextEntry('rmodbmwi8', 'بي ام دبليو i8')
	AddTextEntry('rmodfordgt', 'فورد موستنق GT 2011')
	AddTextEntry('rmodgtr', 'نيسان GTR Wide Body Kit')
	AddTextEntry('gtr', 'نيسان GTR')
	AddTextEntry('rmodm4', 'بي ام دبيلو M4')
	AddTextEntry('slsamg', 'SLS AMG مرسيدس')
	AddTextEntry('rmodmustang', 'فورد موستنق GT 2015')
	AddTextEntry('rmodskyline', 'نيسان GTR')
	AddTextEntry('c7z06', 'كورفيت C7 ZO6')
	AddTextEntry('focusrs', 'فورد فوكس آر أس 2017')
	AddTextEntry('m3e92', 'بي ام دبليو M3 e92 2008')
	AddTextEntry('kuruma', 'ميتسوبيشي لانسر')
	AddTextEntry('elegy2', 'ٍنيسان سكاي لاين')
	AddTextEntry('ast', 'استون مارتن')
	AddTextEntry('zl12017', 'كمارو SS 2017')
	AddTextEntry('furoregt', 'نيسان زد 370 2016')
	AddTextEntry('gamea45', 'مرسيدس A45 AMG')
	AddTextEntry('gamef430s', 'فيراري أف-430 سكودريا')
	AddTextEntry('16charger', 'دودج شـارجـر 2016')
	AddTextEntry('supra2', 'تيوتا سوبرا')
	AddTextEntry('ninef', 'أودي R8')
	AddTextEntry('sultan', 'سوبارو WRX STI')
	AddTextEntry('m2', 'بي ام دبليو M2 2016')
	AddTextEntry('m3e46', 'بي ام دبليو M3 e46 2005')
	AddTextEntry('fk8', 'هوندا سيفيك ار')
	AddTextEntry('exigev6', 'لوتس اكسيج في6')
	AddTextEntry('p7', 'جاكور تايب اف بروجكت7')
	AddTextEntry('911r', 'بورشه 911 ار جي تي 3 2017')
	AddTextEntry('m3gtr', 'بي ام دبليو ام-3 جي-تي-ار')
	AddTextEntry('c8', 'كورفيت سي-8 2020')
	AddTextEntry('c8c', 'كورفيت سي-8 2020 - كاربون فايبر')
	AddTextEntry('supraa90', 'تيوتا سوبرا 2020')
	AddTextEntry('2020ss', 'كمارو اس-اس 2020')
	AddTextEntry('bmwm8', 'بي ام دبليو - ام 8')
	AddTextEntry('gtr2', '2019 GTR نيسان')
	AddTextEntry('16challenger', '2020 شالنجر ديمون')
	AddTextEntry('ctsv16', '2019 CTS-V كاديلاك')
	AddTextEntry('inf', 'نفينيتي المشروع الاسود اس 2018')
	AddTextEntry('911rwb', '1989 911 RWB بورش')
	AddTextEntry('terminator', 'فورد موستنح كوبرا شلبي 2004')
	AddTextEntry('foxshelby', '2021 GT500 فورد موستنج شلبي')
	AddTextEntry('foxsupra', 'تيوتا سوبرا 2021')
	AddTextEntry('p993t', '1995 بورش 993 توربو')
	AddTextEntry('95zr1', '1995 ZR1 C5 كورفت')
	AddTextEntry('rt10', '1992 RT10 دودج فايبر')
	AddTextEntry('z48', 'بي ام دبليو زي4 ام 2006')
	AddTextEntry('vip8', 'دودج فايبر 2006')
	AddTextEntry('czr1', '2009 ZR1 كورفت')
	AddTextEntry('al1', '2020 A110 الباين')
	AddTextEntry('ats', '2020 ATS GT')
	AddTextEntry('it18', 'ايطالديزاين زيرونو 2020')
	AddTextEntry('442', 'اولدزموبيل 422 1968')
	AddTextEntry('718gt4', '2016 GT4 بورش كايمان')
	AddTextEntry('93mustang', 'فورد موستنق جي تي 1993')
	AddTextEntry('stiwrc', '2001 WRX STI سوبارو')
	AddTextEntry('2f2fgts', 'متسوبيشي اكلبس سبايدر 2003 اصدار فاست اند فريوس')
	AddTextEntry('fnflan', 'متسوبيشي لانسر ايفو 7 2002 اصدار فاست اند فريوس')
	AddTextEntry('2f2fmk4', 'تيوتا سوبرا 1994 اصدار فاست اند فريوس')
	AddTextEntry('ff4wrx', 'سوبارو WRX STI 2009 اصدار فاست اند فريوس')
	AddTextEntry('fnfmk4', 'تيوتا سوبرا 1994 اصدار فاست اند فريوس')
	AddTextEntry('2f2fmle7', 'متسوبيشي لانسر ايفو 7 2002 اصدار فاست اند فريوس')
	AddTextEntry('fnf4r34', 'نيسان GT-R34 1999 اصدار فاست اند فريوس')
	AddTextEntry('2f2fgtr34', 'نيسان GT-R34 1999 اصدار فاست اند فريوس')
	AddTextEntry('fnfrx7', 'مازدا RX7 1994 اصدار فاست اند فريوس')
	AddTextEntry('fnfmits', 'متسوبيشي اكلبس 1995 اصدار فاست اند فريوس')
	AddTextEntry('2f2frx7', 'مازدا RX7 1994 اصدار فاست اند فريوس')
	AddTextEntry('2f2fs2000', 'هوندا S2000 2001 اصدار فاست اند فريوس')
	AddTextEntry('fnfjetta', 'فولكس واجن جتا 1995 اصدار فاست اند فريوس')
	AddTextEntry('fnfrx7dom', 'مازدا RX7 1994 اصدار فاست اند فريوس')
	AddTextEntry('350zdk', 'نيسان Z350 2002 اصدار فاست اند فريوس')
	AddTextEntry('350zm', 'نيسان Z350 2002 اصدار فاست اند فريوس')
	AddTextEntry('brabus850', 'مرسيدس بنز برابس 850 2015')



	--sportsclassics
	AddTextEntry('stingergt', 'دتسن زد جي تي')
	AddTextEntry('casco', 'جاكور إي-تايب')
	AddTextEntry('caprice89', 'كابريس 1989')
	AddTextEntry('c10custom', 'وانيت كلاسيك')
	AddTextEntry('firebird', 'بونتيك')
	AddTextEntry('jamara', 'لامبورجيني جامارا 1970')
	AddTextEntry('agta', '1965 GTA الفا روميو')
	AddTextEntry('alfa65', '1965 TZ2 الفا روميو')
	AddTextEntry('amcj', '1967 AMC AMX جيفالين')
	AddTextEntry('66fastback', 'فورد موستنق جي تي 350 ار 1966')
	AddTextEntry('countach', 'لامبورجيني كونتاش 1974')


	--super
	AddTextEntry('t20', 'ماكلارين بي-1')
	AddTextEntry('turismor', 'فيراري جي تي أر')
	AddTextEntry('918', 'بورش  918 سبايدر')
	AddTextEntry('aventadors', 'لمبرجيني  افنتدور')
	AddTextEntry('rmodpagani', 'باقاني هيارا رودستر 2018')
	AddTextEntry('ie', 'أبولو إنتنزا إموزيوني')
	AddTextEntry('ctr3', 'بورش رف سي تي ار 3')
	AddTextEntry('570s', 'ماكلارين 570 اس')
	AddTextEntry('650s', 'ماكلارين 650 اس اصدار خاص')
	AddTextEntry('mp412c', 'ماكلارين ام-بي4 12سي')
	AddTextEntry('m8gte', 'بي ام دبليو ام-8 جي-تي-اي')
	AddTextEntry('f458', 'فيراري اف-458 ايطاليا 2010')
	AddTextEntry('f430s', 'فيراري اف-430-اس سكودريا')
	AddTextEntry('gt17', 'فورد جي تي 2017')
	AddTextEntry('terzo', 'لامبورجيني تيرزو ميلينيو')
	AddTextEntry('p1lm', '2016 P1 LM ماكلارن')
	AddTextEntry('c8r', '2020 اصدار  حلبة سباق C8 شيفروليه كورفت')
	AddTextEntry('aimgainnsx', '2021 NSX هوندا')
	AddTextEntry('fxxkevo', '2020 FXX-EVO فيراري')
	AddTextEntry('regera16', '2020 كونيجسيج ريجيرا')
	AddTextEntry('sesto', 'لامبورجيني سيستو ايليمينتو 2012')
	AddTextEntry('bvit', 'بوقاتي فيرون 2012')
	AddTextEntry('mcgt20', '2020 ماكلارين جي تي')
	AddTextEntry('ldsv', '1995 لامبورجيني ديابلو اس في')
	AddTextEntry('mcst', 'ماكلارين سبيدتيل 2020')
	AddTextEntry('eb110', '1995 EB110 بوقاتي')
	AddTextEntry('bcps', 'بوقاتي شيرون 2020')
	AddTextEntry('monza', 'فراري مونزا اس بي 2 2019')
	AddTextEntry('cc8s', '2002 CC8S كونيجسيج')
	AddTextEntry('zn20', '2020 TS1 GT زنفو')
	AddTextEntry('f40', '1987 F40 فراري')
	AddTextEntry('acura2f2f', 'اكيورا NSX 2000 اصدار فاست اند فريوس')
	AddTextEntry('migu1', 'جيمبالا MIG-U1 2010')
	AddTextEntry('fenzo1', 'فراري انزو 2003')

	--suvs
	AddTextEntry('evoque', 'رنج روفر ايفوك')
	AddTextEntry('vxr', 'تويتا لاند كروزر')
	AddTextEntry('h6', 'همر H6')
	AddTextEntry('G65', 'مرسيدس AMG G65')
	AddTextEntry('gmcyd', 'جمس يوكن دينالي 2015')
	AddTextEntry('x5e53', 'بي ام دبليو X5 Sport')
	AddTextEntry('xls', 'بي ام دبليو X6 Mpower')
	AddTextEntry('granger', 'جيمس يوكن أكس-ألـ 2003')
	AddTextEntry('mesa3', 'جيب رانجلر مرفع صيني')
	AddTextEntry('mesa', 'تيوتا لاند كروزر ربع')
	AddTextEntry('tahoe', 'شيفرولية تاهو 2013')
	AddTextEntry('toyotaland', 'تيوتا لاند كروزر ستيشن 2002')
	AddTextEntry('bentayga17', 'جيب بنتلي')
	AddTextEntry('moonbeam', 'فان نقل ركاب')
	AddTextEntry('r50', 'فولكس واجن طوارق 2008')
	AddTextEntry('patriot', 'هـمـر H2')
	AddTextEntry('cayenne', 'بورش كـايـن تيربو-أس 2016')
	AddTextEntry('lex570', 'لكزس أل-أكس-570')
	AddTextEntry('x6m', 'بي ام دبليو أكس-6 2016 - نسخة جديدة')
	AddTextEntry('patrol', '2016 بنيسان باترول نسمو')
	AddTextEntry('urus', 'لامبورجيني يورس')
	AddTextEntry('srt8', 'جيب شروكي اس ار تي 8 2016')
	AddTextEntry('cesc21', 'كاديلاك اسكالد 2021')
	AddTextEntry('gle53', '2020 GLE 53 مرسيدس بنز')
	AddTextEntry('qx80', '2017 QX80 انفينيتي')
	AddTextEntry('mlnovitec', '2020 V2 نوفيتك مازيراتي ليفانتي استيسو')
	AddTextEntry('suburban', '1973 شيفروليه سوبربان')
	AddTextEntry('teslax', '2021 X تسلا موديل')
	AddTextEntry('lrrr', 'رنج روفر 1969')
	AddTextEntry('bronco80', 'فورد برونكو 1980')
	AddTextEntry('bronco81', 'فورد برونكو 1981')
	AddTextEntry('bronco82', 'فورد برونكو 1982')
	AddTextEntry('18Velar', 'رنج روفر فلار 2018')
	AddTextEntry('rrst', 'رنج روفر فوج 2020')
	AddTextEntry('tulenis', 'نيسان باترول نسمو 2016')
	AddTextEntry('mache', 'فورد ماك اي 2021')
	AddTextEntry('4444', '2021 G-Class AMG مرسيدس')
	AddTextEntry('rculi2', 'رولز رايز كولينان 2019')
	AddTextEntry('rculi2', 'رولز رايز كولينان 2019')
	AddTextEntry('bc21bronco', 'فورد برونكو 2021 - مرفع')
	AddTextEntry('bc21tahoe', 'شيفرولية تاهو 2021 - معدل')
	AddTextEntry('wagoneer', 'جيب واقونير 1963')


	--trailer
	AddTextEntry('graintrailer', 'مقطورة تركتر المزرعة')
	AddTextEntry('botdumptr', 'مقطورة مناجم وسط')
	AddTextEntry('trailerswb', 'مقطورة براد وسط')
	AddTextEntry('trailerswb2', 'مقطورة براد كبير')
	AddTextEntry('trailerlogs2', 'مقطورة اخشاب كبيرة')
	AddTextEntry('dumptr', 'مقطورة مناجم كبير')
	AddTextEntry('fueltr', 'مقطورة نفط كبيرة')
	AddTextEntry('gastr', 'مقطورة الغاز كبيرة')
	AddTextEntry('tr2', 'مقطورة سحب مركبات كبيرة')
	AddTextEntry('trailersmall', 'مقطورة سحب مركبات صغيرة')
	AddTextEntry('boattrailer', 'مقطورة سحب قارب')
	AddTextEntry('lowboy', 'مقطورة سحب معدات ثقيلة')
	AddTextEntry('pjtrailer', 'مقطورة سحب مركبات كبيرة 2')
	AddTextEntry('trailer01', 'مقطورة مطورة - اخشاب')
	AddTextEntry('trailer02', 'مقطورة مطورة - براد 1')
	AddTextEntry('trailer03', 'مقطورة مطورة - براد 2')
	AddTextEntry('trailer04', 'مقطورة مطورة - نفط')
	AddTextEntry('trailer05', 'مقطورة مطورة - سطحة')
	AddTextEntry('thauler', 'مقطورة سحب مركبات وسط - فردي')
	AddTextEntry('bigtex20', 'مقطورة سحب مركبات ابورقبة وسط - فردي')
	AddTextEntry('bigtex40', 'مقطورة سحب مركبات ابورقبة كبيرة - مزدوج')
	AddTextEntry('hometrailer', 'مقطورة الكرفان')
	AddTextEntry('ehauler', 'مقطورة سحب مركبة مغلقة')
	AddTextEntry('thauler2', 'مقطورة سحب مركبات وسط - فردي 2')
	AddTextEntry('nbhauler', 'مقطورة سحب قارب نايترو')
	AddTextEntry('yftrailer', 'مقطورة سحب قارب كبيرة')
	AddTextEntry('seadoohauler', 'مقطورة سحب سيدو')
	AddTextEntry('ptrailer', 'مقطورة سحب قارب بينينقتون')
	AddTextEntry('semihauler', 'مقطورة سحب مركبات ابو رقبة')
	AddTextEntry('manaclog', 'مقطورة خشب')
	AddTextEntry('pullsled', 'مقطورة سحب التحدي')
	AddTextEntry('bcfueltanker', 'مقطورة وقود')
	AddTextEntry('shauler', 'مقطورة سحب مركبات ابورقبة كبيرة')

	--trucks
	AddTextEntry('packer2', 'راس تريله بيكر 2')
	AddTextEntry('phantom4', 'راس تريله فانتوم 4')
	AddTextEntry('ct660', 'شاحنة كات 660 متعددة استعمال')
	AddTextEntry('ct660dump', 'شاحنة مناجم كات 660')
	AddTextEntry('hauler3', 'راس تريله هولر 3')
	AddTextEntry('hauler4', 'راس مقطورة كلاسيك')
	AddTextEntry('hauler5', 'راس مقطورة مان')
	AddTextEntry('hauler6', 'راس مقطورة مرسيدس حمولة ثقيلة')
	AddTextEntry('hauler7', 'راس تريلة اكتروس')
	AddTextEntry('hauler8', 'راس تريلة قديم')
	AddTextEntry('fhauler', 'راس تريلة فريلاينر')
	AddTextEntry('chantom', 'راس تريلة شان')
	AddTextEntry('chantom2', 'شاحنة شان متعدد الاستعمال')
	AddTextEntry('scrap', 'شاحنة السكراب')
	AddTextEntry('acty', 'ربع نقل هوندا')
	AddTextEntry('taco', 'شاحنة متجر متنقل')
	AddTextEntry('pete351', '1950 راس تريله بيتربيلت')
	AddTextEntry('white55', '1947 WB26 راس تريله وايت')
	AddTextEntry('ken49', 'راس تريله كينوورث 1949')
	AddTextEntry('kw900', '1961 W900 راس تريله كينوورث')
	AddTextEntry('bchauler', '2003 379 راس تريلة بيتربلت')
	AddTextEntry('actros', 'راس تريله مرسيدس اكتروس')
	AddTextEntry('toyhauler', '1950 فورد دبل ديزل منزل')
	AddTextEntry('ford9000', 'راس تريلة فورد 9000 1980')
	AddTextEntry('pete352custom', 'راس تريلة بيتربلت 352 1974')
	AddTextEntry('phantomhd', 'راس تريلة فانتوم حمولة ثقيلة')

	AddTextEntry('d7r', 'حفارة كبيرة CAT D7R')
	AddTextEntry('cat259', 'حفارة صغيرة CAT 259')
	AddTextEntry('oiltanker', 'تنكر الغاز والنفط')
	AddTextEntry('d7rcat745c', 'شاحنة المناجم CAT 745c')
	AddTextEntry('dump2', 'شاحنة المناجم CAT 770')
	AddTextEntry('unimog', '2021 شاحنة مرسيدس يونيموج')
	AddTextEntry('tooltruck', 'شاحنة التزويد')
	AddTextEntry('bensonc', 'شاحنة بنسون معدلة')
	AddTextEntry('bensonc2', 'شاحنة بنسون معدلة')

	AddTextEntry('rubble', 'شاحنة المناجم ريبل')
	AddTextEntry('tiptruck', 'شاحنة المناجم CAT 610')
	AddTextEntry('tiptruck2', 'شاحنة المناجم CAT 600')
	AddTextEntry('motorgrader', '')
	AddTextEntry('excavator', '')
	AddTextEntry('cat259', 'حفارة كات 259')
	AddTextEntry('cat555d', 'حفارة كات 555 كبيرة')

	AddTextEntry('tractor2', 'تركتر زراعة')
	AddTextEntry('forklift2', 'رافعة شوكية')
	AddTextEntry('jdpuller', 'تركتر زراعة - معدل')

	AddTextEntry('oiltanker', 'تنكر نفط')
	AddTextEntry('fordtanker', 'وانيت فورد نفط')
	AddTextEntry('exmark', 'سيارة تقليم الحشيش')

	AddTextEntry('rentalbus', 'باص 10 راكب')
	AddTextEntry('tourbus', 'باص سياحي 10 راكب')
	AddTextEntry('coach', 'باص نقل خاص 10 راكب')
	AddTextEntry('gcart', 'كارت نقل راكبين')

	AddTextEntry('bc205500w', 'سطحة دودج 5500 2020')


	--motor home
	AddTextEntry('Camper', 'كرفان')
	AddTextEntry('Journey', 'كرفان')
	AddTextEntry('17jamb', 'كرفان فورد 2017')
	AddTextEntry('mbhome', 'كرفان مرسيدس اكتروس مع كراج')

	--Van
	AddTextEntry('newsvan', 'فان جريدة اس في 1')
	AddTextEntry('newsvan2', 'فان جريدة اس في 2')
	AddTextEntry('speedo5', 'فان نقل 120 كغم - صندوق')
	AddTextEntry('speedo6', 'فان صراف متنقل')
	AddTextEntry('type263', 'باص فولكس واجن كشف 1972')
	AddTextEntry('type262', 'باص فولكس واجن 1972')
	AddTextEntry('e15082', '1982 E-150 فورد')
	AddTextEntry('astro88', 'شيفروليه استرو 1988')

	--emergency
	AddTextEntry('ems', 'اسعاف فورد كبير')
	AddTextEntry('ems2', 'اسعاف سوبربان')

	--Motorcycle
	AddTextEntry('hcbr17', '2019 CBR-1000RR هوندا')
	AddTextEntry('yzfr6', '2016 YZF-R6 ياماها')
	AddTextEntry('goldwing', '2020 الذهبي جولد وينج')
	AddTextEntry('gsx1000', '2018 GSXR-1000 سوزوكي')
	AddTextEntry('softail1', 'دراجة نارية سوفتيل')
	AddTextEntry('bcr1drag', '2021 YZF-R1 ياماها')
	AddTextEntry('bmws', 'BMW S1000 RR 2014')
	AddTextEntry('zx10', 'كاوازاكي نينجا ZX10 R 2014')
	AddTextEntry('z1000', 'كاوازاكي Z1000 2014')
	AddTextEntry('hayabusa', 'سوزوكي هايبوسا GSX1300 2015')
	AddTextEntry('rc', 'KTM RC 390 2014')
	AddTextEntry('d99', 'دوكاتي 1199 بانيجال 2012')
	AddTextEntry('f4rr', 'اقوستا اف4 ار ار 2014 MV')



	--NEW
	AddTextEntry('trans_mbxclass', 'وانيت مرسيدس')
	AddTextEntry('speedo3', 'فان نقل 120 كغم - صندوق')
	AddTextEntry('speedo4', 'فان نقل 120 كغم - براد')
	AddTextEntry('rumpo4', 'فان نقل 120 كغم - براد')
	AddTextEntry('rumpo5', 'فان نقل 120 كغم - براد')
	AddTextEntry('muletip', 'شاحنة معادن 250 كغم')
	AddTextEntry('hauler6_trailer', 'مقطورة مرسيدس حمولة ثقيلة')
	AddTextEntry('steed', 'شاحنة نقل 250 كغم - صندوق')
	AddTextEntry('yankee', 'شاحنة نقل 250 كغم - صندوق')
	AddTextEntry('yankee2', 'شاحنة نقل 250 كغم - معادن')
	AddTextEntry('dvgtsr', 'دودج فايبر - اصدار حلبة سباق')
	AddTextEntry('majimalm', 'انس مجيما - اصدار حلبة سباق')
	AddTextEntry('gtrlms', 'فورد جي-تي - اصدار حلبة سباق')


	--Plane
	AddTextEntry('saab2000', '')
	AddTextEntry('tu154m', '')
	AddTextEntry('707', '')
	AddTextEntry('747', '')
	AddTextEntry('757', '')
	AddTextEntry('773er', '')
	AddTextEntry('788', '')
	AddTextEntry('737200', '')
	AddTextEntry('a319', '')
	AddTextEntry('a321eno', '')
	AddTextEntry('a333', '')
	AddTextEntry('a343', '')
	AddTextEntry('a350', '')
	AddTextEntry('a380', '')
	AddTextEntry('an2', '')
	AddTextEntry('atr72', '')
	AddTextEntry('avashut', '')
	AddTextEntry('a727', '')
	AddTextEntry('a727c', '')
	AddTextEntry('bac', '')
	AddTextEntry('dc10', '')
	AddTextEntry('emb100', '')
	AddTextEntry('emb120', '')
	AddTextEntry('emb175', '')
	AddTextEntry('emb390fedex', '')
	AddTextEntry('emb145', '')
	AddTextEntry('emb190', '')
	AddTextEntry('emb1000', '')
	AddTextEntry('fokker100', '')
	AddTextEntry('il62m', '')
	AddTextEntry('il76', '')
	AddTextEntry('il96', '')
	AddTextEntry('l1011', '')
	AddTextEntry('lcf', '')
	AddTextEntry('e190e2', '')
	AddTextEntry('208', '')
	AddTextEntry('747sp', '')
	AddTextEntry('airglider', '')

	--Boat
	AddTextEntry('nitroboat', 'قارب نايترو')
	AddTextEntry('sr510', '')
	AddTextEntry('sr650fly', '')
	AddTextEntry('yacht2', '')
	AddTextEntry('franco125', '')
	AddTextEntry('contender39', '')
	AddTextEntry('seadoogti215', 'سيدو جي تي ار 215 2013')
	AddTextEntry('tritoon', 'بينينقتون موديل ار 2020')
	AddTextEntry('42ftyellowfin', '')

	AddTextEntry('560sec87', ' مرسيدس كوبية - SEC 560')
	AddTextEntry('750il', ' بي ام دبليو - BMW 750 iL ')
	AddTextEntry('Benz300SEL', 'مرسيدس - SEL 300')
	AddTextEntry('boss302', 'موستنق - Boss 302')
	AddTextEntry('camaro68', 'كمارو - Ss68')
	AddTextEntry('camaro69', 'كمارو - Ss 69')
	AddTextEntry('caprice91', 'كابرس صابونة - 1991')
	AddTextEntry('caprice93', 'كابرس صابونة - 1993')
	AddTextEntry('caprs', 'كابرس - 1989')
	AddTextEntry('chall70', 'تشالنجر - RT 1970')
	AddTextEntry('corvette63', 'كورفت - SbltWindow 63')
	AddTextEntry('firebird', 'بونتياق - FireBird')
	AddTextEntry('firebird77', 'بونتياق ترانزان - 1977')
	AddTextEntry('gsxb', 'بيوك جي اس اكس - Buick GSX')
	AddTextEntry('impala96', 'شوفرليت امبالا - 1996')
	AddTextEntry('impala672', 'شوفرليت امبالا - 1967')
	AddTextEntry('mb300sl', 'مرسيدس - Sl300')
	AddTextEntry('mercw126', ' مرسيدس بنز - SEL 560')
	AddTextEntry('mustang68', 'موستنق - fastback 1968')
	AddTextEntry('silver67', 'روز رايز - 1967')
	AddTextEntry('trans69', 'بونتياق ترانزام - 1969')
	AddTextEntry('z2879', 'شوفرليت كمارو - z28')
	AddTextEntry('z2879', 'شوفرليت كمارو - z28')

	AddTextEntry('bmwx6', 'بي ام دبليو خاص - X6')
	AddTextEntry('expmax20', 'فورد اكس ادشن - 2020')
	AddTextEntry('g65amg', 'مرسيدس جي كلاس - 2013')
	AddTextEntry('g5004x4', 'مرسيدس جي كلاس اوف رود - 2019 ')
	AddTextEntry('g632019', 'مرسيدس جي كلاس - 2019')
	AddTextEntry('gclass', 'مرسيدس جي كلاس - اوف رود')
	AddTextEntry('hdd', 'تايوتا لاند كروزر - GX')
	AddTextEntry('lex57015', 'جيب لكزس - 2015')
	AddTextEntry('lxs', 'جيب لكزس  - Black Edition')
	AddTextEntry('lxs_2019', 'جيب لكزس - 2019')
	AddTextEntry('q820', 'اودي - Q8 2020')
	AddTextEntry('rb3_2006', 'لاند كروزر ربع - 2006')
	AddTextEntry('rb3_2017', 'لاند كروزر ربع - 2017')
	AddTextEntry('rrst', 'رنج روفر فوج 2020')
	AddTextEntry('subn', 'شيفرولية سوبربان - 2010')
	AddTextEntry('taho89', 'شيفرولية سوبربان - 1989')
	AddTextEntry('tahoe21', 'شيفرولية تاهو - 2021')
	AddTextEntry('type262', 'فولكس فاجن فان - 1962')
	AddTextEntry('vxr_2020', 'لاند كروزر - VXR 2020')
	AddTextEntry('w463a', 'مرسيدس جي كلاس برابوس - 2019 ')
	AddTextEntry('745le', "Bmw - 245 Li")
	AddTextEntry('bbentayga', " بنتلي بنتياق - bentayga 2017")
	AddTextEntry('ben17', " بنتلي سبورت - ben 2017 ")
	AddTextEntry('bfs14', " بنتلي قوست - Ghost 2014 ")
	AddTextEntry('binkshf', "  بنتلي سبورت كشف - ben 2019 ")
	AddTextEntry('bmm', "  بنتلي قوست - Ghost 2013 ")
	AddTextEntry('cullinan', "  روز كولينان - Roz cullinan 2018 ")
	AddTextEntry('dawnonyx', " روز داون اونكس - Dawn Onyx 2019 ")
	AddTextEntry('genesisg90', " جينيسيس موتورز - GENESIS G90 ")
	AddTextEntry('ghostewb1', " روز قوست - Roz Ghost 2020 ")
	AddTextEntry('s500w222', " مرسيدس سي - Mercedes C500 ")
	AddTextEntry('wraithb', " روز راذ - Roz Wraith ")
	AddTextEntry('avalon', 'تويوتا افالون - Toyota Avalon')
	AddTextEntry('Camry11', 'تويوتا كامري - Toyota Camry')
	AddTextEntry('caprice13', 'كابرس - Caprice LS 2013')
	AddTextEntry('caprice17', 'كابرس - Caprice LS 2017')
	AddTextEntry('gcmaccent15', 'هونداي اكسينت - Hyundai Accent 2012')
	AddTextEntry('gcmpassat12', 'فولكس واجن - Volkswagen 2012')
	AddTextEntry('lex350', 'لكزس جي اس - Lexus GS 350')
	AddTextEntry('lex500', 'لكزس جي اس - Lexus GS 500')
	AddTextEntry('optima', 'كيا اوبتيما - Kia Optima 2015')
	AddTextEntry('soso18', 'هونداي سوناتا - Sonata Hyundai 2018')
	AddTextEntry('towncar91', 'تاون لنكون - Town Lingcoln 1995')
	AddTextEntry('towncar2010', 'تاون لنكون - Town Lingcoln 2010')
	AddTextEntry('20r1', 'يوماها r1 2020')
	AddTextEntry('aeroxr', 'يوماها ايروكسر - 2010')
	AddTextEntry('aeroxdrag', 'يوماها ايروكسر - دراق ريس')
	AddTextEntry('10ram', 'ونيت دودج رام 3500 2010')
	AddTextEntry('ddsn15', 'نيسان ددسن 2015')
	AddTextEntry('ford20120', 'فورد - f250 2020')
	AddTextEntry('gmc9080', 'جمس سيرا - GMC 1990')
	AddTextEntry('gmcat42', 'غمارتين 1500 2020 AT4 جمس سيرا')
	AddTextEntry('gmcr20120', 'وانيت  دينالي غمارتين - ابو رقبة')
	AddTextEntry('landv62', 'تيوتا لاند كروزر شاص')
	AddTextEntry('silv1560', 'شيفروليه سلفرادو - 2015')
	AddTextEntry('silv2080', 'وانيت شيفرولية سيلفرادو')
	AddTextEntry('silv5860', 'شيفروليه سلفرادو - 1958')
	AddTextEntry('silv6580', 'شيفروليه سلفرادو - 1965')
	AddTextEntry('silvr20120', 'وانيت سلفرادو غمارتين - ابورقبة')
	AddTextEntry('silvr20120', 'وانيت سلفرادو غمارتين - ابورقبة')
	AddTextEntry('hilux1', 'هايلوكس عمل - 2010')
	AddTextEntry('goldwing', 'هوندا قولد ونق - goldwing')
	AddTextEntry('hayabusa', 'هوندا هيابوزا - 2018')
	AddTextEntry('ninjah2', 'كواساكي نينجا h2r')
	AddTextEntry('rmz85cc', 'ساسوكي ريمز صغير')
	AddTextEntry('sanchez2', 'يوماها شنايزر - 2016 ')
	AddTextEntry('tmax', 'يوماها تي ماكس - 2019')
	AddTextEntry('towtruck3', 'ونش اف-150 - ميكانيك')
	AddTextEntry('towtruck4', 'ونش اف-150 - مرور')
	AddTextEntry('towtruck5', 'ونش فورد اف-250 - ميكانيك')
	AddTextEntry('towtruck6', 'ونش سكانيا - مطافي')
	AddTextEntry('towtruck7', 'ونش سكانيا - ميكانيك')
	AddTextEntry('towtruck8', 'ونش فورد اف-450 - ميكانيك')
	AddTextEntry('towtruck9', 'ونش ريكر - ميكانيك')
	AddTextEntry('towtruck10', 'ونش فورد اف-450 - ميكانيك 2')
	AddTextEntry('towtruck11', 'ونش فورد اف-450 - الرقابة')
	AddTextEntry('towtruck12', 'ونش شامل 1')
	AddTextEntry('towtruck13', 'ونش شامل 2')
	AddTextEntry('towtruck14', 'ونش فورد اف-450 - شرطة شامل')
	AddTextEntry('towtruck14', 'ونش فورد اف-450 - حراسة')
	AddTextEntry('13fmb302', "موستنق بوس - 2013")
	AddTextEntry('16challenger', "دوج تشالنجر - 2016")
	AddTextEntry('16charger2', "دوج تشارجر - SRT")
	AddTextEntry('16ss', " كمارو SS 2017")
	AddTextEntry('19dbs', " استون مارتن ")
	AddTextEntry('amggt63s', " مرسيدس amg 63 ")
	AddTextEntry('560sec87', "  مرسيدس كوبية - SEC 560 ")
	AddTextEntry('c7', "  كورفيت C7R ")
	AddTextEntry('cls63s', "  مرسيدس - s63 ")
	AddTextEntry('ctsv16', " كديلاك - 16 ")
	AddTextEntry('demon', "  دوج تشالنجر - DEMON ")
	AddTextEntry('dtd_c63s', "   مرسيدس خاص - s63  ")
	AddTextEntry('gcm992gt3', "  بورش - GT3 ")
	AddTextEntry('m3f80', " بي ام - M3 2015 ")
	AddTextEntry('mach1', " موستنق ماتش - 2018 ")
	AddTextEntry('mbc63', "مرسيدس - C63 AMG")
	AddTextEntry('mustang19', "موستنق خاص - GT")
	AddTextEntry('r820', "أودي R8")

	AddTextEntry('0x01F576A9', "Headlight cap")
	AddTextEntry('0x084DECDE', "Carbon 6.2 HEMI Hood")
	AddTextEntry('0x0ED99625', "Wing 3")
	AddTextEntry('0x1A5690F7', "Demon Hood /w engine")
	AddTextEntry('0x1BFB3068', "Demon Spoiler")
	AddTextEntry('0x3BB0D788', "Wing 5")
	AddTextEntry('0x3D63F794', "Liberty Walk Widebody")
	AddTextEntry('0x4D7C7742', "6.2 HEMI Hood")
	AddTextEntry('0x7B94F5ED', "'12 Front Bumper")
	AddTextEntry('0x8D5B1979', "R/T Front Bumper")
	AddTextEntry('0x24A9A595', "Hellcat Hood")
	AddTextEntry('0x26B4922C', "Sunroof")
	AddTextEntry('0x39C16D89', "Sideskirts")
	AddTextEntry('0x46B105D3', "Liberty Walk Spoiler")
	AddTextEntry('0x56D6A61E', "Painted SRT Spoiler")
	AddTextEntry('0x91E12285', "Dark grill R/T Front Bumper")
	AddTextEntry('0x99B12964', "Demon Badges")
	AddTextEntry('0x297B331D', "Wing 4")
	AddTextEntry('0x651C42A9', "Dark SRT Spoiler")
	AddTextEntry('0x905A16B2', "Hellcat Badges")
	AddTextEntry('0x33565F1E', "Wing 2")
	AddTextEntry('0x6474041D', "Challenger")
	AddTextEntry('0x43528371', "Demon Widebody")
	AddTextEntry('0xA1DEC280', "Hellcat Front Bumper")
	AddTextEntry('0xB023D8B7', "Wing 1")
	AddTextEntry('0xB48C67DB', "Dark grill SRT Front Bumper")
	AddTextEntry('0xBBE157E3', "Wing 6")
	AddTextEntry('0xC2947B26', "392 HEMI Badges")
	AddTextEntry('0xC6568B6F', "Demon Carbon Front Bumper")
	AddTextEntry('0xCD1F7A5F', "Wing 7")
	AddTextEntry('0xD0EE7E20', "Painted Shaker Hood")
	AddTextEntry('0xD26D7DA1', "Roof Wing")
	AddTextEntry('0xD3952EE3', "Wide Exhausts")
	AddTextEntry('0xD604084B', "Carbon Hood")
	AddTextEntry('0xE0A1A16B', "Wing 8")
	AddTextEntry('0xEA494D01', "SRT8 Spoiler")
	AddTextEntry('0xF2EB45FE', "Wing 9")
	AddTextEntry('0xF8D1EA12', "Dark stock Spoiler")
	AddTextEntry('0xF286C150', "Shaker Hood")
	AddTextEntry('0xFB8875D2', "Demon Front Bumper")
	AddTextEntry('0xFC3882B4', "Dodge")
end)
