<?xml version="1.0" encoding="UTF-8"?>
<!-- Redneck Modifications 08/22/2020 (Edited by: Technical Team) -->
<CVehicleMetadataMgr> 

  <ClipSetMaps> <!--ClipSetMaps-->
    
	<Item type="CClipSetMap"> <!--3310-->
      <Name>REDNECK_AMBO_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@common@car@ps</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap"> <!--3310-->
      <Name>ENTRY_REDNECK_RAMBULANCE_REAR_RIGHT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@prison_bus@rps1@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap"> <!--3310-->
      <Name>EXIT_REDNECK_RAMBULANCE_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@van@granger@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  
  </ClipSetMaps>

  <VehicleEntryPointInfos> <!--VehicleEntryPointInfos-->
    
	<Item type="CVehicleEntryPointInfo"> <!--3310-->
      <Name>ENTRY_POINT_RAMBULANCE_REAR_LEFT</Name>
      <DoorBoneName>door_pside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_r</DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VAN_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
  
  </VehicleEntryPointInfos>

  <VehicleEntryPointAnimInfos> <!--VehicleEntryPointAnimInfos-->
    
	<Item type="CVehicleEntryPointAnimInfo"> <!--3310-->
      <Name>ENTRY_POINT_ANIM_RAMBULANCE_REDNECK_REAR_RIGHT</Name>
      <CommonClipSetMap ref="REDNECK_AMBO_RIGHT" />
      <EntryClipSetMap ref="ENTRY_REDNECK_RAMBULANCE_REAR_RIGHT_1" />
      <ExitClipSetMap ref="EXIT_REDNECK_RAMBULANCE_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.000000" y="-1.555555" z="0.283000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForWaterEntry value="0.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition DontCloseDoorInside DontCloseDoorOutside NavigateToWarpEntryPoint</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
    </Item>
  
  </VehicleEntryPointAnimInfos>

  <VehicleLayoutInfos> <!--VehicleLayoutInfos-->
  
    <Item type="CVehicleLayoutInfo"> <!--3310-->
      <Name>LAYOUT_RAMBULANCE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_RAMBULANCE_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RAMBULANCE_REDNECK_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.006700" y="0.330000" z="0.270000" />
	  <MaxXAcceleration value="4.00000" />
	  <BodyLeanXApproachSpeed value="5.00000" />
	  <BodyLeanXSmallDelta value="0.30000" />
	  <FirstPersonAdditiveIdleClipSets>
      <Item>clipset@veh@van@ds@idle_a</Item>
      <Item>clipset@veh@van@ds@idle_b</Item>
      <Item>clipset@veh@van@ds@idle_c</Item>
      <Item>clipset@veh@van@ds@idle_d</Item>
      <Item>clipset@veh@van@ds@idle_e</Item>
	  </FirstPersonAdditiveIdleClipSets>
	  <FirstPersonRoadRageClipSets>
      <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
      <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
      <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
	  </FirstPersonRoadRageClipSets>
    </Item>
	<Item type="CVehicleLayoutInfo"> <!--3415-->
      <Name>LAYOUT_MCTANKER</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_truck</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
	  <MaxXAcceleration value="4.00000" />
	  <BodyLeanXApproachSpeed value="5.00000" />
	  <BodyLeanXSmallDelta value="0.30000" />
	  <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
		<Item>clipset@veh@van@ds@idle_b</Item>
		<Item>clipset@veh@van@ds@idle_c</Item>
		<Item>clipset@veh@van@ds@idle_d</Item>
		<Item>clipset@veh@van@ds@idle_e</Item>
	  </FirstPersonAdditiveIdleClipSets>
	  <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
		<Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
		<Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
	  </FirstPersonRoadRageClipSets>
    </Item>
	<Item type="CVehicleLayoutInfo"> <!--3415-->
      <Name>LAYOUT_MCTANKER3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_SANDKING_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_SANDKING_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_truck</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
	  <MaxXAcceleration value="4.00000" />
	  <BodyLeanXApproachSpeed value="5.00000" />
	  <BodyLeanXSmallDelta value="0.30000" />
	  <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
		<Item>clipset@veh@van@ds@idle_b</Item>
		<Item>clipset@veh@van@ds@idle_c</Item>
		<Item>clipset@veh@van@ds@idle_d</Item>
		<Item>clipset@veh@van@ds@idle_e</Item>
	  </FirstPersonAdditiveIdleClipSets>
	  <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
		<Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
		<Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
	  </FirstPersonRoadRageClipSets>
    </Item>
  
  </VehicleLayoutInfos>  

</CVehicleMetadataMgr>