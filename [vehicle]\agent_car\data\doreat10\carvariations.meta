<?xml version="1.0" encoding="UTF-8"?>
<!-- justRELAX1 / Most-Closer-to-reality  (01/08/2019) -->

<CVehicleModelInfoVariation>
    <variationData>
	
        <Item>
            <modelName>doreat10</modelName>
            <colors>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        0
                        0
                    </indices>
                    <liveries>
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        0
                        0
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        0
                        0
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        0
                        0
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        0
                        0
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
            </colors>
            <kits>
                <Item>0_default_modkit</Item>
            </kits>
            <windowsWithExposedEdges />
            <plateProbabilities>
                <Probabilities>
                    <Item>
                        <Name>police guv plate</Name>
                        <Value value="100" />
                    </Item>
                </Probabilities>
            </plateProbabilities>
            <lightSettings value="0" />
            <sirenSettings value="10084" />
        </Item>
    </variationData>
</CVehicleModelInfoVariation>