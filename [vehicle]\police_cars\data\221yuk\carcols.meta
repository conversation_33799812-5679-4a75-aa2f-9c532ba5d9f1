<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVarGlobal>    
	<Sirens>
    <Item>
      <id value="8347"/> 
      <name>221yuk</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="100.00000000"/>
      <lightFalloffExponent value="100.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="750"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="0"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="0"/>
      </rightTailLight>
      <leftHeadLightMultiples value="1"/>
      <rightHeadLightMultiples value="1"/>
      <leftTailLightMultiples value="1"/>
      <rightTailLightMultiples value="1"/>
      <useRealLights value="true"/>
      <sirens>
        <Item> <!--Siren 1-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819949216"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819949216"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 2-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="88123413"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="88123413"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFF00"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 3-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2770555027"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2770555027"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 4-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="304759445"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="304759445"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 5-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="341443211"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2462402897"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 6-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2769949860"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2769949860"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 7-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2769949860"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2769949860"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <Item> <!--Siren 8-->
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2462402897"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2462402897"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		<!-- siren9 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="304759445"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="304759445"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren10 -->
        <Item>
          <rotation>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2770555027"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2770555027"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren11 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2770555027"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2770555027"/> 
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren12 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11206827"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="1.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren13 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="4.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="1.57079633"/>
            <start value="0.00000000"/>
            <speed value="4.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="3.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren14 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="4.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness> 
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2769949860"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="2.00000000"/>
            <pull value="1.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren15 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="545526579"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="545526579"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren16 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1078462668"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1078462668"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren17 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="800.00000000"/>
            <sequencer value="3435973851"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="3.00000000"/>
            <pull value="3.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren18 -->
        <Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="800.00000000"/>
            <sequencer value="858993531"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="3.00000000"/>
            <pull value="3.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="1.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren19 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819949216"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819949216"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
		<!-- siren20 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="88123413"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="88123413"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="0.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
      </sirens>
    </Item>
  </Sirens>
</CVehicleModelInfoVarGlobal>