<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
	<residentTxd>vehshare</residentTxd>
	<residentAnims />
	<InitDatas>
	<Item>
			<modelName>sk4</modelName>
			<txdName>sk4</txdName>
			<handlingId>police</handlingId>
			<gameName>sk4</gameName>
			<vehicleMakeName>sk4</vehicleMakeName>
			<expressionDictName>null</expressionDictName>
			<expressionName>null</expressionName>
			<animConvRoofDictName>null</animConvRoofDictName>
			<animConvRoofName>null</animConvRoofName>
			<animConvRoofWindowsAffected/>
			<ptfxAssetName>null</ptfxAssetName>
			<audioNameHash>sheriff2</audioNameHash>
			<layout>LAYOUT_STD_HIGHWINDOW</layout>
			<coverBoundOffsets>BALLER_COVER_OFFSET_INFO</coverBoundOffsets>
			<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
			<scenarioLayout/>
			<cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
			<aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
			<bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
			<povCameraName>DEFAULT_POV_CAMERA</povCameraName>
			<FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000"/>
			<FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
			<FirstPersonProjectileDriveByIKOffset x="0.030000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.040000"/>
			<FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.520000"/>
			<FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
			<FirstPersonMobilePhoneSeatIKOffset>
				<Item>
					<Offset x="0.136000" y="0.136000" z="0.415000"/>
					<SeatIndex value="2"/>
				</Item>
				<Item>
					<Offset x="0.136000" y="0.136000" z="0.415000"/>
					<SeatIndex value="3"/>
				</Item>
			</FirstPersonMobilePhoneSeatIKOffset>
			<PovCameraOffset x="0.000000" y="-0.165000" z="0.645000"/>
			<PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
			<PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
			<PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.000000"/>
			<vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
			<shouldUseCinematicViewMode value="true"/>
			<shouldCameraTransitionOnClimbUpDown value="false"/>
			<shouldCameraIgnoreExiting value="false"/>
			<AllowPretendOccupants value="true"/>
			<AllowJoyriding value="true"/>
			<AllowSundayDriving value="true"/>
			<AllowBodyColorMapping value="true"/>
			<wheelScale value="0.336000"/>
			<wheelScaleRear value="0.336000"/>
			<dirtLevelMin value="0.000000"/>
			<dirtLevelMax value="0.550000"/>
			<envEffScaleMin value="0.000000"/>
			<envEffScaleMax value="1.000000"/>
			<envEffScaleMin2 value="0.000000"/>
			<envEffScaleMax2 value="1.000000"/>
			<damageMapScale value="0.300000"/>
			<damageOffsetScale value="1.000000"/>
			<diffuseTint value="0x00FFFFFF"/>
			<steerWheelMult value="1.000000"/>
			<HDTextureDist value="5.000000"/>
			<lodDistances content="float_array">
        500.000000	
        500.000000
        500.000000	
        500.000000	
        500.000000	
        500.000000
      </lodDistances>
			<minSeatHeight value="0.952"/>
			<identicalModelSpawnDistance value="20"/>
			<maxNumOfSameColor value="10"/>
			<defaultBodyHealth value="1000.000000"/>
			<pretendOccupantsScale value="1.000000"/>
			<visibleSpawnDistScale value="1.000000"/>
			<trackerPathWidth value="2.000000"/>
			<weaponForceMult value="1.000000"/>
			<frequency value="100"/>
			<swankness>SWANKNESS_3</swankness>
			<maxNum value="999"/>
		   <flags>FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_ATTACH_TRAILER_IN_CITY FLAG_HAS_LIVERY FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON</flags>
			<type>VEHICLE_TYPE_CAR</type>
			<plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
			<dashboardType>VDT_CAVALCADE</dashboardType>
			<vehicleClass>VC_EMERGENCY</vehicleClass>
			<wheelType>VWT_SUV</wheelType>
			<trailers/>
			<additionalTrailers/>
			<drivers/>
			<extraIncludes/>
			<doorsWithCollisionWhenClosed/>
			<driveableDoors/>
			<bumpersNeedToCollideWithMap value="false"/>
			<needsRopeTexture value="false"/>
			<requiredExtras/>
			<rewards/>
			<cinematicPartCamera>
				<Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
				<Item>WHEEL_FRONT_LEFT_CAMERA</Item>
				<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
				<Item>WHEEL_REAR_LEFT_CAMERA</Item>
			</cinematicPartCamera>
			<NmBraceOverrideSet>Truck</NmBraceOverrideSet>
			<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
			<buoyancySphereSizeScale value="1.000000"/>
			<pOverrideRagdollThreshold type="NULL"/>
			<firstPersonDrivebyData>
				<Item>STD_BUFFALO_FRONT_LEFT</Item>
				<Item>STD_BUFFALO_FRONT_RIGHT</Item>
				<Item>STD_BALLER_REAR_LEFT</Item>
				<Item>STD_BALLER_REAR_RIGHT</Item>
			</firstPersonDrivebyData>
		</Item>
	</InitDatas>
	<txdRelationships>
   
	</txdRelationships>
</CVehicleModelInfo__InitDataList>