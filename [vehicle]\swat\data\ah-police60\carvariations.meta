<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>ah-police60</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            14 
            12 
            18
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
          </liveries>
        </Item>
      </colors>
      <kits />
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
	<Item>
      <modelName>noosevalk</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            14 
            12 
            18
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
          </liveries>
        </Item>
      </colors>
      <kits />
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
	<Item>
      <modelName>usafvalk</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            14 
            12 
            18
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
          </liveries>
        </Item>
      </colors>
      <kits />
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
	<Item>
      <modelName>usarmyvalk</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            14 
            12 
            18
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
          </liveries>
        </Item>
      </colors>
      <kits />
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
	<Item>
      <modelName>usmvalk</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            14 
            12 
            18
            156 
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
            <Item value="true" />
            <Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
			<Item value="false" />
			<Item value="true" />
          </liveries>
        </Item>
      </colors>
      <kits />
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
  </variationData>
</CVehicleModelInfoVariation>