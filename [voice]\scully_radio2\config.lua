Scully = {
    Framework = 'es_extended', -- Server Core = scully_core, ESX = es_extended, QBCore = qb-core, Standalone = none
    RadioColour = 'default', -- Options are default, blue, brown, cherry, coral, cyan, green, mint, orange, pink, purple, red, white and yellow
    AllowColours = true, -- Disable if you don't want people to be able to change their radio colour in-game
    ColourCommand = 'rcolour', -- Set to '' if you don't want to allow changing with a command, command is /rcolour 1-11
    EnableList = false, -- Set to false to disable the player list
    HideListCommand = 'rlist', -- Set to '' if you don't want to allow hiding the list with a command, command is /rlist
    ShowSelf = true, -- Enable if you want your own name to be shown on the radio list
    EnableEditing = true, -- Disable if you don't want to allow players to change their names and callsigns on the radio
    MicClicks = true, -- Disable if you don't want mic clicks
    RadioAnims = true, -- Disable if you don't want to use radio animations for holding the radio
    UseItem = true, -- Enable if you want to use the radio as an item, will only work for QBCore and ESX
    UseItemColours = false, -- Enable if you want to use different items for each colour, this will disable the command also
    UseKeybind = 'Home', -- Set to '' if you don't want to use the radio as a keybind, can be changed here for first use only or in your fivem keybind settings
    UseCustomChannelNames = true, -- Enable if you want custom channel names to be displayed on the radio
    ChannelNames = { -- Channel names have a limit of 7 characters or they won't display
        [1] = 'ADMIN',
        [2] = 'ADMIN2',
        [3] = 'POLICE',
        [4] = 'POLICE2',
        [5] = 'POLICE-SANDY',
        [6] = 'POLICE-POLITO',
        [7] = 'AGENT DISBATH',
        [8] = 'AGENT DISBATH',
        [9] = 'AGENT OUDISBATHT',
        [10] = 'DECTOR LOS SANTOS',
        [11] = 'DECTOR SANDY ',
        [12] = 'DECTOR POLITO',
        [13] = 'MECHANIC1',
        [14] = 'MECHANIC2',
        [15] = 'MECHANIC3'
    },
    WhitelistedAccess = { -- What channels should be whitelisted and what jobs should have access to them?
        [1] = {--ADMIN
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true, -- Admins can always use the radio
            ['mechanic'] = true
        },
        [2] = {--POLICE
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [3] = {--POLICE SANDY POLITO
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [4] = {--AGENT DISBATH
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [5] = {--AGENT OUT port
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
         [6] = {--DECTOR LOS SANTOS
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [7] = {--DECTOR SANDY POLITO
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [8] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [9] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [10] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [11] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [12] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [13] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [14] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
        },
        [15] = {--MECHANIC
            ['agent'] = true,
            ['police'] = true,
            ['ambulance'] = true,
            ['admin'] = true,
            ['mechanic'] = true
}
    },
    AcePerms = { -- Not needed unless Framework is set to 'none'
        'police',
        'agent',
        'ambulance',
        'admin',
        'mechanic'
    }
}