#############################################################################################
#                               vMenu CONFIGURATION OPTIONS                                 #
#             For help, see https://docs.vespura.com/vmenu/configuration                    #
#############################################################################################

# WARNING, if you set "use_permissions" to false, a 'default' permissions system will be used.
# this makes sure that EVERYONE on the server can do EVERYTHING, besides, banning, unbanning, 
# kicking and killing using the Online Players menu.
setr vmenu_use_permissions true

# WARNING, if you enable the option below, then only players with the "vMenu.Staff"
# permission will be able to use vMenu. Not recommended.
setr vmenu_menu_staff_only false

# Disable setting stats like stamina, lung capacity, driving skills etc.
setr vmenu_disable_player_stats_setup false

# Key Mapping Stuff.
setr vmenu_menu_toggle_key "M" # https://docs.fivem.net/docs/game-references/input-mapper-parameter-ids/keyboard/
setr vmenu_noclip_toggle_key "F2" # https://docs.fivem.net/docs/game-references/input-mapper-parameter-ids/keyboard/
setr vmenu_keymapping_id "Default" # needs to be one word no spaces

# Keeps spawned vehicles from de-spawning if 'replace previous vehicle' is disabled.
setr vmenu_keep_spawned_vehicles_persistent false

# Automatically ban cheaters? This feature is not 100% reliable so that's why it's disabled 
# by default.
setr vmenu_auto_ban_cheaters false
setr vmenu_auto_ban_cheaters_ban_message "You have been automatically banned. If you believe this was done by error, please contact the server owner for support."

# Log ban actions to the "vmenu.log" file?
setr vmenu_log_ban_actions true

# Log kick actions to the "vmenu.log" file?
setr vmenu_log_kick_actions true

# This option will enable compatibility mode for els and other siren-control scripts by 
# disabling vMenu's control over vehicle sirens completely.
setr vmenu_use_els_compatibility_mode false

# This option will make the player invisible on tick if the option in player options is active making
#impossible for other scripts to alter invisible state
setr vmenu_handle_invisibility true

# When you set this to true, it will leave the current game session if a player uses the
# rockstar editor button in the recording options menu. (false by default).
setr vmenu_quit_session_in_rockstar_editor false

# Here you can set some info about your server which will be displayed in the "About Menu".
# The first one is just a small description message where you can tell the user about your community.
# Keep it short though, because the description box's size is limited.
# The second convar is for the url/domain name of your community, which will be displayed as a label on
# the right side of the info button. examples: https://www.vespura.com/hi/i/2018-12-09_23-43_9b003_349.png
setr vmenu_server_info_message "About this server, discord: vespura.com/discord"
setr vmenu_server_info_website_url "www.vespura.com"

# Set the keybind that will be used for the 'teleport to waypoint' keybind 
# Only used if enabled in the menu by the user, and if the user has permission
# to use the Teleport to Waypoint button in the Misc Settings submenu. This will
# only be active when the keyboard is being used. Controllers will not trigger this
# keybind unless the keyboard/mouse is being used at the same game frame.
setr vmenu_teleport_to_wp_keybind_key 168 # 168 / F7 by default

# If you set this to true, then players will not be able to spawn as their default
# saved multiplayer character whenever they join or respawn. This includes all staff members as well.
# This does NOT disable the "restore player appearance" option for non-mp peds.
setr vmenu_disable_spawning_as_default_character false

# Recommended to leave this disabled, but if you REALLY want to use animals as player peds then go ahead and enable this.
# You have been warned, this WILL cause game crashes to occur regularly.
setr vmenu_enable_animals_spawn_menu false

# Sets the PVP mode, 0 = do nothing, 1 = enable pvp (friendly fire) for everyone, 2 = disable pvp (friendly fire) for everyone.
# Note this is only set ONCE when the player joins. This is not forced every tick, other resources or client mods are able to override this.
# Default: 0
setr vmenu_pvp_mode 0

# Sets whether or not players can lose their head props when they are hit/pushed.
# true = Props will stay on player (default vMenu and GTA Online behavior)
# false = vMenu will not touch this feature, by default this means that head props will fall off when the player is hit, which is the default behavior for GTA V Single Player peds.
setr keep_player_head_props true

# Set this to true if you don't want vMenu to use any server information convars.
setr vmenu_disable_server_info_convars false

# Distance for playerblips to showup. This is using "game units" as measurement. It's unknown
# what this is in relation to meters or something similar, but 500.0 seems fine in most cases.
setr vmenu_player_names_distance 500.0

# Disables the entity model outlines, model hashes, entity handles development tools section.
setr vmenu_disable_entity_outlines_tool false

# This message gets added at the end of all ban messages, use this to show users where they can contact the server staff team in case
# they want to appeal it or if they have any questions.
setr vmenu_default_ban_message_information "Please contact the staff team by going to (support url) if you want to appeal this ban"

### Weather options ###
# Set this to false to disable weather sync from vMenu globally.
setr vmenu_enable_weather_sync true
# Set this to false to disable dynamic (automatic) weather changes.
setr vmenu_enable_dynamic_weather true
# The number of minutes between dynamic weather time changes. Value must be greater than 0.
setr vmenu_dynamic_weather_timer 15
# Sets the default weather type
setr vmenu_current_weather "clear"
# Whether or not artificial lights (blackout mode) is enabled by default.
setr vmenu_blackout_enabled false
# Weather change duration in whole seconds, default value is 30. Value must be between 0 and 45 (inclusive).
setr vmenu_weather_change_duration 30
# Enable manual snow effects, use this to enable the snow effects with any weather type.
# Can be overwritten in-game, this is just the default value used when starting the server.
# Combine with XMAS weather for best results.
setr vmenu_enable_snow false

### Time options ###
# Set this to false to disable time sync from vMenu globally.
setr vmenu_enable_time_sync true
# Set this to true if you want time to be frozen by default.
setr vmenu_freeze_time false
# This setting determines how long one in-game minute lasts in real time.
# By default, one GTA V minute, takes 2 seconds (2000 miliseconds).
# The value here is measured in miliseconds, and must be a positive number at least greater than 100.
setr vmenu_ingame_minute_duration 2000
# Set the default in-game hour when the server is started. Value must be between 0 and 23 (inclusive).
setr vmenu_current_hour 7
# Set the default in-game minute when the server is started. Value must be between 0 and 59 (inclusive).
setr vmenu_current_minute 0
# Setting this to true, will sync the in-game time to the system time of the server.
# Enabling this will prevent you from setting a custom time, freezing the time and changing the time duration.
setr vmenu_sync_to_machine_time false

### Vehicle options ###
# Setting this to true will enable the chameleon colour vehicle paints category in the primary colours menu.
# You must be streaming the chameleon colours in order for this to function properly.
setr vmenu_using_chameleon_colours false


#############################################################################################
#                                    vMenu PERMISSIONS                                      #
#             For help, see https://docs.vespura.com/vmenu/permissions-ref                  #
#############################################################################################


#####################################
#       PLAYERS / GROUPS SETUP      #
#####################################

# Add players to any group you want, can be group.admin, group.moderator or any other group.<name> you want.

# Admin group players:
add_principal identifier.steam:110000105959047 group.admin
add_principal identifier.license:4510587c13e0b645eb8d24bc104601792277ab98 group.admin


# Moderator group players:
add_principal identifier.steam:110000105959047 group.moderator
add_principal identifier.license:4510587c13e0b645eb8d24bc104601792277ab98 group.moderator




#############################################
#        SETTING UP GROUP INHERITANCE       #
#############################################
## Setup group inheritance, it's probably best you don't touch this unless you know what you're doing.
add_principal group.admin group.moderator




#########################################
#           PERMISSIONS SETUP           #
#########################################

####################################
#       GLOBAL PERMISSIONS         #
####################################
#add_ace builtin.everyone "vMenu.Everything" allow # (Don't touch this, unless you're stupid or you know what you're doing :^) !)

# Prevent admins from getting banned/kicked from the server.
add_ace group.admin "vMenu.DontKickMe" allow
add_ace group.admin "vMenu.DontBanMe" allow

# If the menu is set to menu_staff_only, then this will allow moderators and admins to use the menu.
add_ace group.moderator "vMenu.Staff" allow

# Allow anyone to use noclip.
add_ace builtin.everyone "vMenu.NoClip" allow

##############################
#    ONLINE PLAYERS MENU     #
##############################
add_ace builtin.everyone "vMenu.OnlinePlayers.Menu" allow
#add_ace builtin.everyone "vMenu.OnlinePlayers.All" allow
add_ace builtin.everyone "vMenu.OnlinePlayers.Teleport" allow
add_ace builtin.everyone "vMenu.OnlinePlayers.Waypoint" allow
add_ace builtin.everyone "vMenu.OnlinePlayers.Spectate" allow

# Moderators & admins only:
add_ace group.moderator "vMenu.OnlinePlayers.Summon" allow
add_ace group.moderator "vMenu.OnlinePlayers.Kill" allow
add_ace group.moderator "vMenu.OnlinePlayers.Kick" allow
# Tempban allows bans up to 30 days, which is enough for moderators.
# Mods can also see the banned players list but will not be able to unban players.
add_ace group.moderator "vMenu.OnlinePlayers.TempBan" allow
add_ace group.moderator "vMenu.OnlinePlayers.ViewBannedPlayers" allow

# Admins are allowed to ban players permanently and also unban players.
add_ace group.admin "vMenu.OnlinePlayers.Identifiers" allow
add_ace group.admin "vMenu.OnlinePlayers.PermBan" allow
add_ace group.admin "vMenu.OnlinePlayers.Unban" allow

# This is not allowed for anyone by default, but if you really want it enabled then 
# I suggest you only enable this for the server owner/head of your staff team.
# add_ace group.admin "vMenu.OnlinePlayers.SeePrivateMessages" allow

####################################
#       PLAYER OPTIONS MENU        #
####################################
add_ace builtin.everyone "vMenu.PlayerOptions.Menu" allow
add_ace builtin.everyone "vMenu.PlayerOptions.All" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.God" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.Invisible" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.UnlimitedStamina" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.FastRun" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.FastSwim" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.Superjump" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.NoRagdoll" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.NeverWanted" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.SetWanted" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.ClearBlood" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.SetBlood" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.Ignored" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.StayInVehicle" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.MaxHealth" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.MaxArmor" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.CleanPlayer" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.DryPlayer" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.WetPlayer" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.VehicleAutoPilotMenu" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.Freeze" allow
#add_ace builtin.everyone "vMenu.PlayerOptions.Scenarios" allow

####################################
#       VEHICLE OPTIONS MENU       #
####################################
add_ace builtin.everyone "vMenu.VehicleOptions.Menu" allow
add_ace builtin.everyone "vMenu.VehicleOptions.All" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.God" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.KeepClean" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Repair" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Wash" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Engine" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.DestroyEngine" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.BikeSeatbelt" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.SpeedLimiter" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.ChangePlate" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Mod" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Colors" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Liveries" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Components" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Doors" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Windows" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Freeze" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Invisible" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.TorqueMultiplier" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.PowerMultiplier" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Flip" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Alarm" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.CycleSeats" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.EngineAlwaysOn" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.NoSiren" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.NoHelmet" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Lights" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.FixOrDestroyTires" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Delete" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.Underglow" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.FlashHighbeamsOnHonk" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.DisableTurbulence" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.AnchorBoat" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.InfiniteFuel" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.VOFlares" allow
#add_ace builtin.everyone "vMenu.VehicleOptions.VOPlaneBombs" allow

####################################
#       VEHICLE SPAWNER MENU       #
####################################
add_ace builtin.everyone "vMenu.VehicleSpawner.Menu" allow
add_ace builtin.everyone "vMenu.VehicleSpawner.All" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.DisableReplacePrevious" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.SpawnByName" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Addon" allow # allows you to spawn an addon car from the Addon Vehicles list.
#add_ace builtin.everyone "vMenu.VehicleSpawner.Compacts" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Sedans" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.SUVs" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Coupes" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Muscle" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.SportsClassic" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Sports" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Super" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Motorcycles" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.OffRoad" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Industrial" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Utility" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Vans" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Cycles" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Boats" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Helicopters" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Planes" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Service" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Emergency" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Military" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Commercial" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.Trains" allow
#add_ace builtin.everyone "vMenu.VehicleSpawner.OpenWheel" allow

####################################
#       SAVED VEHICLES MENU        #
####################################
add_ace builtin.everyone "vMenu.SavedVehicles.Menu" allow
add_ace builtin.everyone "vMenu.SavedVehicles.All" allow
#add_ace builtin.everyone "vMenu.SavedVehicles.Spawn" allow

####################################
#      PERSONAL VEHICLE MENU       #
####################################
add_ace builtin.everyone "vMenu.PersonalVehicle.Menu" allow
add_ace builtin.everyone "vMenu.PersonalVehicle.All" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.ToggleEngine" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.ToggleLights" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.ToggleStance" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.KickPassengers" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.LockDoors" allow # This grants both locking and unlocking the doors.
#add_ace builtin.everyone "vMenu.PersonalVehicle.Doors" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.SoundHorn" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.ToggleAlarm" allow
#add_ace builtin.everyone "vMenu.PersonalVehicle.AddBlip" allow # Adds a blip for your personal vehicle only.
#add_ace builtin.everyone "vMenu.PersonalVehicle.ExclusiveDriver" allow # Allows you to become the exclusive driver of this vehicle.

####################################
#     PLAYER APPEARANCE MENU       #
####################################
add_ace builtin.everyone "vMenu.PlayerAppearance.Menu" allow
add_ace builtin.everyone "vMenu.PlayerAppearance.All" allow
#add_ace builtin.everyone "vMenu.PlayerAppearance.Customize" allow
#add_ace builtin.everyone "vMenu.PlayerAppearance.SpawnSaved" allow
#add_ace builtin.everyone "vMenu.PlayerAppearance.SpawnNew" allow
#add_ace builtin.everyone "vMenu.PlayerAppearance.AddonPeds" allow

####################################
#        TIME OPTIONS MENU         #
####################################
# Restricted to moderators/admins by default.
add_ace group.moderator "vMenu.TimeOptions.Menu" allow
add_ace group.moderator "vMenu.TimeOptions.All" allow
#add_ace group.moderator "vMenu.TimeOptions.FreezeTime" allow
#add_ace group.moderator "vMenu.TimeOptions.SetTime" allow

####################################
#      WEATHER OPTIONS MENU        #
####################################
# Also restricted to moderators/admins by default.
add_ace group.moderator "vMenu.WeatherOptions.Menu" allow
add_ace group.moderator "vMenu.WeatherOptions.All" allow
#add_ace group.moderator "vMenu.WeatherOptions.Dynamic" allow
#add_ace group.moderator "vMenu.WeatherOptions.Blackout" allow
#add_ace group.moderator "vMenu.WeatherOptions.SetWeather" allow
#add_ace group.moderator "vMenu.WeatherOptions.RemoveClouds" allow
#add_ace group.moderator "vMenu.WeatherOptions.RandomizeClouds" allow

####################################
#       WEAPON OPTIONS MENU        #
####################################
add_ace builtin.everyone "vMenu.WeaponOptions.Menu" allow
add_ace builtin.everyone "vMenu.WeaponOptions.All" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.GetAll" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.RemoveAll" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.UnlimitedAmmo" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.NoReload" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.Spawn" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.SpawnByName" allow
#add_ace builtin.everyone "vMenu.WeaponOptions.SetAllAmmo" allow

###     WEAPON-SPECIFIC PERMISSIONS
# If you remove the "vMenu.WeaponOptions.All" permission, you can specify specific
# weapons that players can access using the weapon options menu by granting permissions
# for every weapon that you want to allow below. "vMenu.WeaponOptions.All" automatically grants all weapons.

# add_ace builtin.everyone "vMenu.WeaponOptions.APPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.AdvancedRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.AssaultRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.AssaultRifleMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.AssaultSMG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.AssaultShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.BZGas" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Ball" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Bat" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.BattleAxe" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Bottle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.BullpupRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.BullpupRifleMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.BullpupShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CarbineRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CarbineRifleMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CombatMG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CombatMGMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CombatPDW" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CombatPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CompactGrenadeLauncher" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CompactRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Crowbar" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Dagger" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.DoubleAction" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.DoubleBarrelShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.FireExtinguisher" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Firework" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Flare" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.FlareGun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Flashlight" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.GolfClub" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Grenade" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.GrenadeLauncher" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.GrenadeLauncherSmoke" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Gusenberg" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Hammer" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Hatchet" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.HeavyPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.HeavyShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.HeavySniper" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.HeavySniperMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.HomingLauncher" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Knife" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.KnuckleDuster" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Machete" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MachinePistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MarksmanPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MarksmanRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MarksmanRifleMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MicroSMG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.MiniSMG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Minigun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Molotov" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Musket" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.NightVision" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Nightstick" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Parachute" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PetrolCan" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PipeBomb" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Pistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Pistol50" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PistolMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PoolCue" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.ProximityMine" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PumpShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PumpShotgunMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.RPG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Railgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Revolver" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.RevolverMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SMG" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SMGMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SNSPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SNSPistolMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SawnOffShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SmokeGrenade" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SniperRifle" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Snowball" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SpecialCarbine" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SpecialCarbineMk2" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.StickyBomb" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.StunGun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SweeperShotgun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.SwitchBlade" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Unarmed" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.VintagePistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.Wrench" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PlasmaPistol" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PlasmaCarbine" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.PlasmaMinigun" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.StoneHatchet" allow
# add_ace builtin.everyone "vMenu.WeaponOptions.CeramicPistol" allow # mpheist3 dlc (1868)
# add_ace builtin.everyone "vMenu.WeaponOptions.NavyRevolver" allow # mpheist3 dlc (1868)
# add_ace builtin.everyone "vMenu.WeaponOptions.HazardCan" allow # mpheist3 dlc (1868)
# add_ace builtin.everyone "vMenu.WeaponOptions.PericoPistol" allow # mpheist4 dlc (2189)
# add_ace builtin.everyone "vMenu.WeaponOptions.MilitaryRifle" allow # mpheist4 dlc (2189)
# add_ace builtin.everyone "vMenu.WeaponOptions.CombatShotgun" allow # mpheist4 dlc (2189)
# add_ace builtin.everyone "vMenu.WeaponOptions.EMPLauncher" allow # mpsecurity dlc (2545)
# add_ace builtin.everyone "vMenu.WeaponOptions.HeavyRifle" allow # mpsecurity dlc (2545)
# add_ace builtin.everyone "vMenu.WeaponOptions.FertilizerCan" allow # mpsecurity dlc (2545)
# add_ace builtin.everyone "vMenu.WeaponOptions.StunGunMP" allow # mpsecurity dlc (2545)
# add_ace builtin.everyone "vMenu.WeaponOptions.PrecisionRifle" allow # mpsum2 dlc (2699)
# add_ace builtin.everyone "vMenu.WeaponOptions.TacticalRifle" allow # mpsum2 dlc (2699)
# add_ace builtin.everyone "vMenu.WeaponOptions.PistolXM3" allow # mpchristmas3 dlc (2802)
# add_ace builtin.everyone "vMenu.WeaponOptions.CandyCane" allow # mpchristmas3 dlc (2802)
# add_ace builtin.everyone "vMenu.WeaponOptions.RailgunXM3" allow # mpchristmas3 dlc (2802)
# add_ace builtin.everyone "vMenu.WeaponOptions.AcidPackage" allow # mpchristmas3 dlc (2802)
# add_ace builtin.everyone "vMenu.WeaponOptions.TecPistol" allow # mp2023_01 dlc (2944)
# add_ace builtin.everyone "vMenu.WeaponOptions.BattleRifle" allow # mp2023_02 dlc (3095)
# add_ace builtin.everyone "vMenu.WeaponOptions.SnowLauncher" allow # mp2023_02 dlc (3095)
# add_ace builtin.everyone "vMenu.WeaponOptions.HackingDevice" allow # mp2023_02 dlc (3095)
# add_ace builtin.everyone "vMenu.WeaponOptions.StunRod" allow # mp2024_01 dlc (3258)

####################################
#       WEAPON LOADOUTS MENU       #
####################################
add_ace builtin.everyone "vMenu.WeaponLoadouts.Menu" allow
add_ace builtin.everyone "vMenu.WeaponLoadouts.All" allow
# add_ace builtin.everyone "vMenu.WeaponLoadouts.Equip" allow
# add_ace builtin.everyone "vMenu.WeaponLoadouts.EquipOnRespawn" allow

####################################
#        MISC SETTINGS MENU        #
####################################
# There is no vMenu.MiscSettings.Menu permission on purpose, some options in the misc settings menu
# should *ALWAYS* be allowed, so you can't restrict the opening of this menu!
#add_ace builtin.everyone "vMenu.MiscSettings.All" allow
add_ace builtin.everyone "vMenu.MiscSettings.ClearArea" allow
add_ace builtin.everyone "vMenu.MiscSettings.TeleportToWp" allow
add_ace builtin.everyone "vMenu.MiscSettings.TeleportToCoord" allow
add_ace builtin.everyone "vMenu.MiscSettings.ShowCoordinates" allow
add_ace builtin.everyone "vMenu.MiscSettings.ShowLocation" allow
add_ace builtin.everyone "vMenu.MiscSettings.JoinQuitNotifs" allow
add_ace builtin.everyone "vMenu.MiscSettings.DeathNotifs" allow
add_ace builtin.everyone "vMenu.MiscSettings.NightVision" allow
add_ace builtin.everyone "vMenu.MiscSettings.ThermalVision" allow
add_ace builtin.everyone "vMenu.MiscSettings.LocationBlips" allow
add_ace builtin.everyone "vMenu.MiscSettings.OverheadNames" allow
add_ace builtin.everyone "vMenu.MiscSettings.PlayerBlips" allow
add_ace builtin.everyone "vMenu.MiscSettings.TeleportLocations" allow
add_ace group.moderator "vMenu.MiscSettings.TeleportSaveLocation" allow # Only allowed for moderators by default
add_ace builtin.everyone "vMenu.MiscSettings.ConnectionMenu" allow
add_ace builtin.everyone "vMenu.MiscSettings.RestoreAppearance" allow
add_ace builtin.everyone "vMenu.MiscSettings.RestoreWeapons" allow
add_ace builtin.everyone "vMenu.MiscSettings.DriftMode" allow
add_ace group.moderator "vMenu.MiscSettings.EntitySpawner" allow # Probably not the best idea to give this feature for everyone
add_ace group.developer "vMenu.MiscSettings.DevTools" allow # Probably not the best idea to give this feature for everyone

####################################
#     VOICE CHAT OPTIONS MENU      #
####################################
# To disable vMenu's voice chat options, simply remove this section completely and vMenu won't touch voice chat at all.
add_ace builtin.everyone "vMenu.VoiceChat.Menu" allow
#add_ace builtin.everyone "vMenu.VoiceChat.All" allow
add_ace builtin.everyone "vMenu.VoiceChat.Enable" allow
add_ace builtin.everyone "vMenu.VoiceChat.ShowSpeaker" allow
setr vmenu_override_voicechat_default_range 0.0 # range in meters, 0.0 is user default


# Staff voice channel is restricted to moderators/admins by default.
add_ace group.moderator "vMenu.VoiceChat.StaffChannel" allow
